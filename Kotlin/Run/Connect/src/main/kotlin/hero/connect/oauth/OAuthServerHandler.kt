package hero.connect.oauth

import hero.exceptions.http.ForbiddenException
import hero.http4k.extensions.body
import hero.http4k.extensions.lens
import hero.model.OAuthScopes
import hero.sql.jooq.Tables
import org.http4k.contract.ContractRoute
import org.http4k.contract.Tag
import org.http4k.contract.meta
import org.http4k.core.Body
import org.http4k.core.Method.GET
import org.http4k.core.Method.POST
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.then
import org.http4k.format.Jackson
import org.http4k.lens.FormField
import org.http4k.lens.Validator
import org.http4k.lens.boolean
import org.http4k.lens.webForm
import org.http4k.security.oauth.server.AccessTokens
import org.http4k.security.oauth.server.AuthRequestTracking
import org.http4k.security.oauth.server.AuthoriseRequestValidator
import org.http4k.security.oauth.server.AuthorizationCodes
import org.http4k.security.oauth.server.OAuthServer
import org.http4k.security.oauth.server.accesstoken.AccessTokenRequestAuthentication
import org.jooq.DSLContext
import java.time.Clock
import java.util.UUID

fun authorizationServer(
    requestValidator: AuthoriseRequestValidator,
    authRequestTracking: AuthRequestTracking,
    authorizationCodes: AuthorizationCodes,
    accessTokenRequestAuthentication: AccessTokenRequestAuthentication,
    accessTokens: AccessTokens,
    frontendOAuthURL: String,
    clock: Clock = Clock.systemUTC(),
): List<ContractRoute> {
    val server = OAuthServer(
        tokenPath = "/oauth2/token",
        authRequestTracking = authRequestTracking,
        authoriseRequestValidator = requestValidator,
        accessTokenRequestAuthentication = accessTokenRequestAuthentication,
        authorizationCodes = authorizationCodes,
        accessTokens = accessTokens,
        json = Jackson,
        clock = clock,
        documentationUri = "See the full API docs at https://example.com/docs/access_token",
    )

    return listOf(
        "/oauth2/token" meta {
            summary = """
                The token endpoint is responsible for exchanging an authorization code (or refresh token) for an access token.
                It verifies the client credentials and ensures the request is valid before issuing a new access token (and optionally a refresh token).
            """.trimIndent()
            tags += Tag("oauth")
        } bindContract POST to server.tokenRoute,
        "/authorize" meta {
            summary = """
                Redirects the user to the login page for authentication and consent before proceeding with authorization.
            """.trimIndent()
            tags += Tag("oauth")
        } bindContract GET to server.authenticationStart.then {
            Response(Status.FOUND)
                .header("Location", frontendOAuthURL)
        },
        "/authorize" meta {
            summary = """
                Processes the authenticated user's consent and issues an authorization code if approved.
            """.trimIndent()
            tags += Tag("oauth")
            receiving(authorizeFormBody)
        } bindContract POST to server.authenticationComplete,
    )
}

fun authorizationDetailsController(
    lazyContext: Lazy<DSLContext>,
    authRequestTracking: AuthRequestTracking,
) = "/authorize/details" meta {
    summary = """
        Returns metadata about the ongoing OAuth authorization request, such as client information, requested scopes,
        and redirect URI. It is used by the frontend to display consent-related information before the user accepts or denies access.
    """.trimIndent()
    tags += Tag("oauth")
    returning(
        Status.OK,
        lens<AuthorizeDetailsResponse>() to AuthorizeDetailsResponse(
            clientName = "client-name",
            scopes = listOf(OAuthScopes.SUBSCRIPTION_READ),
        ),
    )
} bindContract GET to { req ->
    val authRequest = authRequestTracking.resolveAuthRequest(req) ?: throw ForbiddenException()

    val context = lazyContext.value

    val oAuthClient = context
        .selectFrom(Tables.OAUTH_CLIENT)
        .where(Tables.OAUTH_CLIENT.ID.eq(UUID.fromString(authRequest.client.value)))
        .fetchSingle()

    val scopes = authRequest.scopes.map {
        when (it) {
            OAuthScopes.SUBSCRIPTION_READ.value -> OAuthScopes.SUBSCRIPTION_READ
            else -> error("Unmapped scope $it")
        }
    }

    Response(Status.OK)
        .body(
            AuthorizeDetailsResponse(
                clientName = oAuthClient.name,
                scopes = scopes,
            ),
        )
}

data class AuthorizeDetailsResponse(
    val clientName: String,
    val scopes: List<OAuthScopes>,
)

val userRejectedFormField = FormField.boolean().defaulted("userRejected", false)
val authorizeFormBody = Body.webForm(Validator.Ignore, userRejectedFormField).toLens()
