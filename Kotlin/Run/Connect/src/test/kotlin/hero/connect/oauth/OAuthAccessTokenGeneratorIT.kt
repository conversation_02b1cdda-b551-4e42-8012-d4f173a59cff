package hero.connect.oauth

import dev.forkhandles.result4k.failureOrNull
import dev.forkhandles.result4k.valueOrNull
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.time.TestClock
import io.jsonwebtoken.security.Keys
import org.assertj.core.api.Assertions.assertThat
import org.http4k.core.Uri
import org.http4k.security.oauth.server.AuthorizationCode
import org.http4k.security.oauth.server.AuthorizationCodeAlreadyUsed
import org.http4k.security.oauth.server.ClientId
import org.http4k.security.oauth.server.ClientIdMismatch
import org.http4k.security.oauth.server.InvalidRequest
import org.http4k.security.oauth.server.accesstoken.AuthorizationCodeAccessTokenRequest
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.Random
import java.util.UUID

class OAuthAccessTokenGeneratorIT : IntegrationTest() {
    @Test
    fun `should fail if authorization code does not exist`() {
        val underTest = OAuthAccessTokenGenerator(lazyTestContext, Random(0), secretKey)

        val clientId = ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe")
        val result = underTest.create(clientId, request(clientId, authorizationCode = "non-existent"))

        assertThat(result.failureOrNull()).isInstanceOf(InvalidRequest::class.java)
    }

    @Test
    fun `should fail if authorization code was used`() {
        val underTest = OAuthAccessTokenGenerator(lazyTestContext, Random(0), secretKey)

        val clientId = ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe")
        testHelper.createOAuthClient(id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"))

        testHelper.createUser("honza")
        testHelper.createOAuthAuthorizationCode(clientId.value, "honza", id = "ABCDF", usedAt = Instant.now())

        val result = underTest.create(clientId, request(clientId, authorizationCode = "ABCDF"))

        assertThat(result.failureOrNull()).isEqualTo(AuthorizationCodeAlreadyUsed)
    }

    @Test
    fun `should fail if client ids do not match`() {
        val underTest = OAuthAccessTokenGenerator(lazyTestContext, Random(0), secretKey)

        val differentClientId = ClientId("e209f0f4-8462-45d2-8275-61f864001d86")
        val clientId = ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe")
        testHelper.createOAuthClient(id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"))

        testHelper.createUser("honza")
        testHelper.createOAuthAuthorizationCode(clientId.value, "honza", id = "ABCDF")

        val result = underTest.create(differentClientId, request(differentClientId, authorizationCode = "ABCDF"))

        assertThat(result.failureOrNull()).isEqualTo(ClientIdMismatch)
    }

    @Test
    fun `should invalidate the authorization code and create refresh token`() {
        val now = Instant.ofEpochSecond(1742910188)
        val testClock = TestClock(now)
        val underTest = OAuthAccessTokenGenerator(lazyTestContext, Random(0), secretKey, testClock)

        val clientId = ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe")
        testHelper.createUser("cestmir")
        testHelper.createOAuthClient(id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"), userId = "cestmir")

        testHelper.createUser("honza")
        testHelper.createOAuthAuthorizationCode(clientId.value, "honza", id = "ABCDF", createdAt = now)

        val result = underTest.create(clientId, request(clientId, authorizationCode = "ABCDF"))

        val accessToken = result.valueOrNull()
        assertThat(accessToken).isNotNull()
        assertThat(accessToken?.value).isEqualTo(
            // {
            //  "sub": "honza",
            //  "exp": 1745502188,
            //  "iat": 1742910188,
            //  "type": "ACCESS",
            //  "scopes": [],
            //  "creatorId": "cestmir",
            //  "clientId": "042842bf-ecb8-44d3-ba61-c400c7d7e2fe",
            //  "iss": "herohero"
            // }
            """
            eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJob256YSIsImV4cCI6MTc0NTUwMjE4OCwiaWF0IjoxNzQyOTEwMTg4LCJ0eXBlIjoiQUNDRVNTIiwic2NvcGVzIjpbXSwiY3JlYXRvcklkIjoiY2VzdG1pciIsImNsaWVudElkIjoiMDQyODQyYmYtZWNiOC00NGQzLWJhNjEtYzQwMGM3ZDdlMmZlIiwiaXNzIjoiaGVyb2hlcm8ifQ.5we9eYQfe-pWAM5E088rs-Mj8f-yeZFjvxDerzqz7R6kzYwZTfZMrFRIuRGZakwf_3X60GZAjf2ButFscBuY3w
            """.trimIndent(),
        )

        val refreshToken = testContext
            .selectFrom(Tables.OAUTH_REFRESH_TOKEN)
            .fetchSingle()
        assertThat(accessToken?.refreshToken?.value).isEqualTo(refreshToken.id)
    }
}

private fun request(
    clientId: ClientId = ClientId("client-id"),
    secret: String = "secret",
    redirectUri: String = "redirect-uri",
    authorizationCode: String = "authorization_code",
) = AuthorizationCodeAccessTokenRequest(
    clientId,
    secret,
    Uri.of(redirectUri),
    listOf(),
    AuthorizationCode(authorizationCode),
)

private val secretKey = "5d192ef295f94e738479ab9f1952cd1bc1ae879697a344c9a62aabe73d0ddb47"
    .encodeToByteArray()
    .let {
        Keys.hmacShaKeyFor(it)
    }
