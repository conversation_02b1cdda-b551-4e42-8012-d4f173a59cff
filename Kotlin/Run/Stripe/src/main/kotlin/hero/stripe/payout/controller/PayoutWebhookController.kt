package hero.stripe.payout.controller

import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ServerException
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import hero.stripe.payout.service.PayoutCommandService
import hero.stripe.payout.service.ProcessPayout
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class PayoutWebhookController(
    private val payoutCommandService: PayoutCommandService,
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
) {
    @Suppress("unused")
    val routeStripeWebhookPayouts: ContractRoute =
        "/v1/webhooks/payouts".post(
            summary = "Handle Stripe payout events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.payouts)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for payouts: ${body.toJson()}")
                val payload = body.eventData?.payload
                    ?: throw BadRequestException("Stripe event payload was null.")
                if (payload.objectType != "payout") {
                    throw BadRequestException("Stripe event was not for `payout`: ${payload.objectType}")
                }
                val payoutId = payload.objectId
                    ?: throw BadRequestException("Field objectId was not given.")

                try {
                    payoutCommandService.execute(
                        ProcessPayout(
                            accountId = body.account!!,
                            payoutId = payoutId,
                            currency = currency,
                            overwriteInvoice = false,
                            sendEmail = true,
                        ),
                    )
                } catch (e: Exception) {
                    throw ServerException(
                        message = "Cannot process https://dashboard.stripe.com/${body.account}/payouts/$payoutId:" +
                            " ${e.message}",
                        cause = e,
                    )
                }

                Response(Status.NO_CONTENT)
            },
        )
}
