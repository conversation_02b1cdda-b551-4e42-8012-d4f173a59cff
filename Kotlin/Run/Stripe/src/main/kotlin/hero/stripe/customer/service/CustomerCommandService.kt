package hero.stripe.customer.service

import hero.baseutils.log
import hero.model.Currency
import hero.sql.jooq.tables.Customer.CUSTOMER
import hero.stripe.service.StripeService
import org.jooq.DSLContext
import java.time.Instant

class CustomerCommandService(
    private val stripeService: StripeService,
    private val production: <PERSON><PERSON>an,
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    fun execute(payload: ProcessCustomer) {
        log.info("Processing customer ${payload.customerId}", mapOf("customerId" to payload.customerId))
        val customer = stripeService.customer(payload.customerId, payload.currency)
        if (customer.deleted == true) {
            log.info("Marking customer ${payload.customerId} as deleted, if it's not marked yet")
            val rowsUpdated = context
                .update(CUSTOMER)
                .set(CUSTOMER.DELETED_AT, Instant.now())
                .where(CUSTOMER.STRIPE_ID.eq(payload.customerId).and(CUSTOMER.DELETED_AT.isNull))
                .execute()

            log.info("Marked customer ${payload.customerId} as deleted, updated $rowsUpdated rows")
            return
        }
        if (customer.description == null) {
            val description = "Customer ${customer.id} is missing user id in description field"
            if (production) {
                log.fatal(description)
            } else {
                log.warn("$description, skipping")
            }
            return
        }

        val currency = customer.metadata["currency"] ?: customer.currency?.uppercase()
            ?: error("Customer ${customer.id} is missing currency.")

        val defaultPaymentMethod = customer.invoiceSettings.defaultPaymentMethod

        context
            .insertInto(CUSTOMER)
            .set(CUSTOMER.STRIPE_ID, customer.id)
            .set(CUSTOMER.USER_ID, customer.description)
            .set(CUSTOMER.CREATED_AT, Instant.ofEpochSecond(customer.created))
            .set(CUSTOMER.CURRENCY, currency)
            .set(CUSTOMER.NAME, customer.name)
            .set(CUSTOMER.EMAIL, customer.email)
            .set(CUSTOMER.DEFAULT_PAYMENT_METHOD, defaultPaymentMethod)
            .onDuplicateKeyUpdate()
            .set(CUSTOMER.USER_ID, customer.description)
            .set(CUSTOMER.CURRENCY, currency)
            .set(CUSTOMER.NAME, customer.name)
            .set(CUSTOMER.EMAIL, customer.email)
            .set(CUSTOMER.DEFAULT_PAYMENT_METHOD, defaultPaymentMethod)
            .execute()
        log.info("Done processing customer ${payload.customerId}", mapOf("customerId" to payload.customerId))
    }
}

data class ProcessCustomer(
    val customerId: String,
    val currency: Currency,
)
