package hero.stripe.paymentmethod.service

import hero.baseutils.log
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.model.Currency
import hero.model.User
import hero.model.topics.CardCreateType.APPLE_PAY
import hero.model.topics.CardCreateType.GOOGLE_PAY
import hero.model.topics.CustomerPaymentMethodsUpdated
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService

class PaymentMethodCommandService(
    private val usersCollection: TypedCollectionReference<User>,
    private val pubSub: PubSub,
    private val stripeService: StripeService,
    private val stripePaymentMethods: StripePaymentMethodsService,
) {
    fun execute(command: ProcessPaymentMethod) {
        log.info(
            "Processing received payment method ${command.paymentMethodId} for customer ${command.customerId}",
            mapOf("paymentMethodId" to command.paymentMethodId, "customerId" to command.customerId),
        )

        stripePaymentMethods.shiftMetadataFromSetupIntentToPaymentMethod(
            command.customerId,
            command.paymentMethodId,
            command.currency,
        )
        pubSub.publish(
            CustomerPaymentMethodsUpdated(
                customerId = command.customerId,
                paymentMethodId = command.paymentMethodId,
                currency = command.currency,
            ),
        )

        val customer = stripeService.customer(command.customerId, command.currency)
        // description is currently "abused" for customer's `userId`
        if (customer.description != null) {
            // https://linear.app/herohero/issue/HH-1902
            // adding a new payment method will remove the UI warning about failed payments
            usersCollection[customer.description].field(User::lastChargeFailedAt).update(null)
        }

        // we check only non-apple/google pay to be fraudulent
        val paymentMethods = stripePaymentMethods.paymentMethods(command.customerId, command.currency)
            .filter { (it.metadata["cardCreateType"] ?: "") !in setOf(APPLE_PAY.name, GOOGLE_PAY.name) }
        if (paymentMethods.map { it.card.last4 }.distinct().count() > 4 ||
            paymentMethods.map { it.card.country }.distinct().count() > 1
        ) {
            stripeService.fraudWarning(
                command.customerId,
                command.currency,
                "Too many payment non-apple/google pay methods.",
            )
        }

        log.info(
            "Done processing received payment method ${command.paymentMethodId} for customer ${command.customerId}",
            mapOf("paymentMethodId" to command.paymentMethodId, "customerId" to command.customerId),
        )
    }
}

data class ProcessPaymentMethod(
    val customerId: String,
    val paymentMethodId: String,
    val currency: Currency,
)
