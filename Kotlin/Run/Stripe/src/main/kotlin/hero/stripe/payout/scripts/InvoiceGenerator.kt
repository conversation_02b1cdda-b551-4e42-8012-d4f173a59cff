package hero.scripts

import com.stripe.model.Account
import com.stripe.param.PayoutListParams
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Invoice
import hero.model.Tier
import hero.model.User
import hero.stripe.payout.service.PayoutCommandService
import hero.stripe.payout.service.ProcessPayout
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.VatMappingProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime
import kotlin.system.exitProcess

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val hostnameServices = "https://${if (production) "svc-prod" else "svc-devel"}.herohero.co"
    val pubSub = PubSub(production.envPrefix, SystemEnv.cloudProject)
    val stripeClients = StripeClients(systemEnv("STRIPE_API_KEY_EU_PROD"), systemEnv("STRIPE_API_KEY_EU_PROD"))
    val stripe = StripeService(
        clients = stripeClients,
        pubSub = null,
    )
    val invoicesCollection = firestore.typedCollectionOf(Invoice)
    val payoutCommandService = PayoutCommandService(
        invoicesCollection = invoicesCollection,
        hostnameServices = hostnameServices,
        pubSub = pubSub,
        stripe = stripe,
        tiersCollection = firestore.typedCollectionOf(Tier),
        usersCollection = firestore.typedCollectionOf(User),
        countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping(),
    )

    val usersCollection = firestore.typedCollectionOf(User)

    val timestampLimit = "2020-01-01"
    val currency = Currency.EUR
    val overwriteInvoice = true
    val sendEmail = false
    // see https://linear.app/herohero/issue/HH-3158/silently-ignoring-other-adjustments-if-needed
    val skipOtherAdjustments = false
    var accountIds: List<String> = listOf("acct_1RJJXpBOSXndGBvF")
    val payoutIds: List<String> = listOf("po_1RNjpJBOSXndGBvFhok77kor")
    val userIds: List<String> = listOf()

    // find accountIds for given userIds
    if (userIds.isNotEmpty()) {
        if (accountIds.isNotEmpty()) {
            error("Both userIds and accountIds cannot be given at the same time.")
        }
        val users = userIds.mapNotNull { usersCollection[it].fetch() }
        accountIds = users.mapNotNull { it.creator.stripeAccountId } +
            users.flatMap { it.creator.stripeAccountLegacyIds }
    }

    // when specific payouts for given user are given
    if (payoutIds.isNotEmpty()) {
        if (accountIds.size != 1) {
            error("When providing payoutIds, only single accountId must be given.")
        }

        payoutIds.forEach {
            payoutCommandService.execute(
                ProcessPayout(
                    accountId = accountIds.first(),
                    payoutId = it,
                    overwriteInvoice = overwriteInvoice,
                    sendEmail = sendEmail,
                    currency = currency,
                    skipOtherAdjustments = skipOtherAdjustments,
                ),
            )
        }
        exitProcess(0)
    }

    val accounts = if (accountIds.isEmpty()) {
        stripe
            .listAccounts(currency)
            .autoPagingIterable()
            .filter { it.payoutsEnabled || it.chargesEnabled }
    } else {
        accountIds
            .map { stripe.getAccount(it, currency) }
    }

    log.info("Loaded Stripe accounts, iterating to generate invoices.")
    val parsedLimit = timestampLimit.let { ZonedDateTime.of(LocalDate.parse(it).atStartOfDay(), ZoneOffset.UTC) }

    runBlocking(Dispatchers.Default) {
        accounts
            .map { account ->
                async {
                    handleAccount(
                        account,
                        stripe,
                        parsedLimit,
                        payoutCommandService,
                        overwriteInvoice,
                        sendEmail,
                        currency,
                    )
                }
            }
    }
}

fun handleAccount(
    account: Account,
    stripe: StripeService,
    invoiceLimit: ZonedDateTime,
    payoutCommandService: PayoutCommandService,
    overwriteInvoice: Boolean,
    sendEmail: Boolean,
    currency: Currency,
) {
    stripe
        .listPayouts(account.id, currency) {
            // list only invoices with `created` after `invoiceLimit` timestamp
            this.setCreated(
                PayoutListParams.Created.builder().setGt(invoiceLimit.toEpochSecond()).build(),
            )
        }
        .autoPagingIterable()
        .map { payout -> account.id to payout.id }
        .forEach {
            try {
                // do not fail on error so we can actually regenerate
                payoutCommandService.execute(
                    ProcessPayout(
                        accountId = it.first,
                        payoutId = it.second,
                        overwriteInvoice = overwriteInvoice,
                        sendEmail = sendEmail,
                        currency = currency,
                    ),
                )
            } catch (e: Exception) {
                log.error("Couldn't handle ${it.first}/${it.second}: ${e.message}")
            }
        }
}
