package hero.stripe.common

import com.stripe.exception.SignatureVerificationException
import com.stripe.net.Webhook
import hero.baseutils.log
import hero.exceptions.http.ForbiddenException
import hero.http4k.extensions.enum
import hero.model.Currency
import org.http4k.core.Request
import org.http4k.lens.Query

val currencyQuery = Query.enum<Currency>()
    .required("currency", "Currency to decide which Stripe account to handle.")

fun validatePayload(
    request: Request,
    webhookSecret: String,
    tolerance: Long = 300,
) {
    try {
        Webhook.constructEvent(
            request.bodyString(),
            request.headerValues("Stripe-Signature").joinToString(","),
            webhookSecret,
            tolerance,
        )
    } catch (e: SignatureVerificationException) {
        log.fatal(
            "Failed to verify Stripe signature with " +
                "key ending ${webhookSecret.takeLast(4)} for: ${request.bodyString()}",
            cause = e,
        )
        throw ForbiddenException()
    }
}
