package hero.stripe.subscription.service

import com.stripe.model.Subscription
import com.stripe.net.RequestOptions
import com.stripe.param.InvoiceRetrieveParams
import hero.baseutils.instantOf
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.baseutils.nullIfEmpty
import hero.exceptions.http.BadRequestException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.entry
import hero.gcloud.root
import hero.gcloud.where
import hero.jackson.toJson
import hero.model.Currency
import hero.model.Subscriber
import hero.model.User
import hero.model.topics.CouponApplied
import hero.model.topics.SubscriberChanged
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.stripe.coupon.service.CouponCommandService
import hero.stripe.coupon.service.ProcessCoupon
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriberSaver
import hero.stripe.service.StripeSubscriptionService
import org.jooq.DSLContext
import org.jooq.JSONB
import java.time.Instant

class SubscriptionCommandService(
    private val stripeService: StripeService,
    private val usersCollection: TypedCollectionReference<User>,
    private val couponCommandService: CouponCommandService,
    private val pubSub: PubSub,
    private val subscriptionService: StripeSubscriptionService,
    private val stripeClients: StripeClients,
    private val stripeSubscriberSaver: StripeSubscriberSaver,
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun execute(command: ProcessSubscription) {
        val subscription = stripeService.subscription(command.subscriptionId, command.currency)
        if (subscription.metadata["ignore"] == "true") {
            // ignoring bad results of migration
            return
        }

        val user = subscription.metadata["userId"]?.let { usersCollection[it].fetch() }
            ?: findByCustomerId(subscription.customer)
            ?: error("User not found for customer ${subscription.customer}")

        processSubscription(user, subscription.customer, command.currency)

        val coupon = subscription.discount?.coupon
        if (coupon != null) {
            couponCommandService.execute(ProcessCoupon(coupon.id, command.currency))
            pubSub.publish(CouponApplied(subscriptionId = subscription.id, currency = command.currency))
        }

        val userId = subscription.metadata["userId"].nullIfEmpty()
            // old subscriptions do not contain userId in their meta
            ?: userIdByCustomerAndCurrency(subscription.customer, subscription.currency.uppercase())
            ?: error("Cannot find User for ${subscription.customer} and currency ${subscription.currency}.")

        if (subscription.status == "past_due") {
            // https://linear.app/herohero/issue/HH-1902
            // This is the only place where we need to initiate UI notifications about failed payments as
            // these will come from automatic charges with no user interaction.
            usersCollection[userId].field(User::lastChargeFailedAt).update(Instant.now())
        } else {
            usersCollection[userId].field(User::lastChargeFailedAt).update(null)
        }

        writeToSQL(subscription, userId)
        pubSub.publish(
            SubscriberChanged(
                userId = userId,
                creatorId = subscription.metadata["creatorId"] ?: error("Missing creatorId"),
            ),
        )
        log.info("Wrote subscription ${subscription.id} to PostgreSQL", mapOf("userId" to userId))
    }

    private fun processSubscription(
        user: User,
        stripeCustomerId: String,
        currency: Currency,
    ) {
        log.info("Subscription state changed for ${user.id}.", mapOf("userId" to user.id))

        if (!user.isStripeCustomer) {
            throw BadRequestException("User is not a Stripe customer.", mapOf("userId" to user.id))
        }

        val subscriptions = subscriptionService.getSubscriptionsByCustomer(
            stripeCustomerId,
            null,
            filterActive = false,
            currency,
        )

        subscriptions
            .filter { it.items.data.size != 1 }
            .onEach {
                log.fatal(
                    "Subscription didn't contain exactly 1 item.",
                    mapOf("userId" to user.id, "subscription" to it.id, "items" to it.items.data.size),
                )
            }

        // Ideally this should be reworked to handle subscription by their ID (as tried in 22293590a732198550b2788e818c3a7d0388a43e)
        // however this prooved dangerous as it randomly expired current subscriptions. We should store stripeSubscriptionId to overcome this.
        val subscriptionMap = flattenSubscriptions(subscriptions)

        log.info(
            "User currently has ${subscriptionMap.size} subscriptions.",
            mapOf("userId" to user.id),
        )

        subscriptionMap.forEach { (creatorId, subscription) ->
            val invoice = if (subscription.status == "canceled")
                stripeClients[subscription.currency].invoices().retrieve(
                    subscription.latestInvoice,
                    InvoiceRetrieveParams.builder().addAllExpand(listOf("charge")).build(),
                    RequestOptions.getDefault(),
                )
            else
                null

            val message = listOfNotNull(
                subscription.cancellationDetails.reason,
                subscription.cancellationDetails.comment,
                subscription.cancellationDetails.feedback,
                invoice?.chargeObject?.failureMessage,
            ).joinToString(", ")

            val (refunded: Boolean, refused: Boolean) = when {
                subscription.status == "canceled" && invoice?.chargeObject == null -> {
                    (false to false)
                }

                subscription.status == "canceled" && invoice != null -> {
                    // fetch stripe last invoice and its charge
                    val charge = invoice.chargeObject
                    // subscription is refunded if the charged amount is the same as refunded amount
                    ((charge.amount == charge.amountRefunded) to !invoice.paid)
                }

                else -> {
                    (false to false)
                }
            }
            // unfortunately we cannot update cancelled subscriptions, Stripe fails with:
            // You cannot update a subscription that is `canceled` or `incomplete_expired`.
            // subscription.update(SubscriptionUpdateParams.builder().setMetadata(mapOf("refunded" to refunded.toString())).build())
            subscription.metadata[Subscriber::refunded.name] = refunded.toString()
            subscription.metadata[Subscriber::refused.name] = refused.toString()
            subscription.metadata[Subscriber::cancelledReason.name] = message
            stripeSubscriberSaver.save(user.id, creatorId, subscription, false)
        }
    }

    private fun writeToSQL(
        subscription: Subscription,
        userId: String,
    ) {
        val couponId = subscription.discount?.coupon?.id
        context
            .insertInto(
                SUBSCRIPTION,
                SUBSCRIPTION.STRIPE_ID,
                SUBSCRIPTION.STATUS,
                SUBSCRIPTION.CUSTOMER_ID,
                SUBSCRIPTION.STARTED_AT,
                SUBSCRIPTION.ENDS_AT,
                SUBSCRIPTION.ENDED_AT,
                SUBSCRIPTION.CURRENCY,
                SUBSCRIPTION.PRICE_CENTS,
                SUBSCRIPTION.CREATOR_ID,
                SUBSCRIPTION.CREATOR_COUNTRY,
                SUBSCRIPTION.TIER_ID,
                SUBSCRIPTION.CANCELLED_AT,
                SUBSCRIPTION.CANCELLATION_REASON,
                SUBSCRIPTION.COUPON_METHOD,
                SUBSCRIPTION.COUPON_PERCENT_OFF,
                SUBSCRIPTION.COUPON_EXPIRES_AT,
                SUBSCRIPTION.USER_ID,
                SUBSCRIPTION.METADATA,
                SUBSCRIPTION.COUPON_ID,
            ).values(
                subscription.id,
                subscription.status,
                subscription.customer,
                subscription.createdAt,
                subscription.endsAt,
                subscription.endedAtInstant,
                subscription.currency.uppercase(),
                subscription.priceCents,
                subscription.metadata["creatorId"],
                subscription.metadata["creatorCountry"],
                subscription.metadata["tierId"],
                subscription.cancelledAt,
                subscription.cancellationDetails?.reason,
                subscription.couponMethod,
                subscription.couponPercentOff,
                subscription.couponExpiresAt,
                userId,
                JSONB.valueOf(subscription.metadata.toJson()),
                couponId,
            ).onConflict(SUBSCRIPTION.STRIPE_ID)
            .doUpdate()
            .set(SUBSCRIPTION.ENDS_AT, subscription.endsAt)
            .set(SUBSCRIPTION.ENDED_AT, subscription.endedAtInstant)
            .set(SUBSCRIPTION.CANCELLED_AT, subscription.cancelledAt)
            .set(SUBSCRIPTION.CANCELLATION_REASON, subscription.cancellationDetails?.reason)
            .set(SUBSCRIPTION.STATUS, subscription.status)
            .set(SUBSCRIPTION.TIER_ID, subscription.metadata["tierId"])
            .set(SUBSCRIPTION.PRICE_CENTS, subscription.priceCents)
            .set(SUBSCRIPTION.UPDATED_AT, Instant.now())
            .set(SUBSCRIPTION.COUPON_METHOD, subscription.couponMethod)
            .set(SUBSCRIPTION.COUPON_PERCENT_OFF, subscription.couponPercentOff)
            .set(SUBSCRIPTION.COUPON_EXPIRES_AT, subscription.couponExpiresAt)
            .set(SUBSCRIPTION.METADATA, JSONB.valueOf(subscription.metadata.toJson()))
            .let {
                if (couponId != null) {
                    it.set(SUBSCRIPTION.COUPON_ID, couponId)
                } else {
                    it
                }
            }
            .execute()
    }

    private fun userIdByCustomerAndCurrency(
        customerId: String,
        currency: String,
    ): String? =
        usersCollection
            .where(root(User::customerIds).entry(currency))
            .isEqualTo(customerId)
            .fetchSingle()
            ?.id

    private fun findByCustomerId(customerId: String): User? =
        Currency.entries
            .firstNotNullOfOrNull {
                usersCollection.where(root(User::customerIds).entry(it.name)).isEqualTo(customerId).fetchSingle()
            }
}

private val Subscription.priceCents
    get() = items
        .autoPagingIterable()
        .firstOrNull()
        ?.price
        ?.unitAmount

private val Subscription.endsAt
    get() = Instant.ofEpochSecond(currentPeriodEnd)

private val Subscription.createdAt
    get() = Instant.ofEpochSecond(created)

private val Subscription.cancelledAt
    get() = canceledAt?.let { Instant.ofEpochSecond(it) }

private val Subscription.endedAtInstant
    get() = endedAt?.let { Instant.ofEpochSecond(it) }

private val Subscription.couponMethod
    get() = metadata[Subscriber::couponMethod.name]

private val Subscription.couponPercentOff
    get() = metadata[Subscriber::couponPercentOff.name]?.toIntOrNull()

private val Subscription.couponExpiresAt
    get() = metadata[Subscriber::couponExpiresAt.name]?.let { instantOf(it) }

internal fun flattenSubscriptions(subscriptions: Sequence<Subscription>): Map<String, Subscription> =
    subscriptions
        // avoid re-processing old subscriptions - note that cancelled subscriptions should always have
        // cancelAt non-null - which unfortunately does not happen sometimes
        // be warned: `cancelAt` is the time when the subscription is set to end
        .filter {
            it.status in setOf("active", "past_due") ||
                (it.cancelAt ?: Long.MAX_VALUE) > Instant.now().minusDays(7).epochSecond
        }
        .mapNotNull { subscription ->
            val creatorId = subscription.metadata["creatorId"]
            if (creatorId == null) {
                log.fatal("Field creatorId was missing in ${subscription.id} metadata: ${subscription.metadata}")
                return@mapNotNull null
            }
            (creatorId to subscription)
        }
        // we group to { creatorId -> [listOfSubscriptions] }
        .groupBy({ it.first }, { it.second })
        // then we extract the latest subscription
        .map { (creatorId, subscriptionGroup) ->
            creatorId to subscriptionGroup.let {
                it.filter { it.status == "active" }.ifEmpty { null }
                    ?: it.filter { it.status == "past_due" }.ifEmpty { null }
                    // all the other statuses we consider as inactive
                    ?: it
            }
        }
        .map { (creatorId, subscriptionGroup) ->
            try {
                creatorId to subscriptionGroup.maxByOrNull { it.currentPeriodEnd ?: 0 }!!
            } catch (e: Exception) {
                @Suppress("ktlint:standard:max-line-length")
                log.fatal(
                    "$creatorId, $subscriptionGroup, ${subscriptionGroup.firstOrNull()?.currentPeriodEnd} ${e.message}",
                )
                throw e
            }
        }
        .toMap()

data class ProcessSubscription(
    val subscriptionId: String,
    val currency: Currency,
)
