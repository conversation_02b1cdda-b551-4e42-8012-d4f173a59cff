package hero.api.user.service

import hero.baseutils.minusDays
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.model.SubscriberStatus
import hero.model.User
import hero.model.UserStatus
import hero.repository.user.JooqUserHelper
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.NOTIFICATION_SETTINGS
import hero.sql.jooq.Tables.USER
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Instant

class FeaturedCreatorsQueryService(
    lazyContext: Lazy<DSLContext>,
    private val userRepository: UserRepository,
) {
    private val context by lazyContext

    fun execute(query: GetCreatorsSortedBySubs): Page<User> {
        val creators = userRepository.find {
            this
                .where(USER.ID.notIn(query.ignoredCreatorIds))
                .and(USER.STATUS.eq(UserStatus.ACTIVE.name))
                .orderBy(USER.SUBSCRIBERS_COUNT.desc())
                .limit(query.pageable.pageSize)
        }

        return Page(creators, PageRequest(pageSize = query.pageable.pageSize), false)
    }

    fun execute(query: GetCreatorsSortedByRecentPosts): Page<User> {
        val creators = userRepository.find {
            this.where(USER.SUBSCRIBERS_COUNT.gt(50))
                .and(USER.LAST_POST_AT.isNotNull)
                .and(USER.ID.notIn(query.ignoredCreatorIds))
                .and(USER.STATUS.eq(UserStatus.ACTIVE.name))
                .orderBy(USER.LAST_POST_AT.desc().nullsLast())
                .limit(query.pageable.pageSize)
        }

        return Page(creators, PageRequest(pageSize = query.pageable.pageSize), false)
    }

    fun execute(query: GetCreatorsSortedByMostNewSubs): Page<User> {
        val count = DSL.count()
        val cte = DSL.name("recent_posts")
            .`as`(
                DSL
                    .select(Tables.SUBSCRIPTION.CREATOR_ID, count)
                    .from(Tables.SUBSCRIPTION)
                    .where(Tables.SUBSCRIPTION.CREATED_AT.gt(query.since))
                    .and(Tables.SUBSCRIPTION.STATUS.eq(SubscriberStatus.ACTIVE.name.lowercase()))
                    .groupBy(Tables.SUBSCRIPTION.CREATOR_ID)
                    .orderBy(count.desc()),
            )

        val creators = context
            .with(cte)
            .select(JooqUserHelper.userFields)
            .from(USER)
            .join(NOTIFICATION_SETTINGS)
            .on(NOTIFICATION_SETTINGS.USER_ID.eq(USER.ID))
            .join(cte)
            .on(USER.ID.eq(cte.field(Tables.SUBSCRIPTION.CREATOR_ID)))
            .where(USER.ID.notIn(query.ignoredCreatorIds))
            .and(USER.STATUS.eq(UserStatus.ACTIVE.name))
            .limit(query.pageable.pageSize)
            .fetch()
            .map { JooqUserHelper.mapRecordToEntity(it) }

        return Page(creators, PageRequest(), false)
    }

    fun execute(query: GetCreatorsSortedByVerifiedAt): Page<User> {
        val creators = userRepository.find {
            this.where(USER.SUBSCRIBERS_COUNT.gt(0))
                .and(USER.VERIFIED_AT.isNotNull)
                .and(USER.ID.notIn(query.ignoredCreatorIds))
                .and(USER.STATUS.eq(UserStatus.ACTIVE.name))
                // some older creators might have the verifiedAt set recently since it's a new field
                .and(USER.CREATED_AT.gt(Instant.now().minusDays(31)))
                .and(USER.PROFILE_IMAGE_URL.isNotNull)
                .orderBy(USER.VERIFIED_AT.desc().nullsLast())
                .limit(query.pageable.pageSize)
        }

        return Page(creators, PageRequest(pageSize = query.pageable.pageSize), false)
    }
}

data class GetCreatorsSortedBySubs(val pageable: Pageable, val ignoredCreatorIds: List<String> = emptyList())

data class GetCreatorsSortedByRecentPosts(val pageable: Pageable, val ignoredCreatorIds: List<String> = emptyList())

data class GetCreatorsSortedByMostNewSubs(
    val pageable: Pageable,
    val since: Instant,
    val ignoredCreatorIds: List<String> = emptyList(),
)

data class GetCreatorsSortedByVerifiedAt(val pageable: Pageable, val ignoredCreatorIds: List<String> = emptyList())
