package hero.api.library.service

import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.google.cloud.firestore.Query
import hero.api.library.service.dto.SavedPostWithData
import hero.baseutils.fromBase64
import hero.baseutils.log
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.gcloud.CollectionQuery
import hero.gcloud.MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR
import hero.gcloud.TypedCollectionReference
import hero.gcloud.isTrue
import hero.gcloud.util.paginate
import hero.gcloud.where
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Post
import hero.model.SavedPost
import java.time.Instant

class LibraryQueryService(
    private val savedPostsCollection: TypedCollectionReference<SavedPost>,
    private val postsCollection: TypedCollectionReference<Post>,
) {
    fun execute(query: GetSavedPosts): Page<SavedPostWithData> {
        val cursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetSavedPostsCursor>()

        val (savedPosts, hasNext) = savedPostsCollection
            .where(SavedPost::userId).isEqualTo(query.userId)
            .let {
                if (query.filter.subscribedCreatorsOnly) {
                    it.and(SavedPost::subscriptionActive).isTrue()
                } else {
                    it
                }
            }
            .editQueryByCursor(cursor)
            .paginate(query.pageable.pageSize)

        val postsById = savedPosts
            .map { it.postId }
            .chunked(MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR)
            .flatMap {
                postsCollection.where(Post::id).isIn(it).fetchAll()
            }
            .associateBy { it.id }

        val mappedSavedPosts = savedPosts.mapNotNull {
            val post = postsById[it.postId]

            if (post == null) {
                log.fatal("Missing post ${it.postId} for saved post ${it.id}")
                null
            } else {
                SavedPostWithData(it, post)
            }
        }

        return Page(
            mappedSavedPosts,
            nextPageable(savedPosts, query.pageable),
            hasNext,
        )
    }

    private fun CollectionQuery<SavedPost>.editQueryByCursor(cursor: GetSavedPostsCursor?): CollectionQuery<SavedPost> =
        when (cursor) {
            null -> this.orderBy(SavedPost::savedAt, Query.Direction.DESCENDING)
            is GetSavedPostsBySavedAtCursor -> this.orderBy(SavedPost::savedAt, Query.Direction.DESCENDING)
                .startAfter(cursor.savedAt)
        }

    private fun nextPageable(
        savedPosts: List<SavedPost>,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = savedPosts
            .lastOrNull()
            ?.let {
                GetSavedPostsBySavedAtCursor(it.savedAt)
            }
            ?.toJson()
            ?.toBase64()

        return PageRequest(pageSize = pageable.pageSize, afterCursor = afterCursor)
    }
}

data class GetSavedPosts(val userId: String, val filter: GetSavedPostsFilter, val pageable: Pageable)

data class GetSavedPostsFilter(val subscribedCreatorsOnly: Boolean = false)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
private sealed class GetSavedPostsCursor

private data class GetSavedPostsBySavedAtCursor(val savedAt: Instant) : GetSavedPostsCursor()
