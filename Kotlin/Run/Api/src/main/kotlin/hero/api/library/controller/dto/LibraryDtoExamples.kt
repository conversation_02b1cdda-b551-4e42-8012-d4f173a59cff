package hero.api.library.controller.dto

import hero.api.post.controller.dto.examplePostResponse
import java.time.Instant

val exampleAddPostToLibraryRequest = AddPostToLibraryRequest("post-id")

val exampleSavedPostResponse = SavedPostResponse(
    id = "userId-postId",
    savedAt = Instant.now(),
    post = examplePostResponse,
)

val examplePagedSavedPostResponse = PagedSavedPostResponse(
    content = listOf(exampleSavedPostResponse),
    hasNext = true,
    afterCursor = "eyJsYXN0TWVzc2FnZUF0IjoiMjAyMy0wOS0wNVQwODo0MzoxMC42MTY0MzFaIn0=",
    beforeCursor = null,
)
