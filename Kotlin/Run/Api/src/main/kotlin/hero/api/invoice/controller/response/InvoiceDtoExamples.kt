package hero.api.invoice.controller.response

import hero.api.user.controller.UsersJsonApiController.Companion.exampleCompany
import hero.api.user.controller.UsersJsonApiController.Companion.exampleUserResponseV2
import hero.model.Currency
import hero.model.InvoiceDto
import hero.model.InvoiceDtoAttributes
import hero.model.InvoiceDtoRelationships
import hero.model.InvoiceItem
import hero.model.InvoiceItemType
import hero.model.UserDtoRelationship
import hero.model.toPublic
import java.time.Instant

val invoiceDtoExample = InvoiceDto(
    id = "123456",
    attributes = InvoiceDtoAttributes(
        stripeAccountId = "ac_123456",
        stripePayoutId = "po_1234556",
        timestamp = Instant.now(),
        total = 10_00,
        total4D = 10_0000,
        currency = Currency.EUR,
        currencyPayout = Currency.EUR,
        privateItems = listOf(
            InvoiceItem(
                title = "Tier € 5",
                priceUnit = 500,
                priceUnit4D = 50000,
                count = 10,
                priceTotal = 5000,
                priceTotal4D = 500000,
                type = InvoiceItemType.TURNOVER,
                vatCents = 0,
                comparableToPayout = true,
            ),
            InvoiceItem(
                title = "Tier € 5 fee",
                priceUnit = 50,
                priceUnit4D = 5000,
                count = 10,
                priceTotal = 500,
                priceTotal4D = 50000,
                type = InvoiceItemType.FEE,
                vatCents = 22,
                comparableToPayout = true,
            ),
        ),
        issuingCompany = exampleCompany.copy(name = "AA", id = "11").toPublic(),
        invoicedCompany = exampleCompany.copy(name = "BB", id = "22").toPublic(),
        eurConversionRateCents = 123L,
        euReverseCharged = false,
        authToken = "zxcvbnmqwertyu",
    ),
    relationships = InvoiceDtoRelationships(
        user = UserDtoRelationship(exampleUserResponseV2.id),
        parentUser = UserDtoRelationship(exampleUserResponseV2.id),
    ),
)
