package hero.api.payment.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.User
import hero.model.UserCompany
import hero.model.topics.FeesUpdateRequested
import java.math.BigDecimal

fun main() {
    val production = true
    val environment = "prod"
    val firestore = firestore(SystemEnv.cloudProject, production)
    val usersCollection = firestore.typedCollectionOf(User)
    val pubSub = PubSub(environment, SystemEnv.cloudProject)
    val fee = BigDecimal(10L)

    usersCollection
        .where(root(User::company).path(UserCompany::country)).isEqualTo("SK")
        // .where(User::id).isEqualTo("mikeoganesjandnqgmopj")
        .fetchAll()
        .filter { it.counts.supporters > 0 }
        .forEach { user ->
            pubSub.publish(
                FeesUpdateRequested(
                    creatorId = user.id,
                    vatId = user.company!!.vatId,
                    country = user.company!!.country!!,
                    tierId = user.creator.tierId,
                    forced = true,
                ),
            )
            log.info(
                user.id + "\t" +
                    user.company?.country + "\t" +
                    user.company?.vatId + "\t" +
                    user.counts.supporters,
            )
            // to avoid 429 errors on the StripeFeesUpdater Cloud Function
            Thread.sleep(1_000)
        }
}
