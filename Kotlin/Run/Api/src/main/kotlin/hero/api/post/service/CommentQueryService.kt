package hero.api.post.service

import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Post
import hero.model.Subscriber
import hero.model.topics.PostState
import hero.repository.post.PostRepository
import hero.repository.post.PostType
import hero.sql.cmp
import hero.sql.cmpBeforeCursor
import hero.sql.jooq.Tables
import hero.sql.orderBy
import hero.sql.orderByReversed
import java.time.Instant

class CommentQueryService(
    private val postRepository: PostRepository,
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
) {
    fun execute(query: GetComments): Page<CommentWithSubscriptionInfo> {
        val parentPost = postRepository.findById(query.parentPostId)
            ?.also { validatePostAccess(it, query.userId) }
            ?: throw NotFoundException("Parent ${query.parentPostId} does not exist")

        val topLevelPostUserId = getTopLevelPostUserId(parentPost)
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetCommentsCursor>()?.lastPublishedAt
        val beforeCursor = query.pageable.beforeCursor?.fromBase64()?.fromJson<GetCommentsCursor>()?.lastPublishedAt

        val isPostAuthor = query.userId == topLevelPostUserId
        val subscriptionInfo = if (isPostAuthor) {
            null
        } else {
            // we return empty array if there is no subscription, since there is no use case for empty comments
            query.userId
                ?.let { subscribersCollection.fetchActiveSubscription(it, topLevelPostUserId) }
                ?: return Page.emptyPage(query.pageable.pageSize)
        }

        val sort = query.pageable.sort
        val (comments, hasNext) = postRepository
            .find {
                this
                    .where(Tables.POST.PARENT_ID.eq(query.parentPostId))
                    .and(Tables.POST.STATE.eq(PostState.PUBLISHED.name))
                    .and(Tables.POST.TYPE.eq(PostType.COMMENT.name))
                    .let {
                        if (afterCursor == null && beforeCursor == null) {
                            it
                                .orderBy(Tables.POST.PUBLISHED_AT.orderBy(sort))
                        } else if (afterCursor != null) {
                            it
                                .and(Tables.POST.PUBLISHED_AT.cmp(afterCursor, sort))
                                .orderBy(Tables.POST.PUBLISHED_AT.orderBy(sort))
                        } else {
                            it
                                .and(Tables.POST.PUBLISHED_AT.cmpBeforeCursor(beforeCursor, sort))
                                .orderBy(Tables.POST.PUBLISHED_AT.orderByReversed(sort))
                        }
                    }
                    .limit(query.pageable.pageSize + 1)
            }
            .let {
                val hasNext = it.size > query.pageable.pageSize
                val comments = if (beforeCursor != null) it.reversed() else it

                comments.take(query.pageable.pageSize) to hasNext
            }

        val commentsWithSubscriptionInfo = comments.map {
            CommentWithSubscriptionInfo(it, subscriptionInfo, topLevelPostUserId)
        }
        return Page(commentsWithSubscriptionInfo, nextPageable(comments, query.pageable), hasNext)
    }

    fun execute(query: GetComment): CommentBaseData {
        val comment = postRepository.getById(query.commentId)
        val parentId = comment.parentId ?: throw BadRequestException("${query.commentId} is not a comment")

        val parent = postRepository.getById(parentId)
        val rootParent = getRootParent(parent)

        val subscriptionInfo = subscribersCollection.fetchActiveSubscription(query.userId, rootParent.userId)

        return if (parent.parentId == null) {
            CommentData(comment = comment, rootPost = parent, subscriptionInfo = subscriptionInfo)
        } else {
            ReplyData(comment = comment, parent = parent, rootPost = rootParent, subscriptionInfo = subscriptionInfo)
        }
    }

    private fun nextPageable(
        comments: List<Post>,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = comments
            .lastOrNull()
            ?.let {
                val lastPublishedAt = it.published
                GetCommentsCursor(it.id, lastPublishedAt).toJson().toBase64()
            }

        val beforeCursor = comments
            .firstOrNull()
            ?.let {
                val lastPublishedAt = it.published
                GetCommentsCursor(it.id, lastPublishedAt).toJson().toBase64()
            }

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor, beforeCursor = beforeCursor)
    }

    private fun getTopLevelPostUserId(post: Post): String {
        val parentUserId = post.parentUserId
        if (parentUserId != null) {
            return parentUserId
        }
        val parentId = post.parentId
        return if (parentId == null) {
            post.userId
        } else {
            getTopLevelPostUserId(postRepository.getById(parentId))
        }
    }

    private fun getRootParent(post: Post): Post {
        val parentPostId = post.parentPostId
        if (parentPostId != null) {
            return postRepository.getById(parentPostId)
        }

        val parentId = post.parentId
        if (parentId == null) {
            return post
        } else {
            val parent = postRepository.getById(parentId)
            return getRootParent(parent)
        }
    }
}

data class GetComments(val userId: String?, val parentPostId: String, val pageable: Pageable)

data class GetComment(val commentId: String, val userId: String)

sealed class CommentBaseData {
    abstract val comment: Post

    // top level post, post created by the creator
    abstract val rootPost: Post
    abstract val subscriptionInfo: Subscriber?
}

data class ReplyData(
    override val comment: Post,
    override val rootPost: Post,
    override val subscriptionInfo: Subscriber?,
    val parent: Post,
) : CommentBaseData()

data class CommentData(
    override val comment: Post,
    override val rootPost: Post,
    override val subscriptionInfo: Subscriber?,
) : CommentBaseData()

data class CommentWithSubscriptionInfo(val comment: Post, val subscriptionInfo: Subscriber?, val postAuthor: String)

private data class GetCommentsCursor(val lastCommentId: String, val lastPublishedAt: Instant)
