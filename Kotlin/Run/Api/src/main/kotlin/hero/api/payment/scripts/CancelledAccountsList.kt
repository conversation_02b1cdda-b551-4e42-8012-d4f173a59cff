package hero.api.payment.scripts

import com.stripe.model.Balance
import com.stripe.net.RequestOptions
import hero.gcloud.FirestoreRef
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.User
import hero.model.UserStatus

fun main() {
    val (firestore: FirestoreRef, production: <PERSON><PERSON><PERSON>, _) = initializeStripeScript(true)

    val users = firestore.typedCollectionOf(User)
        .where(User::status).isEqualTo(UserStatus.DELETED)
        .fetchAll()
        .filter { it.creator.stripeRequirements?.disabledReason != null }

    users.forEach {
        try {
            it.creator.stripeAccountId
            val balance = Balance.retrieve(
                RequestOptions.builder().setStripeAccount(it.creator.stripeAccountId!!).build(),
            )
            if (balance.available?.any { (it.amount ?: 0L) != 0L } == true) {
                println(
                    "${it.id}\thttps://dashboard.stripe.com/connect/accounts/${it.creator.stripeAccountId}\t${
                        balance.available.first().let { it.currency + "\t" + Math.round(it.amount / 100.0) }
                    }",
                )
            } else if (balance.pending?.any { (it.amount ?: 0L) != 0L } == true) {
                println(
                    "${it.id}\thttps://dashboard.stripe.com/connect/accounts/${it.creator.stripeAccountId}\t${
                        balance.pending.first().let { it.currency + "\t" + Math.round(it.amount / 100.0) }
                    }",
                )
            } else if (balance.instantAvailable?.any { (it.amount ?: 0L) != 0L } == true) {
                println(
                    "${it.id}\thttps://dashboard.stripe.com/connect/accounts/${it.creator.stripeAccountId}\t${
                        balance.instantAvailable.first().let { it.currency + "\t" + Math.round(it.amount / 100.0) }
                    }",
                )
            }
        } catch (e: Exception) {
            // log.error("${it.id}/${it.creator.stripeAccountId}: ${e.message}")
        }
    }
}
