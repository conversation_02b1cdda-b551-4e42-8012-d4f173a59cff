package hero.api.user.scripts

import hero.api.user.repository.ImageRepository
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.User

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val usersCollection = firestore.typedCollectionOf(User)
    val imageRepository = ImageRepository(production)
    val users = usersCollection.fetchAll<User>()
    users.forEach { user ->
        if (user.image?.id == null) {
            return
        }
        val dimensions = imageRepository.dimensions(user.image!!.id)
        if (user.image!!.width != dimensions.width || user.image!!.height != dimensions.height) {
            log.warn("User ${user.id} has invalid dimensions: ${user.image} != $dimensions")
        }
    }
}
