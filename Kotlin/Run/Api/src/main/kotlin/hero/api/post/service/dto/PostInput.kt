package hero.api.post.service.dto

import hero.model.DocumentAsset

data class PostInput(
    val text: String,
    val textHtml: String,
    val assets: List<PostAssetInput> = listOf(),
    val textDelta: String? = null,
)

data class PostAssetInput(
    val image: ImageAssetInput? = null,
    val gjirafa: GjirafaAssetInput? = null,
    val gjirafaLivestream: GjirafaLivestreamAssetInput? = null,
    val document: DocumentAsset? = null,
    val thumbnail: String? = null,
)

data class GjirafaAssetInput(
    val id: String,
)

data class GjirafaLivestreamAssetInput(
    val id: String,
)

data class ImageAssetInput(
    val url: String,
    val width: Int,
    val height: Int,
    val hidden: Boolean = false,
)
