package hero.api.user.scripts

import com.google.cloud.firestore.Query
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.User
import hero.model.UserStatus
import hero.model.topics.EmailPublished

fun main() {
    val production = true
    val environment = if (production) "prod" else "devel"
    val cloudProject = SystemEnv.cloudProject
    val pubSub = PubSub(environment, cloudProject)
    val firestoreRef = firestore(cloudProject, production)
    val userCollection = firestoreRef.typedCollectionOf(User)
    var cursor = ""

    while (true) {
        val list = userCollection
            // .where(User::id).isEqualTo("vojtechknyttlnjirpwod")
            .where(User::id).isNotEqualTo("")
            .orderBy(User::id, Query.Direction.ASCENDING)
            .startAfter(cursor)
            .limit(100)
            .fetchAll()

        cursor = list.lastOrNull()?.id ?: error("Done.")

        list
            .filter { it.status == UserStatus.ACTIVE }
            .filter { it.email != null }
            .filter { it.language == "cs" || it.language == "en" || it.language == "sk" }
            .filter { it.counts.supporters != 0L || it.counts.supporting != 0L || (it.counts.posts ?: 0L) != 0L }
            .forEach { user ->
                log.info("Notifying ${user.id}/${user.email}")
                pubSub.publish(
                    EmailPublished(
                        to = user.email!!,
                        template = "newsletter",
                        variables = listOf(),
                        language = user.language,
                    ),
                )
            }
    }
}
