package hero.api.statistics.service

import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.gjirafa.GjirafaStatsService
import hero.model.Post
import hero.model.topics.PostState
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.tables.DailyPostViewStatistics.DAILY_POST_VIEW_STATISTICS
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

class PostStatisticsQueryService(
    private val postsCollection: TypedCollectionReference<Post>,
    private val gjirafaService: GjirafaStatsService,
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    /**
     * SELECT SUM(views) AS views, post_id
     * FROM daily_post_view_statistics
     * WHERE creator_id = 'creator-id'
     *   AND DATE BETWEEN '2024-06-16' AND '2024-06-18'
     * GROUP BY post_id
     * ORDER BY views DESC
     * LIMIT 5;
     */
    fun execute(query: GetCreatorsMostViewedPosts): List<PostWithViewStats> {
        val views = DSL.sum(DAILY_POST_VIEW_STATISTICS.VIEWS).`as`("views")
        val fromDate = LocalDate.ofInstant(query.from, ZoneOffset.UTC)
        val toDate = LocalDate.ofInstant(query.to, ZoneOffset.UTC)

        val postsWithStats = context
            .select(views, DAILY_POST_VIEW_STATISTICS.POST_ID)
            .from(DAILY_POST_VIEW_STATISTICS)
            .join(POST).on(POST.ID.eq(DAILY_POST_VIEW_STATISTICS.POST_ID)).and(POST.STATE.eq(PostState.PUBLISHED.name))
            .where(DAILY_POST_VIEW_STATISTICS.CREATOR_ID.eq(query.creatorId))
            .and(DAILY_POST_VIEW_STATISTICS.DATE.between(fromDate, toDate))
            .groupBy(DAILY_POST_VIEW_STATISTICS.POST_ID)
            .orderBy(views.desc())
            .limit(query.limit)
            .fetch()
            .map {
                PostViewStats(
                    postId = it[DAILY_POST_VIEW_STATISTICS.POST_ID],
                    views = it[views]?.toLong() ?: 0,
                )
            }

        val postByIds = postsCollection
            .where(Post::id)
            .isIn(postsWithStats.map { it.postId })
            .fetchAll()
            .associateBy { it.id }

        return postsWithStats.map {
            PostWithViewStats(postByIds.getValue(it.postId), it)
        }
    }

    /**
     * SELECT SUM(views) AS views
     * FROM daily_post_view_statistics
     * WHERE post_id = 'post-id'
     */
    fun execute(query: GetPostViewStats): PostWithCompleteStats {
        val post = postsCollection[query.postId].get()
        if (post.userId != query.userId) {
            throw ForbiddenException("${query.userId} cannot access view stats of post ${query.postId}")
        }

        val stats = runBlocking(Dispatchers.Default) {
            val viewsAsync = async {
                val viewsField = DSL.sum(DAILY_POST_VIEW_STATISTICS.VIEWS).`as`("views")
                context
                    .select(viewsField)
                    .from(DAILY_POST_VIEW_STATISTICS)
                    .where(DAILY_POST_VIEW_STATISTICS.POST_ID.eq(query.postId))
                    .fetchSingle()
                    .map {
                        it[viewsField]?.toLong() ?: 0
                    }
            }

            val gjirafaAsset = post.assets.firstNotNullOfOrNull { it.gjirafa }
            val assetStatsAsync = if (gjirafaAsset != null) {
                val from = LocalDate.of(2023, 1, 1)
                val now = LocalDate.now()
                async {
                    val playsAsync = async {
                        gjirafaService.assetPlays(gjirafaAsset.id, from, now)
                    }
                    val completePlaysAsync = async {
                        gjirafaService.assetCompletePlays(gjirafaAsset.id, from, now)
                    }
                    val averageDurationAsync = async {
                        gjirafaService.assetAverageViewDuration(gjirafaAsset.id)
                    }
                    val averageDuration = averageDurationAsync.await()
                    listOf(
                        AssetStats(
                            assetId = gjirafaAsset.id,
                            plays = playsAsync.await().toInt(),
                            completes = completePlaysAsync.await().toInt(),
                            averagePlayDurationSeconds = averageDuration,
                        ),
                    )
                }
            } else {
                null
            }

            val views = viewsAsync.await()
            val assetStats = assetStatsAsync?.await() ?: emptyList()
            PostCompleteStats(postId = post.id, views = views, assetStats = assetStats)
        }

        return PostWithCompleteStats(post, stats)
    }
}

data class GetCreatorsMostViewedPosts(
    val creatorId: String,
    val from: Instant,
    val to: Instant,
    val limit: Int,
)

data class GetPostViewStats(
    val postId: String,
    val userId: String,
)

data class PostWithViewStats(
    val post: Post,
    val stats: PostViewStats,
)

data class PostViewStats(
    val postId: String,
    val views: Long,
)

data class PostWithCompleteStats(
    val post: Post,
    val stats: PostCompleteStats,
)

data class PostCompleteStats(
    val postId: String,
    val views: Long,
    val assetStats: List<AssetStats>,
)

data class AssetStats(
    val assetId: String,
    val plays: Int,
    val completes: Int,
    val averagePlayDurationSeconds: Double,
)
