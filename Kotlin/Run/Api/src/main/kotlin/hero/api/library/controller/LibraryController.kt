package hero.api.library.controller

import hero.api.library.controller.dto.AddPostToLibraryRequest
import hero.api.library.controller.dto.exampleAddPostToLibraryRequest
import hero.api.library.controller.dto.examplePagedSavedPostResponse
import hero.api.library.controller.dto.exampleSavedPostResponse
import hero.api.library.controller.dto.toResponse
import hero.api.library.service.AddPostToLibrary
import hero.api.library.service.GetSavedPosts
import hero.api.library.service.GetSavedPostsFilter
import hero.api.library.service.LibraryCommandService
import hero.api.library.service.LibraryQueryService
import hero.api.library.service.RemovePostFromLibrary
import hero.core.data.PageRequest
import hero.core.data.toResponse
import hero.http4k.auth.getJwtUser
import hero.http4k.controller.QueryUtils
import hero.http4k.extensions.body
import hero.http4k.extensions.delete
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean

class LibraryController(
    private val libraryCommandService: LibraryCommandService,
    private val libraryQueryService: LibraryQueryService,
) {
    @Suppress("Unused")
    val routeAddPostToLibrary: ContractRoute =
        "/v1/library".post(
            summary = "Add a post to the library",
            tag = "Library",
            parameters = object {},
            responses = listOf(Status.OK example exampleSavedPostResponse),
            receiving = exampleAddPostToLibraryRequest,
            handler = { request, _ ->
                val jwtUser = request.getJwtUser()
                val addToLibraryRequest = lens<AddPostToLibraryRequest>(request)

                val savedPost = libraryCommandService.execute(AddPostToLibrary(addToLibraryRequest.postId, jwtUser.id))

                Response(Status.OK).body(savedPost.toResponse())
            },
        )

    @Suppress("Unused")
    val routeRemovePostFromLibrary: ContractRoute =
        ("/v1/library" / Path.of("savedPostId")).delete(
            summary = "Remove a post from the library",
            tag = "Library",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT example Unit),
            handler = { request, _, savedPostId ->
                val jwtUser = request.getJwtUser()

                libraryCommandService.execute(RemovePostFromLibrary(savedPostId, jwtUser.id))

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    val routeGetLibraryPosts: ContractRoute =
        "/v1/library".get(
            summary = "Get posts saved in user's library",
            tag = "Library",
            parameters = object {
                val pageSize = QueryUtils.pageSize()
                val afterCursor = QueryUtils.afterCursor()
                val subscribedCreatorsOnly = Query.boolean().defaulted(
                    "subscribedCreatorsOnly",
                    true,
                    "Filters saved posts to include only those from creators the user is currently subscribed to",
                )
            },
            responses = listOf(Status.OK example examplePagedSavedPostResponse),
            handler = { request, parameters ->
                val jwtUser = request.getJwtUser()
                val pageSize = parameters.pageSize(request)
                val afterCursor = parameters.afterCursor(request)
                val subscribedCreatorsOnly = parameters.subscribedCreatorsOnly(request)

                val pageable = PageRequest(pageSize = pageSize, afterCursor = afterCursor)
                val filter = GetSavedPostsFilter(subscribedCreatorsOnly)
                val result = libraryQueryService.execute(GetSavedPosts(jwtUser.id, filter, pageable))

                val response = result.toResponse { it.toResponse() }

                Response(Status.OK).body(response)
            },
        )
}
