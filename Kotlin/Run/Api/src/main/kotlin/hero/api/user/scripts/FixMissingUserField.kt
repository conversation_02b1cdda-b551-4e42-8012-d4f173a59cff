package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.get
import hero.model.User
import java.util.concurrent.Executors

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject)

    val collection = firestore["${production.envPrefix}-users"]

    var i = 0
    val executor = Executors.newFixedThreadPool(33)
    collection
        .fetchAll<User>()
        .onEach {
            i++
            val j = i
            executor.execute {
                try {
                    collection.document(it.id).update("a", "d").get()
                    if (j % 100 == 0) println(it.id)
                } catch (e: Exception) {
                    println("Error when updating ${it.id}: ${e::class.simpleName} – ${e.message}")
                }
            }
        }
    executor.shutdown()
    while (!executor.isTerminated) {
        // wait a bit until all the checks are finished
        Thread.sleep(1000)
    }
}
