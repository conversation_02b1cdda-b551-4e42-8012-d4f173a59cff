package hero.api.payment.scripts

import com.github.kittinunf.fuel.httpPost
import com.stripe.net.RequestOptions
import hero.gcloud.isNotNull
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.jackson.toJson
import hero.model.Creator
import hero.model.Currency
import hero.model.User
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@Suppress("ktlint:standard:max-line-length")
fun main() {
    val (firestore, production, _, _, clients) = initializeStripeScript(true)
    val currency = Currency.EUR

    val usersCollection = firestore.typedCollectionOf(User)
    val creators = usersCollection
        .where(root(User::creator).path(Creator::stripeAccountId)).isNotNull()
        .fetchAll()
        .filter { it.creator.stripeAccountOnboarded == true }

    val pool = Executors.newFixedThreadPool(30)
    for (creator in creators) {
        val accountId = creator.creator.stripeAccountId!!
        pool.submit {
            try {
                val account = clients[currency].accounts().retrieve(accountId)
                val response = "https://staging.herohero.co/api/v1/stripe/webhooks/accounts"
                    .httpPost()
                    .body(mapOf("account" to accountId).toJson())
                    .response()
                println(
                    "https://herohero.co/${creator.id}\t${creator.email}\t${creator.counts.supporters}\thttps://dashboard.stripe.com/connect/accounts/$accountId\t${response.second.statusCode}\t${response.second.responseMessage}",
                )

                if (account.requirements.currentlyDue.isEmpty() && account.requirements.eventuallyDue.isEmpty()) {
                    return@submit
                }
                val balance = clients[currency].balance().retrieve(
                    RequestOptions.builder().setStripeAccount(accountId).build(),
                )
                val balanceAvailable =
                    (balance.available.firstOrNull()?.amount ?: 0L) + (balance.pending.firstOrNull()?.amount ?: 0L)
                if (balanceAvailable <= 0L) {
                    return@submit
                }
                // println("https://herohero.co/${creator.id}\t${creator.email}\t${creator.counts.supporters}\thttps://dashboard.stripe.com/connect/accounts/$accountId\t${response.second.statusCode}\t${response.second.responseMessage}")
            } catch (e: Exception) {
                // log.error("Cannot fetch account ${creator.id}/$accountId: ${e.message}")
            }
        }
    }
    pool.shutdown()
    pool.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)
}
