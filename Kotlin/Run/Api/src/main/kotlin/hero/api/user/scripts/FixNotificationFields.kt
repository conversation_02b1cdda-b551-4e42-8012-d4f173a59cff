package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.firestore
import hero.gcloud.isTrue
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Creator
import hero.model.User
import hero.model.UserStatus

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    val collection = firestore.typedCollectionOf(User)
    val list = collection
        .where(root(User::creator).path(Creator::active)).isTrue()
        .fetchAll()
        .filter { it.status == UserStatus.ACTIVE && it.email != null }

    list.forEach { user ->
        println(user.email + "\t" + user.counts.supporters)
    }
}
