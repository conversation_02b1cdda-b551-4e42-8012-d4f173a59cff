package hero.api.user.controller

import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.post
import hero.jackson.map
import hero.jackson.to
import hero.jwt.parseJwt
import hero.model.ExtractedUser
import hero.model.Role
import hero.model.UserIdResponse
import hero.model.UserStateChange
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class UserFactoryController(
    private val repository: UsersRepository,
) {
    @Suppress("unused")
    val routeUserFactory: ContractRoute =
        "/v1/users".post(
            summary = "User factory method.",
            tag = "Users",
            parameters = object {},
            receiving = null,
            responses = listOf(
                Status.OK example UserIdResponse("anne-marie", Role.USER, UserStateChange.CREATED, true),
            ),
            handler = { request, _ ->
                val extractedUser = request.bodyString().parseJwt().to<ExtractedUser>()
                log.debug("User ${extractedUser.email} extracted.", extractedUser.map())
                val (user, state) = repository.factory(extractedUser)
                log.info("User ${extractedUser.email} factoried.", user.map() + mapOf("userId" to user.id))
                Response(Status.OK)
                    .body(
                        UserIdResponse(
                            userId = user.id,
                            role = user.role,
                            state = state,
                            emailVerified = user.emailVerified,
                        ),
                    )
            },
        )
}
