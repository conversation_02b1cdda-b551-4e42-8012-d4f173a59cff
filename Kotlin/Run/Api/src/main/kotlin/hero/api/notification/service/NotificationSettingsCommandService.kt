package hero.api.notification.service

import hero.gcloud.TypedCollectionReference
import hero.model.NotificationsEnabled
import hero.model.User
import hero.repository.notification.NotificationSettingsRepository

class NotificationSettingsCommandService(
    private val usersCollection: TypedCollectionReference<User>,
    private val notificationSettingsRepository: NotificationSettingsRepository,
) {
    fun execute(command: UpdateNotificationSettings) {
        val user = usersCollection[command.userId].get()

        usersCollection[command.userId].set(user.copy(notificationsEnabled = command.notificationSettings))
        notificationSettingsRepository.save(command.userId, command.notificationSettings)
    }
}

data class UpdateNotificationSettings(
    val userId: String,
    val notificationSettings: NotificationsEnabled,
)
