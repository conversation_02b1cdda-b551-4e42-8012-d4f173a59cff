package hero.api.user.scripts

import hero.baseutils.SystemEnv
import hero.gcloud.fetchAll
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Subscriber

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    val collection = firestore.typedCollectionOf(Subscriber)

    collection
        .fetchAll()
        .groupBy { it.tierId!! }
        .map { it.key to it.value.size }
        .forEach { println(it.first + " " + it.second) }
}
