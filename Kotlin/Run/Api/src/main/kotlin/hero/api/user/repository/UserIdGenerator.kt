package hero.api.user.repository

import hero.baseutils.instantOf
import hero.baseutils.md5nice
import java.time.Instant
import java.util.Random

class UserIdGenerator(val randomizer: Random = Random()) {
    private val baseLine: Long =
        instantOf("2023-09-01T00:00:00Z").toEpochMilli()

    private val adjectives = try {
        javaClass.classLoader
            .getResourceAsStream("adjectives.list")!!
            .bufferedReader()
            .useLines { lines -> lines.toList() }
            .map { it.lowercase() }
    } catch (e: Throwable) {
        throw IllegalStateException("Cannot read list of adjectives to be used for usernames: ${e.message}", e)
    }

    /** select a suitable adjective starting with the same character as the expected user's name */
    fun generatePath(seed: String): String {
        try {
            val firstChar = seed.lowercase().replace("[^a-z]".toRegex(), "").firstOrNull() ?: 'a'
            return adjectives.shuffled(randomizer).first { it.startsWith(firstChar) }
        } catch (e: Exception) {
            throw IllegalStateException("Error generating `path` for user with seed '$seed': ${e.message}", e)
        }
    }

    /** we generate user's id as lowercase a-z md5 of the user's name and number of milliseconds from 2023/09/01 */
    fun generateId(seed: String): String {
        try {
            return seed.md5nice().take(5) + toBase25(Instant.now().toEpochMilli() - baseLine)
        } catch (e: Exception) {
            throw IllegalStateException("Error generating `id` for user with seed '$seed': ${e.message}", e)
        }
    }

    private fun toBase25(value: Long): String =
        generateSequence(value) { it / 25 }
            .takeWhile { it > 0 }
            .map { (it % 25 + 97).toInt().toChar() }
            .joinToString("")
}
