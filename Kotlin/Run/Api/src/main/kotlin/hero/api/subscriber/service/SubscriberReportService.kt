package hero.api.subscriber.service

import com.github.kittinunf.fuel.httpPost
import hero.baseutils.internalFunctionCall
import hero.baseutils.log
import hero.gcloud.SharedDriveIds.CREATOR_SUBSCRIBERS_REPORTS
import hero.gcloud.TypedCollectionReference
import hero.gcloud.batched
import hero.gcloud.createSpreadsheet
import hero.gcloud.resizeColumns
import hero.gcloud.shareableLink
import hero.gcloud.string
import hero.gcloud.writeRangeFormatted
import hero.jackson.toJson
import hero.model.User
import hero.model.topics.GenerateSubscribersReportRequest
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

class SubscriberReportService(
    private val usersCollection: TypedCollectionReference<User>,
    private val internalApiKey: String,
) {
    fun execute(command: GenerateSubscriberReport): String {
        val creator = usersCollection[command.creatorId].get()

        val fileName = "${creator.name} subscribers report ${formatter.format(Instant.now())}"

        val spreadsheet = createSpreadsheet(fileName, CREATOR_SUBSCRIBERS_REPORTS, creator.id)
            .apply {
                val message = string("Report is being generated, please come back in few minutes")
                batched()
                    .writeRangeFormatted(listOf(listOf(message)), 1, 1)
                    .resizeColumns(1, 1, 600)
                    .execute()
            }

        val shareableLink = spreadsheet.shareableLink()
            ?: error("Failed to get shareable link for subs report for creator ${command.creatorId}")

        return shareableLink
            .also {
                internalFunctionCall("subscribers-report-generator")
                    .httpPost()
                    .header("X-HeroHero-Api-Key" to internalApiKey)
                    .body(
                        GenerateSubscribersReportRequest(
                            creatorId = command.creatorId,
                            spreadsheetId = spreadsheet.spreadsheetId,
                            privacyPolicyEffectiveAt = creator.privacyPolicyEffectiveAt,
                        ).toJson(),
                    )
                    .response { _, _, result ->
                        log.info(
                            "Subs report generation responded with $result",
                            mapOf("userId" to command.creatorId),
                        )
                    }
            }
    }
}

data class GenerateSubscriberReport(val creatorId: String)

// TODO: differentiate by user's locale
private val formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd").withZone(ZoneOffset.UTC)
