{"id": "evt_3PonCeB6ZCHekl2R0xZzLOH5", "object": "event", "api_version": "2023-10-16", "created": 1723903320, "data": {"object": {"id": "ch_3PonCeB6ZCHekl2R05677cI9", "object": "charge", "amount": 100, "amount_captured": 100, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": null, "balance_transaction": "txn_3PonCeB6ZCHekl2R08EUAC22", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": null, "name": null, "phone": null}, "calculated_statement_descriptor": "HEROHERO.CO", "captured": true, "created": 1723903320, "currency": "usd", "customer": null, "description": "(created by Stripe CLI)", "destination": null, "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {}, "on_behalf_of": null, "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "normal", "risk_score": 30, "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "payment_intent": "pi_3PonCeB6ZCHekl2R0lRQame0", "payment_method": "pm_1PonCeB6ZCHekl2RbUzOAriu", "payment_method_details": {"card": {"amount_authorized": 100, "authorization_code": null, "brand": "visa", "checks": {"address_line1_check": null, "address_postal_code_check": null, "cvc_check": "pass"}, "country": "US", "exp_month": 8, "exp_year": 2025, "extended_authorization": {"status": "disabled"}, "fingerprint": "0cTaTkH6K0y8KgTf", "funding": "credit", "incremental_authorization": {"status": "unavailable"}, "installments": null, "last4": "4242", "mandate": null, "multicapture": {"status": "unavailable"}, "network": "visa", "network_token": {"used": false}, "overcapture": {"maximum_amount_capturable": 100, "status": "unavailable"}, "three_d_secure": null, "wallet": null}, "type": "card"}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xSHBZaFFCNlpDSGVrbDJSKNjagrYGMgalHuX-fno6LBZZJC8H-ptlXqM_FRJtucM9gAkLaxsolQiedJ-Z1OPP2tha0aMomlzWmSkE", "refunded": false, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": null, "statement_descriptor_suffix": null, "status": "succeeded", "transfer_data": null, "transfer_group": null}}, "livemode": false, "pending_webhooks": 5, "request": {"id": "req_ttuGE6VGeknga5", "idempotency_key": "a503e67a-31ba-48be-9623-6dbca61936d6"}, "type": "charge.succeeded"}