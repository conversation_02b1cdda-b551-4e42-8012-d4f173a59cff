package hero.api.payment.controller

import hero.api.subscriber.repository.PaymentIntentStatus
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.subscriber.repository.SubscribersRepository
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.zonedDateTimeOf
import hero.http4k.auth.jwtFor
import hero.http4k.auth.withAccessTokenCookie
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Creator
import hero.model.Currency
import hero.model.SubscriberStatus
import hero.model.Tier
import hero.model.User
import hero.model.UserDtoRelationship
import hero.stripe.service.VatMapping
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.unmockkAll
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.test.assertEquals

internal class StripeControllerSubscriptionTest {
    private val subscriberRepository: SubscribersRepository = mockk()
    private val subscriberStripeRepository: SubscriberStripeRepository = mockk()
    private val userRepository: UsersRepository = mockk()
    private val tierRepository: TiersRepository = mockk()

    private val controller = spyk(
        StripeController(
            production = false,
            hostname = "devel.herohero.co",
            stripe = mockk(),
            subscriberRepository = subscriberRepository,
            subscriberStripeRepository = subscriberStripeRepository,
            tierRepository = tierRepository,
            userRepository = userRepository,
            categoriesRepository = mockk(),
            stripePublicKeys = mapOf(Currency.EUR to "*********"),
            stripePaymentsService = mockk(),
            stripeAccountService = mockk(),
            subscriptionService = mockk(),
            stripePaymentMethods = mockk(),
            countryToVatMapping = VatMapping(mapOf()),
            cancelSubscriptionCommandService = mockk(),
            subscribersCollection = mockk(),
            invoicesCollection = mockk(),
        ),
    )

    private val testTimestamp = zonedDateTimeOf("2020-01-01T00:00:00Z[UTC]").toInstant()
    private val user1: User
        get() = User("john-doe", path = "jane", name = "jane", creator = Creator(tierId = "EUR05")).also {
            it.created = testTimestamp
        }
    private val user2: User
        get() = User("jane-doe", path = "jane", name = "jane", creator = Creator(tierId = "EUR05")).also {
            it.created = testTimestamp
            it.creator = Creator(
                tierId = "EUR05",
                stripeAccountId = "ac_123456",
                stripeAccountActive = true,
                stripeAccountOnboarded = true,
                stripeRequirements = null,
                currency = Currency.EUR,
            )
        }

    private val tier05 = Tier.ofId("EUR05")
    private val tier10 = Tier.ofId("EUR10")

    @BeforeEach
    fun init() {
        every { tierRepository.defaultTier } returns tier05
        every { tierRepository[tier05.id] } returns tier05
        every { tierRepository[tier10.id] } returns tier10
    }

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun testStripeSubscriber() {
        val paymentMethodId = "pm_*********"
        val request = Request(Method.POST, "/v1/users/${user1.id}/subscriptions/${user2.id}/payments")
            .withAccessTokenCookie(jwtFor(user1.id))
            .body(
                CreateSubscriptionPostBody(
                    paymentMethodId = paymentMethodId,
                ).toJson(),
            )

        val paymentResponse = PaymentResponse(
            PaymentResponseAttributes(
                status = PaymentIntentStatus.SUCCEEDED,
                subscriptionStatus = SubscriberStatus.ACTIVE,
                paymentIntentClientSecret = null,
                createdAt = Instant.now(),
                couponCode = null,
            ),
            PaymentResponseRelationships(
                user = UserDtoRelationship(user1.id),
                post = null,
                creator = UserDtoRelationship(user2.id),
            ),
        )

        every { userRepository.get(request, user1.id) } returns user1
        every { userRepository.get(user2.id) } returns user2
        @Suppress("ktlint:standard:max-line-length")
        every {
            subscriberStripeRepository.subscribe(
                user1,
                user2,
                paymentMethodId,
                null,
                null,
            )
        } returns paymentResponse

        val response = controller.routeCreateSubscription(request)

        assertEquals(
            paymentResponse,
            String(response.body.payload.array()).fromJson<PaymentResponse>(),
        )
        assertEquals(Status.OK, response.status)
    }

    @Test
    fun testStripeSubscriberPaymentRefused() {
        val paymentMethodId = "pm_*********"
        val request = Request(Method.POST, "/v1/users/${user1.id}/subscriptions/${user2.id}/payments")
            .withAccessTokenCookie(jwtFor(user1.id))
            .body(
                CreateSubscriptionPostBody(
                    paymentMethodId = paymentMethodId,
                ).toJson(),
            )

        val paymentResponse = PaymentResponse(
            PaymentResponseAttributes(
                status = PaymentIntentStatus.REQUIRES_PAYMENT_METHOD,
                subscriptionStatus = SubscriberStatus.INCOMPLETE,
                paymentIntentClientSecret = null,
                createdAt = Instant.now(),
                couponCode = null,
            ),
            PaymentResponseRelationships(
                user = UserDtoRelationship(user1.id),
                post = null,
                creator = UserDtoRelationship(user2.id),
            ),
        )

        every { userRepository.get(request, user1.id) } returns user1
        every { userRepository.get(user2.id) } returns user2
        @Suppress("ktlint:standard:max-line-length")
        every {
            subscriberStripeRepository.subscribe(
                user1,
                user2,
                paymentMethodId,
                null,
                null,
            )
        } returns paymentResponse

        val response = controller.routeCreateSubscription(request)

        assertEquals(
            paymentResponse,
            String(response.body.payload.array()).fromJson<PaymentResponse>(),
        )
        assertEquals(Status.UNPROCESSABLE_ENTITY, response.status)
    }
}
