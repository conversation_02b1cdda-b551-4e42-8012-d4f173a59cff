package hero.api.post.service

import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.model.topics.PostState
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections.subscribersCollection
import hero.test.TestRepositories.postRepository
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class CommentQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetComments {
        @Test
        fun `should return comments for a top level post with subscription information for a subscriber`() {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val comment1 = testHelper.createPost("adrian", parentId = post.id, text = "comment1")
            val comment2 = testHelper.createPost("kamil", parentId = post.id, text = "comment2")

            val subscription = testHelper.createSubscriber("filip", "pepa")

            val comments = underTest.execute(GetComments("pepa", post.id, PageRequest()))

            assertThat(comments.content).hasSize(2)
            assertThat(comments.hasNext).isFalse()

            assertThat(comments.content).filteredOn { it.comment.id == comment1.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "comment1" && it.subscriptionInfo == subscription && it.postAuthor == "filip"
                }

            assertThat(comments.content).filteredOn { it.comment.id == comment2.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "comment2" && it.subscriptionInfo == subscription && it.postAuthor == "filip"
                }
        }

        @Test
        fun `should sort by given sort direction`() {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val now = Instant.now()
            val comment1 = testHelper.createPost(
                "adrian",
                parentId = post.id,
                text = "comment1",
                publishedAt = now - 5.seconds,
            )
            val comment2 = testHelper.createPost(
                "kamil",
                parentId = post.id,
                text = "comment2",
                publishedAt = now - 10.seconds,
            )

            val subscription = testHelper.createSubscriber("filip", "pepa")

            val ascPageRequest = PageRequest(sort = Sort(direction = Sort.Direction.ASC))
            val ascComments = underTest.execute(GetComments("pepa", post.id, ascPageRequest))

            assertThat(ascComments.content).containsExactly(
                CommentWithSubscriptionInfo(comment2, subscription, "filip"),
                CommentWithSubscriptionInfo(comment1, subscription, "filip"),
            )

            val descPageRequest = PageRequest(sort = Sort(direction = Sort.Direction.DESC))
            val descComments = underTest.execute(GetComments("pepa", post.id, descPageRequest))

            assertThat(descComments.content).containsExactly(
                CommentWithSubscriptionInfo(comment1, subscription, "filip"),
                CommentWithSubscriptionInfo(comment2, subscription, "filip"),
            )
        }

        @Test
        fun `should return nested comments for a comment with subscription information for a subscriber`() {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val comment = testHelper.createPost("martin", parentId = post.id, text = "top-level-comment")
            val nestedComment1 = testHelper.createPost("adrian", parentId = comment.id, text = "nested1")
            val nestedComment2 = testHelper.createPost("kamil", parentId = comment.id, text = "nested2")

            val subscription = testHelper.createSubscriber("filip", "pepa")

            val comments = underTest.execute(GetComments("pepa", comment.id, PageRequest()))

            assertThat(comments.content).hasSize(2)
            assertThat(comments.hasNext).isFalse()

            assertThat(comments.content).filteredOn { it.comment.id == nestedComment1.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "nested1" && it.subscriptionInfo == subscription && it.postAuthor == "filip"
                }

            assertThat(comments.content).filteredOn { it.comment.id == nestedComment2.id }
                .hasSize(1)
                .allMatch {
                    it.comment.text == "nested2" && it.subscriptionInfo == subscription && it.postAuthor == "filip"
                }
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["SCHEDULED", "PROCESSING", "PUBLISHED"])
        fun `should return comments for the post creator if the post is in all allowed states`(postState: PostState) {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = postState)

            val comment1 = testHelper.createPost("adrian", parentId = post.id, text = "comment1")
            val comment2 = testHelper.createPost("kamil", parentId = post.id, text = "comment2")

            val comments = underTest.execute(GetComments("filip", post.id, PageRequest()))

            assertThat(comments.content).hasSize(2)
            assertThat(comments.hasNext).isFalse()

            assertThat(comments.content).filteredOn { it.comment.id == comment1.id }
                .hasSize(1)
                .allMatch { it.comment.text == "comment1" && it.subscriptionInfo == null && it.postAuthor == "filip" }

            assertThat(comments.content).filteredOn { it.comment.id == comment2.id }
                .hasSize(1)
                .allMatch { it.comment.text == "comment2" && it.subscriptionInfo == null && it.postAuthor == "filip" }
        }

        @Test
        fun `should paginate using after cursor and order by publishedAt descending`() {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            val now = Instant.now()
            val comment1 = testHelper.createPost("adrian", parentId = post.id, publishedAt = now)
            val comment2 = testHelper.createPost("kamil", parentId = post.id, publishedAt = now - 5.seconds)
            val comment3 = testHelper.createPost("olda", parentId = post.id, publishedAt = now - 10.seconds)

            val requesterSubscription = testHelper.createSubscriber("filip", "pepa")

            val firstComment = underTest.execute(GetComments("pepa", post.id, PageRequest(pageSize = 1)))

            assertThat(firstComment.content)
                .containsExactly(CommentWithSubscriptionInfo(comment1, requesterSubscription, "filip"))
            assertThat(firstComment.hasNext).isTrue()

            val secondPageRequest = PageRequest(pageSize = 1, afterCursor = firstComment.nextPageable.afterCursor)
            val secondComment = underTest.execute(GetComments("pepa", post.id, secondPageRequest))

            assertThat(secondComment.content)
                .containsExactly(CommentWithSubscriptionInfo(comment2, requesterSubscription, "filip"))
            assertThat(secondComment.hasNext).isTrue()

            val thirdPageRequest = PageRequest(pageSize = 1, afterCursor = secondComment.nextPageable.afterCursor)
            val thirdComment = underTest.execute(GetComments("pepa", post.id, thirdPageRequest))

            assertThat(thirdComment.content)
                .containsExactly(CommentWithSubscriptionInfo(comment3, requesterSubscription, "filip"))
            assertThat(thirdComment.hasNext).isFalse()

            val beforePageRequest = PageRequest(pageSize = 2, beforeCursor = thirdComment.nextPageable.beforeCursor)
            val firstTwoComments = underTest.execute(GetComments("pepa", post.id, beforePageRequest))
            assertThat(firstTwoComments.content)
                .containsExactly(
                    CommentWithSubscriptionInfo(comment1, requesterSubscription, "filip"),
                    CommentWithSubscriptionInfo(comment2, requesterSubscription, "filip"),
                )
            assertThat(firstTwoComments.hasNext).isFalse()
        }

        @Test
        fun `should not return any comments if a user is not subscriber of the post creator`() {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = PostState.PUBLISHED)

            testHelper.createPost("adrian", parentId = post.id, text = "comment1")
            testHelper.createPost("kamil", parentId = post.id, text = "comment2")

            val comments = underTest.execute(GetComments("pavel", post.id, PageRequest()))

            assertThat(comments.content).isEmpty()
            assertThat(comments.hasNext).isFalse()
            assertThat(comments.nextPageable).isEqualTo(PageRequest())
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["SCHEDULED", "PROCESSING"])
        fun `should throw forbidden if post is processing, scheduled and user is not the owner`(postState: PostState) {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = postState)

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetComments("pavel", post.id, PageRequest()))
            }
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["DELETED", "REVISION"])
        fun `should throw not found if post is deleted or is a revision`(postState: PostState) {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("filip", state = postState)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(GetComments("pavel", post.id, PageRequest()))
            }
        }
    }

    @Nested
    inner class GetComment {
        @Test
        fun `should fetch a reply comment, its parent and root parent`() {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("cestmir")
            val comment = testHelper.createPost("filip", parentId = post.id)
            val replyComment = testHelper.createPost("petr", parentId = comment.id)
            val siblingComment = testHelper.createPost("ondra", parentId = comment.id, siblingId = replyComment.id)

            val result = underTest.execute(GetComment(siblingComment.id, "filip"))

            assertThat(result)
                .isInstanceOf(ReplyData::class.java)
                .isEqualTo(
                    ReplyData(
                        comment = siblingComment,
                        rootPost = post,
                        subscriptionInfo = null,
                        parent = comment,
                    ),
                )
        }

        @Test
        fun `should fetch comment and its post parent and subscription info`() {
            val underTest = CommentQueryService(postRepository, subscribersCollection)
            val post = testHelper.createPost("cestmir")
            val comment = testHelper.createPost("filip", parentId = post.id)
            val subscription = testHelper.createSubscriber("cestmir", "filip")

            val result = underTest.execute(GetComment(comment.id, "filip"))

            assertThat(result)
                .isInstanceOf(CommentData::class.java)
                .isEqualTo(CommentData(comment = comment, rootPost = post, subscriptionInfo = subscription))
        }
    }
}
