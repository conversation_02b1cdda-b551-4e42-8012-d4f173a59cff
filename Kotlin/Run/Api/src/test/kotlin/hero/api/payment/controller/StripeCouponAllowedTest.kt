package hero.api.payment.controller

import hero.model.Creator
import hero.model.SupportCounts
import hero.model.User
import hero.model.UserCompany
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class StripeCouponAllowedTest {
    private fun testUser(counts: SupportCounts): User =
        User(
            id = "asdf",
            name = "asdf",
            bio = "bio",
            bioHtml = "<strong>bio</strong>",
            bioEn = "bio in en",
            bioHtmlEn = "<strong>bio in en</strong>",
            email = "<EMAIL>",
            path = "andmul",
            creator = Creator("EUR05"),
            hasRssFeed = false,
            company = UserCompany(),
            counts = counts,
        )

    @Test
    fun `creators that are too new-fresh cannot sell coupons`() {
        val notEnoughSubs = testUser(SupportCounts(supporters = 9, incomes = 50, invoices = 5))
        assertEquals(false, notEnoughSubs.hasGiftsAllowed)

        val notEnoughIncomes = testUser(SupportCounts(supporters = 10, incomes = 49, invoices = 5))
        assertEquals(false, notEnoughIncomes.hasGiftsAllowed)

        val notEnoughInvoices = testUser(SupportCounts(supporters = 10, incomes = 50, invoices = 0))
        assertEquals(false, notEnoughInvoices.hasGiftsAllowed)

        val justEnoughWithAnInvoice = testUser(SupportCounts(supporters = 10, incomes = 50, invoices = 1))
        assertEquals(true, justEnoughWithAnInvoice.hasGiftsAllowed)

        val bigIncomesNoInvoice = testUser(SupportCounts(supporters = 50, incomes = 150, invoices = 0))
        assertEquals(true, bigIncomesNoInvoice.hasGiftsAllowed)
    }
}
