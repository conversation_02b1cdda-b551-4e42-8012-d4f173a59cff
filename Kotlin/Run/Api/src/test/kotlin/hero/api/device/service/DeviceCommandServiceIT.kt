package hero.api.device.service

import hero.baseutils.minus
import hero.baseutils.plus
import hero.model.DeviceType
import hero.sql.jooq.tables.Device.DEVICE
import hero.sql.jooq.tables.records.DeviceRecord
import hero.test.IntegrationTest
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class DeviceCommandServiceIT : IntegrationTest() {
    @Test
    fun `should register new device`() {
        val now = Instant.ofEpochSecond(1734689400)
        val testClock = TestClock(now)
        val underTest = DeviceCommandService(lazyTestContext, testClock)

        underTest.execute(
            RegisterDevice(
                userId = "cestmir",
                deviceId = "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
                appVersion = "Herohero@1.0.11",
                registrationToken = "registration-token",
                deviceType = DeviceType.IOS,
            ),
        )

        val expectedDevice = DeviceRecord(
            "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
            "cestmir",
            "Herohero@1.0.11",
            "registration-token",
            "IOS",
            now,
            now,
            null,
        )
        assertThat(testContext.selectFrom(DEVICE).fetch()).containsExactly(expectedDevice)
    }

    @Test
    fun `should update only registration token and version on already registered device`() {
        val now = Instant.ofEpochSecond(1734689400)
        val testClock = TestClock(now)
        val underTest = DeviceCommandService(lazyTestContext, testClock)

        // we register new device
        underTest.execute(
            RegisterDevice(
                userId = "cestmir",
                deviceId = "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
                appVersion = "Herohero@1.0.11",
                registrationToken = "old-registration-token",
                deviceType = DeviceType.IOS,
            ),
        )
        val oldDeviceData = testContext.selectFrom(DEVICE).fetch()

        testClock += 10.seconds
        // update the already registered device
        underTest.execute(
            RegisterDevice(
                userId = "cestmir",
                deviceId = "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
                appVersion = "Herohero@1.0.14",
                registrationToken = "new-registration-token",
                deviceType = DeviceType.ANDROID,
            ),
        )

        val expectedDevice = DeviceRecord(
            "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
            "cestmir",
            // only things that should change are the version and token
            "Herohero@1.0.14",
            "new-registration-token",
            // device type should never change
            "IOS",
            now,
            now.plusSeconds(10),
            null,
        )
        assertThat(testContext.selectFrom(DEVICE).fetch()).containsExactly(expectedDevice)
        assertThat(oldDeviceData).isNotEqualTo(expectedDevice)
    }

    @Test
    fun `registering disabled disabled device should enable it`() {
        val now = Instant.ofEpochSecond(1734689400)
        val testClock = TestClock(now)
        val underTest = DeviceCommandService(lazyTestContext, testClock)

        val deviceRecord = DeviceRecord(
            "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
            "cestmir",
            "Herohero@1.0.11",
            "registration-token",
            "IOS",
            now - 5.seconds,
            now - 5.seconds,
            now,
        )
        testContext.insertInto(DEVICE).set(deviceRecord).execute()

        testClock += 5.seconds

        underTest.execute(
            RegisterDevice(
                userId = "cestmir",
                deviceId = "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
                appVersion = "Herohero@1.0.11",
                registrationToken = "new-registration-token",
                deviceType = DeviceType.IOS,
            ),
        )

        assertThat(testContext.selectFrom(DEVICE).fetch()).containsExactly(
            deviceRecord.copy().apply {
                deviceId = "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a"
                userId = "cestmir"
                firebaseRegistrationToken = "new-registration-token"
                disabledAt = null
                updatedAt = now + 5.seconds
            },
        )
    }

    @Test
    fun `should disable existing device`() {
        val now = Instant.ofEpochSecond(1734689400)
        val testClock = TestClock(now)
        val underTest = DeviceCommandService(lazyTestContext, testClock)

        val deviceRecord = DeviceRecord(
            "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
            "cestmir",
            "Herohero@1.0.11",
            "registration-token",
            "IOS",
            now,
            now,
            null,
        )
        testContext.insertInto(DEVICE).set(deviceRecord).execute()

        testClock += 5.seconds

        underTest.execute(
            DisableDevice(
                userId = "cestmir",
                deviceId = "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a",
            ),
        )

        assertThat(testContext.selectFrom(DEVICE).fetch()).containsExactly(
            deviceRecord.copy().apply {
                deviceId = "8d48a3b1-0abc-4f41-8fdb-b3fd6d7ce52a"
                userId = "cestmir"
                disabledAt = now + 5.seconds
            },
        )
    }
}
