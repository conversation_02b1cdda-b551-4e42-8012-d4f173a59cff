package hero.api.notification.service

import hero.baseutils.minus
import hero.exceptions.http.ForbiddenException
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class NotificationCommandServiceIT : IntegrationTest() {
    @Nested
    inner class UpdateNotification {
        @Test
        fun `should update notification`() {
            val underTest = NotificationCommandService(TestRepositories.notificationRepository, lazyTestContext)
            testHelper.createUser("emer")
            val originalNotification = testHelper.createNotification("emer", checkedAt = null, seenAt = null)
            val checkedAt = Instant.ofEpochSecond(1000)
            val seenAt = Instant.ofEpochSecond(1001)

            underTest.execute(UpdateNotification(originalNotification.id, "emer", checkedAt, seenAt))

            val expectedNotification = originalNotification.copy(checkedAt = checkedAt, seenAt = seenAt)
            val notifications = TestRepositories.notificationRepository.find { this }
            assertThat(notifications).containsExactly(expectedNotification)
        }

        @Test
        fun `should do nothing if checkedAt and seenAt are null`() {
            val underTest = NotificationCommandService(TestRepositories.notificationRepository, lazyTestContext)
            val checkedAt = Instant.ofEpochSecond(1000)
            val seenAt = Instant.ofEpochSecond(1001)
            testHelper.createUser("emer")
            val originalNotification = testHelper.createNotification("emer", checkedAt = checkedAt, seenAt = seenAt)

            underTest.execute(UpdateNotification(originalNotification.id, "emer", null, null))

            val notifications = TestRepositories.notificationRepository.find { this }
            assertThat(notifications).containsExactly(originalNotification)
        }

        @Test
        fun `should throw if user attempts to update another user's notification`() {
            val underTest = NotificationCommandService(TestRepositories.notificationRepository, lazyTestContext)
            testHelper.createUser("emer")
            val originalNotification = testHelper.createNotification("emer")

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(UpdateNotification(originalNotification.id, "patrick", null, null))
            }
        }
    }

    @Nested
    inner class MarkNotificationsAsSeen {
        @Test
        fun `should mark users notifications as seen`() {
            val now = Instant.ofEpochSecond(1741700659)
            val testClock = TestClock(now)
            val underTest = NotificationCommandService(
                TestRepositories.notificationRepository,
                lazyTestContext,
                testClock,
            )

            testHelper.createUser("emer")
            val notification1 = testHelper.createNotification("emer", seenAt = now - 5.seconds, id = "1")
            val notification2 = testHelper.createNotification("emer", seenAt = null, id = "2")

            testHelper.createUser("johny")
            val notification3 = testHelper.createNotification("johny", seenAt = null)

            underTest.execute(MarkNotificationsAsSeen("emer"))

            val notifications = TestRepositories.notificationRepository.find { this }
            assertThat(notifications).containsExactlyInAnyOrder(
                notification1,
                notification2.copy(seenAt = now),
                notification3,
            )
        }
    }
}
