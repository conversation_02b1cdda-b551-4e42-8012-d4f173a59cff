package hero.api.post.service

import hero.api.user.service.UserRelationsService
import hero.api.user.service.UserSubscriptionRelation
import hero.baseutils.truncated
import hero.gcloud.TypedCollectionReference
import hero.gcloud.get
import hero.model.CouponMethod
import hero.model.Subscriber
import hero.model.SubscriberStatus.ACTIVE
import hero.model.SubscriberType
import hero.model.SubscriptionRelationType
import hero.model.SubscriptionRelationType.HIMSELF
import hero.model.SubscriptionRelationType.IS_SUBSCRIBED_TO
import hero.model.SubscriptionRelationType.IS_SUBSCRIBED_TO_SAME_CREATOR
import hero.test.gcloud.FirestoreTestDatabase.testFirestore
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import java.time.Instant

class UserRelationsServiceIT {
    private val subscribersRepository = TypedCollectionReference<Subscriber>(
        testFirestore["test-${Subscriber.collectionName}"],
    )
    private val userRelationsService = UserRelationsService(subscribersRepository)

    private val userId1 = "user_1"
    private val userId2 = "user_2"

    private val creatorId1 = "creator_1"
    private val creatorId2 = "creator_2"

    private val subscriber1 = Subscriber(
        userId = userId1,
        creatorId = creatorId1,
        status = ACTIVE,
        subscribed = Instant.now().truncated(),
        subscriberType = SubscriberType.STRIPE,
        couponMethod = CouponMethod.VOUCHER,
        tierId = "EUR05",
    )
    private val subscriber2 = Subscriber(
        userId = userId2,
        creatorId = creatorId1,
        status = ACTIVE,
        subscribed = Instant.now().truncated(),
        subscriberType = SubscriberType.STRIPE,
        couponMethod = CouponMethod.VOUCHER,
        tierId = "EUR05",
    )

    @AfterEach
    fun afterEach() {
        subscribersRepository[subscriber1.id].delete()
        subscribersRepository[subscriber2.id].delete()
    }

    @Test
    fun userDoesNotSubscribeButCanAccessThemselves() {
        assertThat(userRelationsService.userRelationsTo(userId1, true, listOf(creatorId1)))
            .isEqualTo(setOf(UserSubscriptionRelation(userId1, HIMSELF)))
        assertThat(userRelationsService.canInteract(userId1, true, listOf(creatorId1))).isFalse()
    }

    @Test
    fun creatorIsNotSubscribedButCanAccessThemselves() {
        assertThat(userRelationsService.userRelationsTo(creatorId1, true, listOf(userId1)))
            .isEqualTo(setOf(UserSubscriptionRelation(creatorId1, HIMSELF)))
        assertThat(userRelationsService.canInteract(creatorId1, true, listOf(userId1))).isFalse()
    }

    @Test
    fun userSubscribes() {
        subscribersRepository[subscriber1.id].set(subscriber1)
        assertThat(userRelationsService.userRelationsTo(userId1, true, listOf(creatorId1)))
            .isEqualTo(
                setOf(
                    UserSubscriptionRelation(userId1, HIMSELF),
                    UserSubscriptionRelation(creatorId1, IS_SUBSCRIBED_TO),
                ),
            )
        assertThat(userRelationsService.canInteract(userId1, true, listOf(creatorId1))).isTrue()
    }

    @Test
    fun creatorIsSubscribed() {
        subscribersRepository[subscriber1.id].set(subscriber1)
        assertThat(userRelationsService.userRelationsTo(userId1, true, listOf(creatorId1)))
            .isEqualTo(
                setOf(
                    UserSubscriptionRelation(userId1, HIMSELF),
                    UserSubscriptionRelation(creatorId1, IS_SUBSCRIBED_TO),
                ),
            )
        assertThat(userRelationsService.canInteract(userId1, true, listOf(creatorId1))).isTrue()
    }

    @Test
    fun creatorIsSubscribedByTwo() {
        subscribersRepository[subscriber1.id].set(subscriber1)
        subscribersRepository[subscriber2.id].set(subscriber2)
        // note that users are allowed to write to the whole community (userId1<=>userId2<=>creator1)
        assertThat(
            userRelationsService.userRelationsTo(userId1, true, listOf(userId2, creatorId1, creatorId2)),
        ).isEqualTo(
            setOf(
                UserSubscriptionRelation(userId1, HIMSELF),
                UserSubscriptionRelation(userId2, IS_SUBSCRIBED_TO_SAME_CREATOR, creatorId1),
                UserSubscriptionRelation(creatorId1, IS_SUBSCRIBED_TO),
            ),
        )
        // … and outside of community (isCommunity=false)
        assertThat(
            userRelationsService.userRelationsTo(userId1, false, listOf(userId2, creatorId1, creatorId2)),
        ).isEqualTo(
            setOf(UserSubscriptionRelation(userId1, HIMSELF), UserSubscriptionRelation(creatorId1, IS_SUBSCRIBED_TO)),
        )

        // the same for user2 in community
        assertThat(
            userRelationsService.userRelationsTo(userId2, true, listOf(userId1, creatorId1, creatorId2)),
        ).isEqualTo(
            setOf(
                UserSubscriptionRelation(userId1, IS_SUBSCRIBED_TO_SAME_CREATOR, creatorId1),
                UserSubscriptionRelation(userId2, HIMSELF),
                UserSubscriptionRelation(creatorId1, IS_SUBSCRIBED_TO),
            ),
        )
        // … and outside of community (isCommunity=false)
        assertThat(
            userRelationsService.userRelationsTo(userId2, false, listOf(userId2, creatorId1, creatorId2)),
        ).isEqualTo(
            setOf(
                UserSubscriptionRelation(userId2, HIMSELF),
                UserSubscriptionRelation(creatorId1, IS_SUBSCRIBED_TO),
            ),
        )
        // … and the same for creator
        assertThat(
            userRelationsService.userRelationsTo(creatorId1, true, listOf(userId1, userId2)),
        ).isEqualTo(
            setOf(
                UserSubscriptionRelation(userId1, SubscriptionRelationType.IS_SUBSCRIBED_BY),
                UserSubscriptionRelation(userId2, SubscriptionRelationType.IS_SUBSCRIBED_BY),
                UserSubscriptionRelation(creatorId1, HIMSELF),
            ),
        )
        assertThat(
            userRelationsService.userRelationsTo(creatorId1, false, listOf(userId1, userId2)),
        ).isEqualTo(
            setOf(UserSubscriptionRelation(creatorId1, HIMSELF)),
        )
        // user1 can access the creator
        assertThat(userRelationsService.canInteract(userId1, true, listOf(creatorId1))).isTrue()
        // user1 can access the other user2
        assertThat(userRelationsService.canInteract(userId1, true, listOf(userId2))).isTrue()
        // user1 can access other user2 if not community related
        assertThat(userRelationsService.canInteract(userId1, false, listOf(userId2))).isFalse()
        // user1 can access other user2 and creator
        assertThat(userRelationsService.canInteract(userId1, true, listOf(userId2, creatorId1))).isTrue()
        // user1 can access other user2 if not community related
        assertThat(userRelationsService.canInteract(userId1, false, listOf(userId2, creatorId1))).isFalse()
        // creator1 can access both users in community
        assertThat(userRelationsService.canInteract(creatorId1, true, listOf(userId1, userId2))).isTrue()
        // but not outside of it
        assertThat(userRelationsService.canInteract(creatorId1, false, listOf(userId1, userId2))).isFalse()
        // user1 cannot write the other creator
        assertThat(userRelationsService.canInteract(userId1, true, listOf(creatorId2))).isFalse()
        // user1 cannot access both creators at the same time
        assertThat(userRelationsService.canInteract(userId1, true, listOf(creatorId1, creatorId2))).isFalse()
    }
}
