package hero.api.statistics.service

import hero.baseutils.minusDays
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

class SubscriberStatisticsQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetSubscriberStatistics {
        @Test
        fun `should use precalculated data and calculate only for missing days`() {
            val underTest = SubscriberStatisticsQueryService(lazyTestContext)
            testContext.execute(
                """
insert into public.subscription (stripe_id, customer_id, creator_id, started_at, ends_at, status, currency, user_id, creator_country, tier_id, price_cents, created_at, updated_at, cancelled_at, ended_at, cancellation_reason)
values  ('sub_1MmeRmB6ZCHekl2RyKbVBado', 'cus_NXjurDXjGBJjCd', 'cestmirstrakatyheroherorpkjaolu', now() , now() + '1 day'::interval, 'active', 'EUR', 'jitkasuchanovavsdybykx', null, 'EUR05', 500, '2024-01-17 14:40:10.670571', '2024-03-08 15:09:00.577723', now(), null, 'cancellation_requested'),
        -- this subscription is ignored
        ('sub_1MClhxB6ZCHekl2RQh5hQXDS', 'cus_J0gOkJpN1jWOD0', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'incomplete', 'EUR', 'martinseverasbinvija', null, 'EUR05', 500, '2024-01-08 15:09:09.026134', '2024-03-08 15:08:42.530235', null, null, null),
        ('sub_1Os51rB6ZCHekl2RkOZdyUQO', 'cus_PhTzE3MsnCxJ56', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'past_due', 'EUR', 'josefstehliktlqormzo', null, 'EUR06', 600, '2024-03-08 15:08:16.306886', '2024-03-08 15:08:16.306886', null, null, null),
        ('sub_1Os50iB6ZCHekl2RBtkcLuPD', 'cus_PhTyOxFkf4lZOo', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'active', 'EUR', 'prrfaqtwurcrc', null, 'EUR06', 600, '2024-03-08 15:07:05.488011', '2024-03-08 15:07:05.488011', null, null, null),
        ('sub_1OVBznB6ZCHekl2RgyFBF2Cs', 'cus_PJpexevXkRRgAo', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'active', 'EUR', 'hvikcpwitdttb', null, 'EUR06', 600, '2024-01-05 17:23:16.644031', '2024-03-08 15:04:50.163454', null, null, null);
                """,
            )
            testContext.execute(
                """
insert into daily_subscriber_statistics(date, creator_id, total_subscribers, active_subscribers, subscribed, unsubscribed, total_income_cents)
values (now() - '5 day'::interval, 'cestmirstrakatyheroherorpkjaolu', 193, 181, 20, 3, 96500),
       (now() - '4 day'::interval, 'cestmirstrakatyheroherorpkjaolu', 201, 189, 8, 10, 100500),
       (now() - '3 day'::interval, 'cestmirstrakatyheroherorpkjaolu', 208, 196, 7, 5, 104000),
       (now() - '2 day'::interval, 'cestmirstrakatyheroherorpkjaolu', 228, 216, 20, 12, 114000),
       (now() - '1 day'::interval, 'cestmirstrakatyheroherorpkjaolu', 253, 241, 25, 9, 126500)
                """.trimIndent(),
            )
            testContext.execute(
                """
insert into charge (stripe_id, customer_id, amount, amount_captured, amount_refunded, payment_intent_id, status, stripe_created_at, payment_method_id, succeeded_charges, currency, refunded, disputed, creator_id, transfer_amount)
values  ('ch_3Lpv3bB6ZCHekl2R0loMdDye', 'cus_MZ39E6SytiH5Uw', 500, 500, 0, 'pi_3Lpv3bB6ZCHekl2R0YCp8eNX', 'succeeded', now(), 'pm_1Lpv3WB6ZCHekl2Rro9MR5PF', 1, 'EUR', false, false, 'cestmirstrakatyheroherorpkjaolu', 450)
                """.trimIndent(),
            )

            val now = Instant.now()
            val query = GetSubscriberStatistics("cestmirstrakatyheroherorpkjaolu", now.minusDays(5), now)
            val result = underTest.execute(query)

            assertThat(result).containsExactly(
                // this data except the last one should be fetched from precalculated daily_subscriber_statistics
                data(now.minusDays(5), 193, 181, 20, 3, 96500),
                data(now.minusDays(4), 201, 189, 8, 10, 100500),
                data(now.minusDays(3), 208, 196, 7, 5, 104000),
                data(now.minusDays(2), 228, 216, 20, 12, 114000),
                data(now.minusDays(1), 253, 241, 25, 9, 126500),
                // this line should be calculated from the subscription table
                data(now.minusDays(0), 4, 3, 4, 1, 450),
            )
        }
    }
}

private fun data(
    date: Instant,
    totalSub: Int,
    activeSub: Int,
    createdOnDay: Int,
    cancelledOnDay: Int,
    totalIncomeCents: Int,
) = SubscriberStatsData(
    LocalDate.ofInstant(date, ZoneOffset.UTC),
    totalSub,
    activeSub,
    createdOnDay,
    cancelledOnDay,
    totalIncomeCents,
)
