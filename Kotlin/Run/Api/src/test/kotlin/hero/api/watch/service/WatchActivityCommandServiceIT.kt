package hero.api.watch.service

import hero.api.user.service.gjirafaAsset
import hero.baseutils.plus
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.model.PostAsset
import hero.sql.jooq.Tables.WATCH_ACTIVITY
import hero.sql.jooq.tables.records.WatchActivityRecord
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class WatchActivityCommandServiceIT : IntegrationTest() {
    @Test
    fun `should update watch activity twice and then mark as finished, then should create new watch activity`() {
        val now = Instant.ofEpochSecond(1729514035)
        val testClock = TestClock(now)
        val underTest = WatchActivityCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
            testClock,
        )

        testHelper.createUser("honzik")
        testHelper.createUser("cestmir")
        testHelper.createSubscription(creatorId = "cestmir", userId = "honzik")
        // some other post
        testHelper.createPost(
            "cestmir",
            assets = listOf(PostAsset(gjirafa = gjirafaAsset.copy(id = "another-id"))),
        )
        val post = testHelper.createPost(
            "cestmir",
            assets = listOf(
                // this asset is not used
                PostAsset(gjirafa = gjirafaAsset.copy(id = "vjsnkuac", duration = 650.3)),
                PostAsset(gjirafa = gjirafaAsset.copy(id = "vjsnluej", duration = 392.3)),
            ),
        )

        underTest.execute(
            RecordWatchActivity(
                userId = "honzik",
                sessionId = "1234",
                assetId = "vjsnluej",
                postId = null,
                timestamp = 25.0,
            ),
        )

        fun WatchActivityRecord.assertImmutables() {
            assertThat(this[WATCH_ACTIVITY.USER_ID]).isEqualTo("honzik")
            assertThat(this[WATCH_ACTIVITY.POST_ID]).isEqualTo(post.id)
            assertThat(this[WATCH_ACTIVITY.CREATOR_ID]).isEqualTo("cestmir")
            assertThat(this[WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE]).isTrue()
            assertThat(this[WATCH_ACTIVITY.ASSET_ID]).isEqualTo("vjsnluej")
            assertThat(this[WATCH_ACTIVITY.SESSION_ID]).isEqualTo("1234")
        }

        with(testContext.selectFrom(WATCH_ACTIVITY).fetchSingle()) {
            assertImmutables()
            assertThat(this[WATCH_ACTIVITY.TIMESTAMP]).isEqualTo(25.0)
            assertThat(this[WATCH_ACTIVITY.WATCHED_AT]).isEqualTo(now)
            assertThat(this[WATCH_ACTIVITY.CREATED_AT]).isEqualTo(now)
            assertThat(this[WATCH_ACTIVITY.FINISHED]).isFalse()
        }

        testClock += 5.seconds
        underTest.execute(
            RecordWatchActivity(
                userId = "honzik",
                sessionId = "1234",
                assetId = "vjsnluej",
                postId = null,
                timestamp = 28.0,
            ),
        )

        with(testContext.selectFrom(WATCH_ACTIVITY).fetchSingle()) {
            assertImmutables()
            assertThat(this[WATCH_ACTIVITY.TIMESTAMP]).isEqualTo(28.0)
            assertThat(this[WATCH_ACTIVITY.WATCHED_AT]).isEqualTo(now + 5.seconds)
            assertThat(this[WATCH_ACTIVITY.CREATED_AT]).isEqualTo(now)
            assertThat(this[WATCH_ACTIVITY.FINISHED]).isFalse()
        }

        testClock += 5.seconds
        underTest.execute(
            RecordWatchActivity(
                userId = "honzik",
                sessionId = "1234",
                assetId = "vjsnluej",
                postId = null,
                timestamp = 392.3,
            ),
        )

        with(testContext.selectFrom(WATCH_ACTIVITY).fetchSingle()) {
            assertImmutables()
            assertThat(this[WATCH_ACTIVITY.TIMESTAMP]).isEqualTo(392.3)
            assertThat(this[WATCH_ACTIVITY.WATCHED_AT]).isEqualTo(now + 10.seconds)
            assertThat(this[WATCH_ACTIVITY.CREATED_AT]).isEqualTo(now)
            assertThat(this[WATCH_ACTIVITY.FINISHED]).isTrue()
        }

        // user honzik starts to watch the video again, new activity should be shown
        testClock += 5.seconds
        underTest.execute(
            RecordWatchActivity(
                userId = "honzik",
                sessionId = "1234",
                assetId = "vjsnluej",
                postId = post.id,
                timestamp = 15.3,
            ),
        )

        val results = testContext.selectFrom(WATCH_ACTIVITY).fetch()
        assertThat(results).hasSize(2)
        assertThat(results.map { it[WATCH_ACTIVITY.FINISHED] }.toSet()).isEqualTo(setOf(true, false))
        with(results.first { !it[WATCH_ACTIVITY.FINISHED] }) {
            assertImmutables()
            assertThat(this[WATCH_ACTIVITY.TIMESTAMP]).isEqualTo(15.3)
            assertThat(this[WATCH_ACTIVITY.WATCHED_AT]).isEqualTo(now + 15.seconds)
            assertThat(this[WATCH_ACTIVITY.CREATED_AT]).isEqualTo(now + 15.seconds)
            assertThat(this[WATCH_ACTIVITY.FINISHED]).isFalse()
        }
    }

    @Test
    fun `should validate that asset is part of post if post id passed directly`() {
        val underTest = WatchActivityCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
        )

        testHelper.createUser("honzik")
        testHelper.createUser("cestmir")
        testHelper.createSubscription(creatorId = "cestmir", userId = "honzik")
        val post = testHelper.createPost(
            "cestmir",
            assets = listOf(PostAsset(gjirafa = gjirafaAsset.copy(id = "vjsnluej"))),
        )

        assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
            underTest.execute(
                RecordWatchActivity(
                    userId = "honzik",
                    sessionId = "1234",
                    assetId = "non-existant-id",
                    postId = post.id,
                    timestamp = 15.3,
                ),
            )
        }
    }

    @Test
    fun `should validate that user subscribes creator`() {
        val underTest = WatchActivityCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
        )

        testHelper.createUser("honzik")
        testHelper.createUser("cestmir")
        val post = testHelper.createPost(
            "cestmir",
            assets = listOf(PostAsset(gjirafa = gjirafaAsset.copy(id = "vjsnluej"))),
        )

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest.execute(
                RecordWatchActivity(
                    userId = "honzik",
                    sessionId = "1234",
                    assetId = "vjsnluej",
                    postId = post.id,
                    timestamp = 15.3,
                ),
            )
        }
    }

    @Test
    fun `should do nothing if timestamp is set to zero or lower since zero means user hasn't started watching yet`() {
        val underTest = WatchActivityCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
        )

        underTest.execute(
            RecordWatchActivity(
                userId = "honzik",
                sessionId = "1234",
                assetId = "vjsnluej",
                postId = "post-id",
                timestamp = 0.0,
            ),
        )

        assertThat(testContext.fetchCount(WATCH_ACTIVITY)).isZero()
    }

    @Test
    fun `should remove previous watch activity because user has marked the asset as un-played`() {
        val underTest = WatchActivityCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
        )

        testHelper.createUser("honzik")
        testHelper.createUser("cestmir")
        testHelper.createSubscription(creatorId = "cestmir", userId = "honzik")
        val post = testHelper.createPost(
            "cestmir",
            assets = listOf(PostAsset(gjirafa = gjirafaAsset.copy(id = "vjsnluej", duration = 15.0))),
        )

        underTest.execute(
            RecordWatchActivity(
                userId = "honzik",
                sessionId = "1234",
                assetId = "vjsnluej",
                postId = post.id,
                timestamp = 10.0,
            ),
        )

        val activityCount = testContext.selectCount().from(WATCH_ACTIVITY).where(WATCH_ACTIVITY.DELETED_AT.isNull)
        assertThat(activityCount.fetchSingle().value1()).isOne()

        underTest.execute(
            RemoveWatchActivity(
                userId = "honzik",
                assetId = "vjsnluej",
                postId = post.id,
            ),
        )

        assertThat(activityCount.fetchSingle().value1()).isZero()

        // user should be able to record another watch activity on the same post asset again
        underTest.execute(
            RecordWatchActivity(
                userId = "honzik",
                sessionId = "1234",
                assetId = "vjsnluej",
                postId = post.id,
                timestamp = 10.0,
            ),
        )
        assertThat(activityCount.fetchSingle().value1()).isOne()
    }
}
