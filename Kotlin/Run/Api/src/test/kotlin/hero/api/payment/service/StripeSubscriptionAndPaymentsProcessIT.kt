package hero.api.payment.service

import com.stripe.exception.RateLimitException
import com.stripe.model.Customer
import com.stripe.model.PaymentMethod
import com.stripe.model.Subscription
import com.stripe.param.CustomerCreateParams
import com.stripe.param.PaymentMethodCreateParams
import com.stripe.param.PriceCreateParams
import com.stripe.param.PriceCreateParams.Recurring.Interval.DAY
import hero.api.user.repository.TiersRepository
import hero.baseutils.SystemEnv
import hero.baseutils.instantOf
import hero.baseutils.plusDays
import hero.baseutils.plusHours
import hero.baseutils.retryOn
import hero.gcloud.FirestoreRef
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.CZ_VAT_COUNTRY
import hero.model.Currency
import hero.model.Tier
import hero.model.topics.CardCreateType
import hero.stripe.service.StripeClients
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMapping
import hero.test.StripeHelper
import hero.test.advanceTo
import hero.test.euStripeConnectedAccount
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class StripeSubscriptionAndPaymentsProcessIT {
    private val isProduction = false
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction)
    private val tierRepository = TiersRepository(firestore.typedCollectionOf(Tier))
    private val testTier = tierRepository.defaultTier
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val stripeService = StripeService(stripeClients, null)
    private val stripePaymentMethods = StripePaymentMethodsService(stripeClients, stripeService, null)
    private val paymentsMethodsService = StripePaymentMethodsService(stripeClients, stripeService, null)
    private val subscriptionService =
        StripeSubscriptionService(stripeClients, paymentsMethodsService, isProduction, VatMapping(mapOf()))
    private val stripeHelper = StripeHelper(stripeClients[Currency.EUR])
    private val invoiceExpandList = listOf("payment_intent")

    @AfterAll
    fun afterAll() {
        stripeHelper.testCleanUp()
    }

    @Test
    @Disabled
    fun paymentMethodsAreCorrectlyReassignedOrUnassignedFromSubscriptions() {
        // This is a complex test which tests attaching/detaching/replacing paymentMethods
        // in (in)active subscriptions. See detailed comments below.
        // Also see https://gitlab.com/heroheroco/general/-/issues/647 is implemented
        val dayOne = instantOf("2020-01-01T12:00:00Z")

        // this allows us simulate progress of subscriptions
        val testClock = stripeHelper.testClock(dayOne)

        var customer = stripeClients[Currency.EUR].customers().create(
            CustomerCreateParams
                .builder()
                .setEmail("<EMAIL>")
                .setName("Herohero testing account")
                .setTestClock(testClock.id)
                .build(),
        )

        val price = stripeClients[Currency.EUR].prices().create(
            PriceCreateParams
                .builder()
                .setCurrency("EUR")
                .setProductData(PriceCreateParams.ProductData.builder().setName("Testing product").build())
                .setUnitAmount(100L)
                .setRecurring(subscriptionService.createRecurringPeriod(DAY))
                .build(),
        )

        val validPaymentMethod1 = paymentMethod(valid = true, customerId = customer.id)

        customer = customer.refresh()
        assertEquals(validPaymentMethod1.id, customer.invoiceSettings.defaultPaymentMethod)

        // we create a subscription with valid payment method
        var validSubscription = subscriptionService.createSubscription(
            customerId = customer.id,
            tier = testTier,
            priceId = price.id,
            creatorId = "testCreatorId",
            userId = "testUserId",
            paymentMethodId = validPaymentMethod1.id,
            couponId = null,
            creatorStripeAccountId = euStripeConnectedAccount,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = testTier.currency,
        )

        // at this point we should have a valid subscription thanks to a valid payment method
        assertEquals("active", validSubscription.status)
        assertEquals(validPaymentMethod1.id, validSubscription.defaultPaymentMethod)
        println("Handling subscription: https://dashboard.stripe.com/test/subscriptions/${validSubscription.id}")

        // also the invoice is paid and also the defaultPaymentMethod is null as well as subscription
        val paidInvoice = stripeService.invoice(validSubscription.latestInvoice, testTier.currency, invoiceExpandList)
        assertEquals("paid", paidInvoice.status)
        val paidPaymentIntent = paidInvoice.paymentIntentObject
        assertEquals(validPaymentMethod1.id, paidPaymentIntent.paymentMethod)

        // now let's create invalid payment method a try to pay subscription with it
        val invalidPaymentMethod = paymentMethod(valid = false, customerId = customer.id)

        // now the customer has its payment method replaced with the new one
        // TODO this will differ when https://gitlab.com/heroheroco/general/-/issues/647 is implemented
        customer = customer.refresh()
        assertEquals(invalidPaymentMethod.id, customer.invoiceSettings.defaultPaymentMethod)

        // but previous subscriptions are left unchanged
        validSubscription = validSubscription.refresh()
        assertEquals(validPaymentMethod1.id, validSubscription.defaultPaymentMethod)

        // now, we create a subscription with valid payment method
        var invalidSubscription = subscriptionService.createSubscription(
            customerId = customer.id,
            tier = testTier,
            priceId = price.id,
            creatorId = "testingCreatorId",
            userId = "testUserId",
            paymentMethodId = invalidPaymentMethod.id,
            couponId = null,
            creatorStripeAccountId = euStripeConnectedAccount,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = testTier.currency,
        )

        // at this point we should have a valid subscription thanks to a valid payment method
        assertEquals("incomplete", invalidSubscription.status)
        assertEquals(invalidPaymentMethod.id, invalidSubscription.defaultPaymentMethod)

        // also the invoice is unpaid and also the defaultPaymentMethod is null as well as subscription
        val unpaidInvoice = stripeService.invoice(
            invalidSubscription.latestInvoice,
            testTier.currency,
            invoiceExpandList,
        )
        assertEquals("open", unpaidInvoice.status)
        val unpaidPaymentIntent = unpaidInvoice.paymentIntentObject
        // the invalid payment method is not attached to unpaid PaymentIntent (Stripe implementation)
        assertEquals(null, unpaidPaymentIntent.paymentMethod)

        // now we delete the invalid payment method and observe that the valid one became default again
        stripePaymentMethods.deletePaymentMethod(customer.id, invalidPaymentMethod, testTier.currency)
        retry {
            customer = customer.refresh()
            validSubscription = validSubscription.refresh()
            invalidSubscription = invalidSubscription.refresh()
            // the customer is now served with the previous validPaymentMethod1
            assertEquals(validPaymentMethod1.id, customer.invoiceSettings.defaultPaymentMethod)
            assertEquals(validPaymentMethod1.id, validSubscription.defaultPaymentMethod)
            // invalid subscription has the payment method unchanged
            assertEquals(null, invalidSubscription.defaultPaymentMethod)
        }

        // now we create a new payment method
        val validPaymentMethod2 = paymentMethod(valid = true, customerId = customer.id)
        retry {
            customer = customer.refresh()
            validSubscription = validSubscription.refresh()
            invalidSubscription = invalidSubscription.refresh()
            // the customer is now served with the previous validPaymentMethod2
            // TODO this will differ when https://gitlab.com/heroheroco/general/-/issues/647 is implemented
            assertEquals(validPaymentMethod2.id, customer.invoiceSettings.defaultPaymentMethod)
            // the subscriptions remain unchanged
            assertEquals(validPaymentMethod1.id, validSubscription.defaultPaymentMethod)
            // invalid subscription has the payment method unchanged
            assertEquals(null, invalidSubscription.defaultPaymentMethod)
        }

        // now we delete the validPaymentMethod1 and observe that the validPaymentMethod2 was set in subscriptions
        stripePaymentMethods.deletePaymentMethod(customer.id, validPaymentMethod1, testTier.currency)
        retry {
            customer = customer.refresh()
            validSubscription = validSubscription.refresh()
            invalidSubscription = invalidSubscription.refresh()
            assertEquals(validPaymentMethod2.id, customer.invoiceSettings.defaultPaymentMethod)
            // the subscription payment method was replaced
            assertEquals(validPaymentMethod2.id, validSubscription.defaultPaymentMethod)
            // invalid subscription has the payment method unchanged
            assertEquals(null, invalidSubscription.defaultPaymentMethod)
        }

        retry {
            // let's advance to another day to see there is a new charge
            testClock.advanceTo(dayOne.plusDays(1).plusHours(2), "subscription ${validSubscription.id}")
        }

        retry {
            // the subscription should be now updated and have a new payment
            validSubscription = validSubscription.refresh()
            assertNotEquals(paidInvoice.id, validSubscription.latestInvoice)
            val secondInvoice = stripeService.invoice(
                validSubscription.latestInvoice,
                testTier.currency,
                invoiceExpandList,
            )
            assertEquals("paid", secondInvoice.status)
            assertEquals(validPaymentMethod2.id, secondInvoice.paymentIntentObject.paymentMethod)
        }

        // now let's detach the payment method
        stripePaymentMethods.deletePaymentMethod(customer.id, validPaymentMethod2, testTier.currency)
        retry {
            // now there is now payment method
            customer = customer.refresh()
            validSubscription = validSubscription.refresh()
            assertEquals(null, customer.invoiceSettings.defaultPaymentMethod)
            assertEquals(null, validSubscription.defaultPaymentMethod)
        }

        retry {
            // let's advance one day, the subscription should get into past_due state
            testClock.advanceTo(dayOne.plusDays(2).plusHours(2), "subscription ${validSubscription.id}")
        }

        retry {
            validSubscription = validSubscription.refresh()
            assertEquals("past_due", validSubscription.status)
        }

        // so now we create a new payment method and attach it to the customer
        retry {
            val validPaymentMethod3 = paymentMethod(true, customerId = customer.id)
            customer = customer.refresh()
            validSubscription = validSubscription.refresh()
            assertEquals(validPaymentMethod3.id, customer.invoiceSettings.defaultPaymentMethod)
            // the subscription now takes paymentMethod from the customer as the customer did not specifically
            // made this payment method default for this specific subscription -> paymentMethod stays null
            assertEquals(null, validSubscription.defaultPaymentMethod)
        }

        retry {
            // and then we advance one day to see that the past_due state is fixed
            testClock.advanceTo(dayOne.plusDays(3).plusHours(3), "subscription ${validSubscription.id}")
        }

        retry {
            validSubscription = validSubscription.refresh()
            assertEquals("active", validSubscription.status)
        }
    }

    private fun retry(body: () -> Unit) {
        retryOn(AssertionError::class, RateLimitException::class, retryWaitMillis = 800L) {
            Thread.sleep(1000L)
            body()
        }
    }

    /** hacky way of updating stripe Customers */
    private fun Customer.refresh(): Customer = stripeService.customer(this.id, testTier.currency)

    /** hacky way of updating stripe Subscriptions */
    private fun Subscription.refresh(): Subscription = stripeClients[Currency.EUR].subscriptions().retrieve(this.id)

    private fun paymentMethod(
        valid: Boolean,
        customerId: String,
    ): PaymentMethod {
        val paymentMethod = stripeClients[Currency.EUR].paymentMethods().create(
            PaymentMethodCreateParams
                .builder()
                .setType(PaymentMethodCreateParams.Type.CARD)
                .setCard(
                    PaymentMethodCreateParams.Token.builder()
                        .setToken(if (valid) "tok_visa" else "tok_chargeDeclined")
                        .build(),
                )
                .build(),
        )
        // we associate the new invalid payment method with the customer
        stripePaymentMethods.putPaymentMethodViaSetupIntent(
            paymentMethodId = paymentMethod.id,
            customerId = customerId,
            cardCreateType = CardCreateType.CARD,
            currency = Currency.EUR,
            makeDefault = true,
        )

        return paymentMethod
    }
}
