package hero.api.messages.service

import hero.api.user.service.UserRelationsService
import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.exceptions.http.ForbiddenException
import hero.model.topics.PostState
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class MessageThreadQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetUsersActiveMessageThreads {
        @Test
        fun `query should return single result, must include last message and all participants`() {
            val underTest = MessageThreadQueryService(
                TestCollections.messageThreadsCollection,
                TestCollections.postsCollection,
                TestCollections.usersCollection,
                UserRelationsService(TestCollections.subscribersCollection),
            )
            val participant1 = testHelper.createUser("hung")
            val participant2 = testHelper.createUser("ester")
            val thread1 = testHelper.createMessageThread("hung", listOf("ester"), lastMessageAt = Instant.now())
            testHelper.createPost(
                "ester",
                id = "2",
                messageThreadId = thread1.id,
                state = PostState.DELETED,
            )
            val lastMessage = testHelper.createPost("ester", id = "3", messageThreadId = thread1.id)

            val threads = underTest.execute(GetUsersActiveMessageThreads("hung", PageRequest(pageSize = 1)))

            assertThat(threads.content).containsExactly(
                MessageThreadWithLastMessage(
                    thread1,
                    lastMessage,
                    setOf(participant1, participant2),
                ),
            )
            assertThat(threads.hasNext).isFalse()
        }

        @Test
        fun `should return last non deleted message`() {
            val underTest = MessageThreadQueryService(
                TestCollections.messageThreadsCollection,
                TestCollections.postsCollection,
                TestCollections.usersCollection,
                UserRelationsService(TestCollections.subscribersCollection),
            )
            val thread1 = testHelper.createMessageThread("hung", listOf("ester"), lastMessageAt = Instant.now())
            val lastMessageUser = testHelper.createUser("ester")
            val lastMessage = testHelper.createPost("ester", id = "1", messageThreadId = thread1.id)
            testHelper.createPost(
                "ester",
                id = "2",
                messageThreadId = thread1.id,
                state = PostState.DELETED,
            )

            val threads = underTest.execute(GetUsersActiveMessageThreads("hung", PageRequest(pageSize = 1)))

            assertThat(threads.content).containsExactly(
                MessageThreadWithLastMessage(
                    thread1,
                    lastMessage,
                    setOf(lastMessageUser),
                ),
            )
            assertThat(threads.hasNext).isFalse()
        }

        @Test
        fun `should paginate using after cursor`() {
            val underTest = MessageThreadQueryService(
                TestCollections.messageThreadsCollection,
                TestCollections.postsCollection,
                TestCollections.usersCollection,
                UserRelationsService(TestCollections.subscribersCollection),
            )
            val now = Instant.now()
            val thread1 = testHelper.createMessageThread(
                "hung",
                listOf("ester"),
                lastMessageAt = now,
            )
            val lastMessageUser1 = testHelper.createUser("ester")
            val lastMessage1 = testHelper.createPost("ester", messageThreadId = thread1.id)

            val lastMessageUser2 = testHelper.createUser("vojta")
            val thread2 = testHelper.createMessageThread("hung", listOf("vojta"), lastMessageAt = now - 5.seconds)
            val lastMessage2 = testHelper.createPost("vojta", messageThreadId = thread2.id)

            val lastMessageUser3 = testHelper.createUser("jonas")
            val thread3 = testHelper.createMessageThread("hung", listOf("jonas"), lastMessageAt = now - 10.seconds)
            val lastMessage3 = testHelper.createPost("jonas", messageThreadId = thread3.id)

            val firstPage = underTest.execute(GetUsersActiveMessageThreads("hung", PageRequest(pageSize = 1)))

            assertThat(firstPage.content).containsExactly(
                MessageThreadWithLastMessage(
                    thread1,
                    lastMessage1,
                    setOf(lastMessageUser1),
                ),
            )
            assertThat(firstPage.hasNext).isTrue()

            val secondPage = underTest.execute(GetUsersActiveMessageThreads("hung", firstPage.nextPageable))

            assertThat(secondPage.content).containsExactly(
                MessageThreadWithLastMessage(
                    thread2,
                    lastMessage2,
                    setOf(lastMessageUser2),
                ),
            )
            assertThat(secondPage.hasNext).isTrue()

            val thirdPage = underTest.execute(GetUsersActiveMessageThreads("hung", secondPage.nextPageable))

            assertThat(thirdPage.content).containsExactly(
                MessageThreadWithLastMessage(
                    thread3,
                    lastMessage3,
                    setOf(lastMessageUser3),
                ),
            )
            assertThat(thirdPage.hasNext).isFalse()
        }
    }

    @Nested
    inner class GetMessageThread {
        @Test
        fun `should get message thread details with permissions that user is allowed to post to`() {
            val underTest = MessageThreadQueryService(
                TestCollections.messageThreadsCollection,
                TestCollections.postsCollection,
                TestCollections.usersCollection,
                UserRelationsService(TestCollections.subscribersCollection),
            )
            val user1 = testHelper.createUser("hung")
            val user2 = testHelper.createUser("ester")
            val thread = testHelper.createMessageThread("hung", listOf("ester"))
            testHelper.createSubscriber("ester", "hung")

            val result = underTest.execute(GetMessageThread("hung", thread.id))

            assertThat(result.messageThread).isEqualTo(thread)
            assertThat(result.participants).containsExactlyInAnyOrder(user1, user2)
            assertThat(result.permissions.canPost).isTrue()
        }

        @Test
        fun `should get message thread details with permissions that user is not allowed to post to`() {
            val underTest = MessageThreadQueryService(
                TestCollections.messageThreadsCollection,
                TestCollections.postsCollection,
                TestCollections.usersCollection,
                UserRelationsService(TestCollections.subscribersCollection),
            )
            val user1 = testHelper.createUser("hung")
            val user2 = testHelper.createUser("ester")
            val thread = testHelper.createMessageThread("hung", listOf("ester"))

            val result = underTest.execute(GetMessageThread("hung", thread.id))

            assertThat(result.messageThread).isEqualTo(thread)
            assertThat(result.participants).containsExactlyInAnyOrder(user1, user2)
            assertThat(result.permissions.canPost).isFalse()
        }

        @Test
        fun `should allow only participants to access message thread`() {
            val underTest = MessageThreadQueryService(
                TestCollections.messageThreadsCollection,
                TestCollections.postsCollection,
                TestCollections.usersCollection,
                UserRelationsService(TestCollections.subscribersCollection),
            )
            val thread = testHelper.createMessageThread("hung", listOf("ester"))

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetMessageThread("vojta", thread.id))
            }
        }
    }
}
