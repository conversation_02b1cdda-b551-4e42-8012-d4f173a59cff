package hero.api.user.service

import hero.baseutils.instantOf
import hero.baseutils.mockNow
import hero.exceptions.http.BadRequestException
import hero.model.ImageAsset
import hero.model.Path
import hero.model.User
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant

class UserCommandServiceIT : IntegrationTest() {
    @Nested
    inner class UpdateUserDetails {
        @Test
        fun `updating user with no changes, should not do anything`() {
            val underTest = UserCommandService(
                TestCollections.usersCollection,
                TestRepositories.userRepository,
                TestCollections.pathsCollection,
                pubSubMock,
            )
            val user = testHelper.createUser("pepa")

            val updatedUser = underTest.execute(user.toUpdateCommand())

            assertThat(updatedUser).isEqualTo(user)
            assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(user)
            assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(user)
            verify(exactly = 0) { pubSubMock.publish(any()) }
        }

        @Nested
        inner class UpdateInvoiceEmail {
            @Test
            fun `should update invoice email`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val expectedUser = user.copy(creator = user.creator.copy(emailInvoice = "<EMAIL>"))

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(emailInvoice = "<EMAIL>"))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
            }
        }

        @Nested
        inner class UpdatePublicEmail {
            @Test
            fun `should update public email`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val expectedUser = user.copy(creator = user.creator.copy(emailPublic = "<EMAIL>"))

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(emailPublic = "<EMAIL>"))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
            }
        }

        @Nested
        inner class UpdatePath {
            @Test
            fun `should update path only`() {
                mockNow("2020-01-01T20:00:00Z")
                val now = instantOf("2020-01-01T20:00:00Z")
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val expectedUser = user.copy(path = "cestmir", pathChanged = now)

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(path = "cestmir"))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestCollections.pathsCollection["cestmir"].get()).isEqualTo(
                    Path(
                        id = "cestmir",
                        userId = "pepa",
                        created = now,
                    ),
                )
            }

            @ParameterizedTest
            @ValueSource(
                strings = ["assets", "create", "hero", "herohero", "login", "post", "public", "search", "services"],
            )
            fun `user cannot change path to one of excluded paths`(path: String) {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(path = path))
                    }
                    .withMessage("Field 'path' of value '$path' is invalid: illegal_string")
            }

            @ParameterizedTest
            @ValueSource(strings = ["pePa", "pep!"])
            fun `path must be alphanumeric and lowercase`(path: String) {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(path = path))
                    }
                    .withMessage("Field 'path' of value '$path' is invalid: lowercase_alphanumeric")
            }

            @Test
            fun `path must be at least 3 characters long`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(path = "12"))
                    }
                    .withMessage("Field 'path' of value '12' is invalid: min_length_two")
            }

            @Test
            fun `user cannot take a path that is taken by another user`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                TestCollections.pathsCollection["cestmir"].set(Path("cestmir", "cestmir"))
                val user = testHelper.createUser("pepa")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(path = "cestmir"))
                    }
                    .withMessage("Field 'path' of value 'cestmir' is invalid: name_taken")
            }

            @Test
            fun `user are allowed to update path only once every hour`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa", pathChangedAt = Instant.now())

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(path = "cestmir"))
                    }
                    .withMessage("Field 'path' of value 'cestmir' is invalid: path_change_too_often")
            }
        }

        @Nested
        inner class UpdateBio {
            @Test
            fun `should update user's bio`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val expectedUser = user.copy(bio = "my new super bio")

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(bio = "my new super bio"))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
            }

            @Test
            fun `bio should be trimmed`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val expectedUser = user.copy(bio = "my new super bio")

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(bio = "    my new super bio"))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
            }

            @Test
            fun `bio cannot be longer than 1500 characters`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val bio = "a".repeat(1501)

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(bio = bio))
                    }
                    .withMessage("Field 'bio' of value '$bio' is invalid: max_length_exceeded")
            }
        }

        @Nested
        inner class UpdateName {
            @Test
            fun `should update user's name`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val expectedUser = user.copy(name = "Josef Pastrnak")

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(name = "Josef Pastrnak"))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
            }

            @Test
            fun `name must be at least 3 characters long`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(name = "pe"))
                    }
                    .withMessage("Field 'name' of value 'pe' is invalid: min_length_two")
            }

            @Test
            fun `name must not be empty`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(
                            user.toUpdateCommand().copy(name = "\u2001\u2002\u2003\u2004\u200E\u200E\u200E"),
                        )
                    }
                    .withMessageMatching("Field 'name' of value '.*' is invalid: min_length_two")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(
                            user.toUpdateCommand().copy(name = ""),
                        )
                    }
                    .withMessageMatching("Field 'name' of value '.*' is invalid: min_length_two")
            }

            @ParameterizedTest
            @ValueSource(strings = ["HeroHero", "HeroHero pepa"])
            fun `name herohero is a reserved name`(name: String) {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")

                assertThatExceptionOfType(BadRequestException::class.java)
                    .isThrownBy {
                        underTest.execute(user.toUpdateCommand().copy(name = name))
                    }
                    .withMessage("Field 'name' of value '$name' is invalid: illegal_string")
            }
        }

        @Nested
        inner class UpdateProfileImage {
            @Test
            fun `should update user's profile image`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa")
                val profileImage = UpdateUserProfileImage(url = "https://expected.com", height = 100, width = 50)
                val expectedUser = user.copy(image = ImageAsset("https://expected.com", width = 50, 100))

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(profileImage = profileImage))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
            }
        }

        @Nested
        inner class UpdateIsOfAge {
            @Test
            fun `should turn isOfAge to true`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa", isOfAge = false)
                val expectedUser = user.copy(isOfAge = true)

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(isOfAge = true))

                assertThat(updatedUser).isEqualTo(expectedUser)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(expectedUser)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(expectedUser)
            }

            @Test
            fun `should not modify isOfAge`() {
                val underTest = UserCommandService(
                    TestCollections.usersCollection,
                    TestRepositories.userRepository,
                    TestCollections.pathsCollection,
                    pubSubMock,
                )
                val user = testHelper.createUser("pepa", isOfAge = true)

                val updatedUser = underTest.execute(user.toUpdateCommand().copy(isOfAge = null))

                assertThat(updatedUser).isEqualTo(user)
                assertThat(TestCollections.usersCollection[user.id].get()).isEqualTo(user)
                assertThat(TestRepositories.userRepository.getById(user.id)).isEqualTo(user)
            }
        }

        private fun User.toUpdateCommand() =
            UpdateUserDetails(
                userId = id,
                path = path,
                bio = bio,
                name = name,
                isOfAge = false,
                profileImage = null,
                emailPublic = null,
                emailInvoice = null,
            )
    }
}
