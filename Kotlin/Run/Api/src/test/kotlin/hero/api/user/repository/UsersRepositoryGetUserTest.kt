package hero.api.user.repository

import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.jwtFor
import hero.http4k.auth.withAccessTokenCookie
import hero.model.Creator
import hero.model.User
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.unmockkAll
import org.http4k.core.Method
import org.http4k.core.Request
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertTrue

internal class UsersRepositoryGetUserTest {
    private val userRepository = spyk(
        UsersRepository(
            firestoreFulltext = mockk(),
            imageRepository = mockk(),
            pathCollection = mockk(),
            pubSub = mockk(),
            subscriberCollection = mockk(),
            tierRepository = mockk(),
            collection = mockk(),
            accountService = mockk(),
            userRepository = mockk(),
        ),
    )

    private val user1 = User(id = "and-mul", path = "and-mul", name = "and-mul", creator = Creator(tierId = "EUR05"))
    private val user2 = User("bea-rod", path = "and-mul", name = "and-mul", creator = Creator(tierId = "EUR05"))

    @BeforeEach
    fun beforeEach() {
        every { userRepository.get(user1.id) } returns user1
        every { userRepository.get(user2.id) } returns user2
    }

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun testGetUserValid() {
        val request = Request(Method.GET, "/")
            .withAccessTokenCookie(jwtFor(user1.id))

        assertEquals(
            user1,
            userRepository.get(request, user1.id),
        )
    }

    @Test
    fun testGetUserValidButInvalidPath() {
        val request = Request(Method.GET, "/")
            .withAccessTokenCookie(jwtFor(user1.id))

        val exception = assertThrows<ForbiddenException> {
            userRepository.get(request, user2.id)
        }

        assertTrue("JWT user does not correspond to the requested user" in exception.message!!)
    }

    @Test
    fun testGetUserInvalidBearer() {
        val request = Request(Method.GET, "/")
            .withAccessTokenCookie("123456")

        val exception = assertThrows<UnauthorizedException> {
            userRepository.get(request, user1.id)
        }

        assertEquals("Unauthorized.", exception.message)
    }
}
