package hero.api.payment.controller

import hero.exceptions.http.ForbiddenException
import org.http4k.core.Method
import org.http4k.core.Request
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import java.io.File

class StripeWebhookControllerTest {
    @Test
    fun `should validate that signature matches body and the secret`() {
        val body = File("src/test/resources/stripe/webhooks/charge-example.json").readText()

        val request = Request(Method.POST, "")
            .body(body)
            .header("Stripe-Signature", "t=1723903321")
            .header("Stripe-Signature", "v1=80e0b1efb407a691a2eb486eb139f75d29690d9dae092e585283c7597d4dc450")
            .header("Stripe-Signature", "v0=41d3a158948e96286e7cb6c824691d78bf75cc0d74f171588ab1b25411c5d087")

        assertDoesNotThrow {
            validatePayload(
                request,
                // this value was generated by CLI `stripe listen`
                "whsec_914ecbc16e2d73f84f75c07eef291fb07b411a8f441a43b7ab39968e0607fec5",
                Long.MAX_VALUE,
            )
        }
    }

    @Test
    fun `should validate that signature does not match body and the secret`() {
        val body = File("src/test/resources/stripe/webhooks/charge-example.json").readText()

        val request = Request(Method.POST, "")
            .body(body)
            .header("Stripe-Signature", "t=1723903321")
            .header("Stripe-Signature", "v1=random07a691a2eb486eb139f75d29690d9dae092e585283c7597d4dc450")
            .header("Stripe-Signature", "v0=41d3a158948e96286e7cb6c824691d78bf75cc0d74f171588ab1b25411c5d087")

        assertThrows<ForbiddenException> {
            validatePayload(
                request,
                // this value was generated by CLI `stripe listen`
                "whsec_914ecbc16e2d73f84f75c07eef291fb07b411a8f441a43b7ab39968e0607fec5",
                Long.MAX_VALUE,
            )
        }
    }
}
