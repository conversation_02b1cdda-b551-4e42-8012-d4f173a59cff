package hero.api.messages.service

import hero.baseutils.minus
import hero.baseutils.plus
import hero.baseutils.truncated
import hero.core.data.PageRequest
import hero.exceptions.http.ForbiddenException
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class MessageQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetMessagesFromThread {
        @Test
        fun `should return messages from a thread with payment information`() {
            val underTest = MessageQueryService(
                IntegrationTestHelper.TestCollections.messageThreadsCollection,
                IntegrationTestHelper.TestCollections.postsCollection,
                IntegrationTestHelper.TestCollections.postPaymentsCollection,
            )
            val thread = testHelper.createMessageThread("filip", listOf("pablo", "alex"))
            val messageFromFilip = testHelper.createPost("filip", messageThreadId = thread.id)

            val freeMessageFromPablo = testHelper.createPost("pablo", messageThreadId = thread.id)
            val paidMessageFromPablo = testHelper.createPost("pablo", messageThreadId = thread.id, price = 100)
            testHelper.createPostPayment("filip", paidMessageFromPablo.id)

            val paidMessageFromAlex = testHelper.createPost("alex", messageThreadId = thread.id, price = 300)
            testHelper.createPostPayment("pablo", paidMessageFromAlex.id)

            val messages = underTest.execute(GetMessagesFromThread("filip", thread.id, PageRequest())).content

            assertThat(messages).hasSize(4)
            assertThat(messages).filteredOn { it.message.id == messageFromFilip.id }
                .hasSize(1)
                .allMatch { it.payment == null }

            assertThat(messages).filteredOn { it.message.id == freeMessageFromPablo.id }
                .hasSize(1)
                .allMatch { it.payment == null }

            // Filip paid for the paid message from Pablo
            assertThat(messages).filteredOn { it.message.id == paidMessageFromPablo.id }
                .hasSize(1)
                .allMatch { it.payment != null }

            // Filip did not pay for the paid message from Alex, but Pablo did, no payment should be returned for Filip!
            assertThat(messages).filteredOn { it.message.id == paidMessageFromAlex.id }
                .hasSize(1)
                .allMatch { it.payment == null }
        }

        @Test
        fun `should paginate using after cursor and order by publishedAt descending`() {
            val underTest = MessageQueryService(
                IntegrationTestHelper.TestCollections.messageThreadsCollection,
                IntegrationTestHelper.TestCollections.postsCollection,
                IntegrationTestHelper.TestCollections.postPaymentsCollection,
            )
            val thread = testHelper.createMessageThread("filip", listOf("pablo", "alex"))

            val now = Instant.now().truncated()
            val msg1 = testHelper.createPost("filip", messageThreadId = thread.id, publishedAt = now)
            val msg2 = testHelper.createPost("pablo", messageThreadId = thread.id, publishedAt = now - 5.seconds)
            val msg3 = testHelper.createPost("pablo", messageThreadId = thread.id, publishedAt = now - 20.seconds)

            val allMessages = underTest.execute(GetMessagesFromThread("filip", thread.id, PageRequest())).content
            assertThat(allMessages).isSortedAccordingTo(
                compareBy<MessageWithPayment> {
                    it.message.published
                }.reversed(),
            )

            val firstMessage = underTest.execute(GetMessagesFromThread("filip", thread.id, PageRequest(pageSize = 1)))
            assertThat(firstMessage.content.map { it.message }).isEqualTo(listOf(msg1))
            assertThat(firstMessage.hasNext).isTrue()

            val secondMessage = underTest.execute(GetMessagesFromThread("filip", thread.id, firstMessage.nextPageable))
            assertThat(secondMessage.content.map { it.message }).isEqualTo(listOf(msg2))
            assertThat(secondMessage.hasNext).isTrue()

            val thirdMessage = underTest.execute(GetMessagesFromThread("filip", thread.id, secondMessage.nextPageable))
            assertThat(thirdMessage.content.map { it.message }).isEqualTo(listOf(msg3))
            assertThat(thirdMessage.hasNext).isFalse()
        }

        @Test
        fun `should allow only participants to fetch messages from the thread`() {
            val underTest = MessageQueryService(
                IntegrationTestHelper.TestCollections.messageThreadsCollection,
                IntegrationTestHelper.TestCollections.postsCollection,
                IntegrationTestHelper.TestCollections.postPaymentsCollection,
            )
            val thread = testHelper.createMessageThread("filip", listOf("pablo", "alex"))
            testHelper.createPost("filip", messageThreadId = thread.id)
            testHelper.createPost("pablo", messageThreadId = thread.id)

            assertThrows<ForbiddenException> {
                underTest.execute(GetMessagesFromThread("vojta", thread.id, PageRequest()))
            }
        }

        @Test
        fun `should not return messages before the delete timestamp for the given user`() {
            val underTest = MessageQueryService(
                IntegrationTestHelper.TestCollections.messageThreadsCollection,
                IntegrationTestHelper.TestCollections.postsCollection,
                IntegrationTestHelper.TestCollections.postPaymentsCollection,
            )
            val deletedAt = Instant.ofEpochSecond(1705326865)
            val thread = testHelper.createMessageThread(
                "filip",
                listOf("pablo", "alex"),
                deletes = mapOf("pablo" to deletedAt),
            )

            val beforeDeleted1 = testHelper.createPost(
                "filip",
                messageThreadId = thread.id,
                publishedAt = deletedAt - 15.seconds,
            )
            val beforeDeleted2 = testHelper.createPost(
                "filip",
                messageThreadId = thread.id,
                publishedAt = deletedAt - 10.seconds,
            )
            val afterDeleted = testHelper.createPost(
                "filip",
                messageThreadId = thread.id,
                publishedAt = deletedAt + 5.seconds,
            )

            val pabloResult = underTest.execute(GetMessagesFromThread("pablo", thread.id, PageRequest()))
            assertThat(pabloResult.content).containsExactly(MessageWithPayment(afterDeleted))

            val filipResult = underTest.execute(GetMessagesFromThread("filip", thread.id, PageRequest()))
            assertThat(filipResult.content).containsExactly(
                MessageWithPayment(afterDeleted),
                MessageWithPayment(beforeDeleted2),
                MessageWithPayment(beforeDeleted1),
            )
        }
    }

    @Nested
    inner class GetMessage {
        @Test
        fun `should fetch single message without payment`() {
            val underTest = MessageQueryService(
                IntegrationTestHelper.TestCollections.messageThreadsCollection,
                IntegrationTestHelper.TestCollections.postsCollection,
                IntegrationTestHelper.TestCollections.postPaymentsCollection,
            )
            val thread = testHelper.createMessageThread("filip", listOf("pablo", "alex"))
            val message = testHelper.createPost("filip", messageThreadId = thread.id)

            val result = underTest.execute(GetMessage("pablo", message.id))

            assertThat(result.message).isEqualTo(message)
            assertThat(result.payment).isNull()
        }

        @Test
        fun `should fetch single message with payment`() {
            val underTest = MessageQueryService(
                IntegrationTestHelper.TestCollections.messageThreadsCollection,
                IntegrationTestHelper.TestCollections.postsCollection,
                IntegrationTestHelper.TestCollections.postPaymentsCollection,
            )
            val thread = testHelper.createMessageThread("filip", listOf("pablo", "alex"))
            val message = testHelper.createPost("filip", messageThreadId = thread.id, price = 10)
            val payment = testHelper.createPostPayment("pablo", message.id)

            val result = underTest.execute(GetMessage("pablo", message.id))

            assertThat(result.message).isEqualTo(message)
            assertThat(result.payment).isEqualTo(payment)
        }

        @Test
        fun `should throw if user is not part of the thread`() {
            val underTest = MessageQueryService(
                IntegrationTestHelper.TestCollections.messageThreadsCollection,
                IntegrationTestHelper.TestCollections.postsCollection,
                IntegrationTestHelper.TestCollections.postPaymentsCollection,
            )
            val thread = testHelper.createMessageThread("filip", listOf("pablo", "alex"))
            val message = testHelper.createPost("filip", messageThreadId = thread.id, price = 10)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(GetMessage("impostor", message.id))
                }
        }
    }
}
