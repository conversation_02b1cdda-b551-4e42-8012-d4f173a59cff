package hero.media

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaStatsService
import hero.gjirafa.GjirafaUploadsService
import hero.http4k.http4kInJetty
import hero.media.controller.AssetsController
import hero.media.controller.GjirafaLivestreamsController
import hero.media.controller.GjirafaStatsController
import hero.media.controller.GjirafaUploadsController
import hero.media.service.GjirafaPostService
import hero.media.service.StorageUploadsService
import hero.model.Post
import hero.model.User
import hero.repository.post.PostRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import org.jooq.DSLContext

fun main() {
    log.info("Service is starting.")
    val isLocalHost = systemEnv("LOG_APPENDER") != "ConsoleFluentD"
    val pubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)
    val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    val postsCollection = firestore.typedCollectionOf(Post)
    val usersCollection = firestore.typedCollectionOf(User)
    val gjirafaApiKey = SystemEnv.gjirafaApiKey
    val gjirafaProject = SystemEnv.gjirafaProject
    val gjirafaImageKey = SystemEnv.gjirafaImageKey
    val gjirafaLivestreamsService = GjirafaLivestreamsService(gjirafaProject, gjirafaApiKey)
    val lazyContext = lazyContext(SystemEnv.environment)
    val gjirafaPostService = GjirafaPostService(postsCollection, PostRepository(lazyContext), pubSub)
    val gjirafaStatsService = GjirafaStatsService(gjirafaProject, gjirafaApiKey)
    val gjirafaUploadsService = GjirafaUploadsService(gjirafaProject, gjirafaApiKey, gjirafaImageKey)
    val storageUploadsService = StorageUploadsService(SystemEnv.cloudProject, SystemEnv.isProduction)

    if (System.getProperty("sun.net.http.allowRestrictedHeaders") != "true") {
        error(
            "Property allowRestrictedHeaders is not correctly set, " +
                "therefore origin header cannot be added to the POST call.",
        )
    }

    http4kInJetty(
        "Herohero Media service",
        SystemEnv.isProduction,
        isLocalHost,
        listOf(
            AssetsController(
                storageUploadsService,
            ),
            GjirafaLivestreamsController(
                gjirafaLivestreamsService,
                gjirafaStatsService,
                usersCollection,
                postsCollection,
            ),
            GjirafaStatsController(
                gjirafaStatsService,
            ),
            GjirafaUploadsController(
                gjirafaUploadsService,
                gjirafaPostService,
                pubSub,
            ),
        ),
    )
}

private fun lazyContext(environment: String): Lazy<DSLContext> {
    // if environment is not local, we have to trigger the lazy loading, so we know if a configuration is wrong
    // on the start
    try {
        ConnectorConnectionPool.dataSource
    } catch (e: Exception) {
        if (environment != "local") {
            throw e
        } else {
            log.warn("Failed to load data source, ignoring as run in local env: ${e.message}")
        }
    }

    return lazy {
        JooqSQL.context(ConnectorConnectionPool.dataSource)
    }
}
