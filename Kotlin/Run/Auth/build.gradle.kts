plugins {
    id("hero.kotlin-run-service-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(Http4k.securityOauth)
    implementation("org.http4k:http4k-client-fuel:_")

    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Exceptions"))
    implementation(projectModule(":Modules:Firebase"))
    implementation(projectModule(":Modules:GoogleCloud"))
    implementation(projectModule(":Modules:Http4k"))
    implementation(projectModule(":Modules:Jwt"))
    implementation(projectModule(":Modules:Model"))
    implementation(projectModule(":Modules:Contract"))
    implementation(projectModule(":Modules:Spotify"))
    implementation(projectModule(":Modules:Repository"))
    implementation("org.passay:passay:_")

    // com.sun.mail provides actual implementation of javax.mail:javax.mail-api
    implementation("com.sun.mail:javax.mail:_")
    testImplementation(projectModule(":Modules:Testing"))
    testImplementation(projectModule(":Modules:IntegrationTesting"))
}
