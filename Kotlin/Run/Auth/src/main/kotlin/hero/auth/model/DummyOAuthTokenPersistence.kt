package hero.auth.model

import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Uri
import org.http4k.security.AccessToken
import org.http4k.security.CrossSiteRequestForgeryToken
import org.http4k.security.Nonce
import org.http4k.security.OAuthPersistence
import org.http4k.security.PkceChallengeAndVerifier
import org.http4k.security.openid.IdToken

/** This is dummy implementation, so we can pass it to the OAuth provider. */
class DummyOAuthTokenPersistence : OAuthPersistence {
    override fun assignCsrf(
        redirect: Response,
        csrf: CrossSiteRequestForgeryToken,
    ): Response {
        TODO("Should never be called.")
    }

    override fun assignNonce(
        redirect: Response,
        nonce: Nonce,
    ): Response {
        TODO("Should never be called.")
    }

    override fun assignOriginalUri(
        redirect: Response,
        originalUri: Uri,
    ): Response {
        TODO("Should never be called.")
    }

    override fun assignPkce(
        redirect: Response,
        pkce: PkceChallengeAndVerifier,
    ): Response {
        TODO("Should never be called.")
    }

    override fun assignToken(
        request: Request,
        redirect: Response,
        accessToken: AccessToken,
        idToken: IdToken?,
    ): Response {
        TODO("Should never be called.")
    }

    override fun retrieveCsrf(request: Request): CrossSiteRequestForgeryToken? {
        TODO("Should never be called.")
    }

    override fun retrieveNonce(request: Request): Nonce? {
        TODO("Should never be called.")
    }

    override fun retrieveOriginalUri(request: Request): Uri? {
        TODO("Should never be called.")
    }

    override fun retrievePkce(request: Request): PkceChallengeAndVerifier? {
        TODO("Should never be called.")
    }

    override fun retrieveToken(request: Request): AccessToken? {
        TODO("Should never be called.")
    }
}
