package hero.auth.controller

import hero.auth.service.RevokeSession
import hero.auth.service.SessionCommandService
import hero.baseutils.log
import hero.contract.api.disableDevice
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.withDeletedAccessTokenCookie
import hero.http4k.auth.withDeletedImpersonateTokenCookie
import hero.http4k.auth.withDeletedRefreshTokenCookie
import hero.http4k.extensions.authorization
import hero.http4k.extensions.deviceId
import hero.http4k.extensions.example
import hero.http4k.extensions.post
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header

class LogoutController(
    private val sessionCommandService: SessionCommandService,
) {
    @Suppress("unused")
    val routeLogout = "/v1/oauth/logout".post(
        summary = "Logs user out",
        tag = "Authentication",
        parameters = object {
            val authorization = Header.authorization()
        },
        responses = listOf(
            Status.NO_CONTENT example Unit,
        ),
        receiving = null,
        handler = { request, _ ->
            val user = request.getJwtUser()
            val sessionId = user.sessionId
            if (sessionId != null) {
                sessionCommandService.execute(RevokeSession(sessionId, user.id))
            }
            log.info("User ${user.id} is logging out")
            val deviceId = request.deviceId
            if (deviceId != null) {
                log.info("Disabling device $deviceId for user ${user.id}")
                disableDevice(user.id, deviceId)
            }
            Response(Status.NO_CONTENT)
                .withDeletedAccessTokenCookie()
                .withDeletedImpersonateTokenCookie()
                .withDeletedRefreshTokenCookie(RefreshController.refreshFullPath)
        },
    )
}
