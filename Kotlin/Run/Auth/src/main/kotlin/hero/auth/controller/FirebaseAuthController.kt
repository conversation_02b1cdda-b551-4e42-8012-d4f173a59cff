package hero.auth.controller

import com.github.kittinunf.fuel.httpDelete
import com.github.kittinunf.fuel.httpPost
import com.google.firebase.ErrorCode
import com.google.firebase.auth.ActionCodeSettings
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthException
import com.google.firebase.auth.FirebaseToken
import com.google.firebase.auth.UserRecord
import hero.auth.controller.dto.exampleAuthResponse
import hero.auth.controller.dto.exampleEmailProcessingRequest
import hero.auth.controller.dto.exampleInitiateUpdateEmailRequest
import hero.auth.controller.dto.exampleResetUserPasswordRequest
import hero.auth.controller.dto.exampleSignUpRequest
import hero.auth.controller.dto.exampleUpdateEmailRequest
import hero.auth.service.GenerateRefreshToken
import hero.auth.service.RefreshToken
import hero.auth.service.RefreshTokenCommandService
import hero.auth.service.RevokeAllSessions
import hero.auth.service.SessionCommandService
import hero.auth.util.changeEmailToken
import hero.auth.util.deleteToken
import hero.auth.util.validateChangeEmailToken
import hero.auth.util.validateDeleteToken
import hero.auth.util.validateVerifyEmailToken
import hero.auth.util.verifyEmailToken
import hero.baseutils.FuelException
import hero.baseutils.allowedRedirects
import hero.baseutils.fetch
import hero.baseutils.isUrlValid
import hero.baseutils.log
import hero.baseutils.serviceCall
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.HttpStatusException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.http4k.auth.getJwtUser
import hero.http4k.auth.tokenExpirations
import hero.http4k.auth.withAccessTokenCookie
import hero.http4k.auth.withDeletedAccessTokenCookie
import hero.http4k.auth.withDeletedImpersonateTokenCookie
import hero.http4k.auth.withRefreshTokenCookie
import hero.http4k.extensions.body
import hero.http4k.extensions.deviceId
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.location
import hero.http4k.extensions.post
import hero.http4k.extensions.remoteAddress
import hero.http4k.extensions.userAgent
import hero.jackson.map
import hero.jackson.toJson
import hero.jwt.ACCESS_TOKEN
import hero.jwt.IMPERSONATION_TOKEN
import hero.jwt.JwtUser
import hero.jwt.authorization
import hero.jwt.toJwt
import hero.model.DeletedReason
import hero.model.ExtractedUser
import hero.model.OAuthProvider
import hero.model.SignInProvider
import hero.model.User
import hero.model.UserIdResponse
import hero.model.UserStateChange
import hero.model.UserStatus
import hero.model.topics.EmailPublished
import org.http4k.contract.ContractRoute
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.Uri
import org.http4k.core.cookie.cookie
import org.http4k.core.query
import org.http4k.core.removeQuery
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.passay.LengthRule
import org.passay.PasswordData
import org.passay.PasswordValidator
import org.passay.RuleResultDetail
import org.passay.WhitespaceRule
import java.net.URI
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

// For correct setup (redirect-urls, apple relay) please read carefully README.md
class FirebaseAuthController(
    private val refreshTokenCommandService: RefreshTokenCommandService,
    private val sessionCommandService: SessionCommandService,
    private val firebaseAuth: FirebaseAuth,
    private val usersCollection: TypedCollectionReference<User>,
    private val hostname: String,
    private val production: Boolean,
    private val pubSub: PubSub,
    private val hostNameMatcher: String = allowedRedirects(production, hostname),
) {
    @Suppress("Unused")
    val routeVerifyIdToken: ContractRoute =
        "/v1/firebase/verify".post(
            summary = "Verify Firebase token and login accordingly",
            tag = "Authentication",
            parameters = object {},
            responses = listOf(
                Status.OK to exampleAuthResponse,
            ),
            receiving = VerifyFirebaseTokenRequest("token"),
            handler = { request, _ ->
                val decodedToken = try {
                    val token = lens<VerifyFirebaseTokenRequest>(request).token
                    firebaseAuth.verifyIdToken(token)
                } catch (e: FirebaseAuthException) {
                    throw ForbiddenException()
                }

                val language = request.header("accept-language")?.take(2)
                val affiliateSource = request.cookie("affiliateSource")?.value
                val extractedUser = decodedToken.toExtractedUser(language, affiliateSource)
                val signInProvider = (decodedToken.claims["firebase"] as Map<*, *>)["sign_in_provider"]?.toString()
                    ?.let {
                        when (it) {
                            "password" -> SignInProvider.PASSWORD
                            "apple.com" -> SignInProvider.APPLE
                            "facebook.com" -> SignInProvider.FACEBOOK
                            "google.com" -> SignInProvider.GOOGLE
                            else -> error("Unknown sign in provider")
                        }
                    }
                    ?: error("Missing sign_in_provider in token ${decodedToken.toJson()}")

                val response = userFactory(extractedUser)
                val (refreshToken, accessToken) = generateTokens(request, response, signInProvider)
                val providerIds = try {
                    firebaseAuth.getUser(decodedToken.uid).providerData.map { it.providerId }
                } catch (e: FirebaseAuthException) {
                    throw UnauthorizedException(e.message)
                }

                log.info(
                    "User ${response.userId} is being logged in with $signInProvider," +
                        " previously using providers: $providerIds",
                    mapOf("userId" to response.userId),
                )

                // if user was not verified before, we have to revoke all previous sessions, since it might be possible
                // that malicious actor created the account before, see HH-2538
                val user = usersCollection[response.userId].get()
                if (!user.isEmailVerifiedOrThirdParty && containsSupportedProviderIds(providerIds)) {
                    sessionCommandService.execute(RevokeAllSessions(user.id, setOfNotNull(refreshToken.sessionId)))
                }

                if (!response.emailVerified && containsSupportedProviderIds(providerIds)) {
                    // if user connects via OAuth after email sign-up, we can consider the email to be verified
                    usersCollection[response.userId].field(User::emailVerified).update(true)
                }

                val authResponse = authResponse(response, refreshToken, accessToken)
                Response(Status.OK).body(authResponse)
                    .withRefreshTokenCookie(refreshToken.token, RefreshController.refreshFullPath)
                    .withAccessTokenCookie(accessToken).let {
                        // in case that impersonation token is sent during login
                        if (request.cookie(IMPERSONATION_TOKEN) != null) {
                            it.withDeletedImpersonateTokenCookie()
                        } else {
                            it
                        }
                    }
            },
        )

    @Suppress("Unused")
    val routeVerifyEmail: ContractRoute =
        "/v1/firebase/finish-email-verification".post(
            summary = "Finish email verification",
            tag = "Authentication",
            parameters = object {},
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = VerifyEmailRequest("verify-email-token"),
            handler = { request, _ ->
                val user = request.getJwtUser()
                val body = lens<VerifyEmailRequest>(request)
                log.info("User ${user.id} is finishing verification of their profile.", mapOf("userId" to user.id))
                // This email token was sent in [routeInitEmailVerification], and this way we verify, that only
                // owner of the mailbox can call this endpoint and verify the email
                validateVerifyEmailToken(user.id, body.verifyEmailToken)
                usersCollection[user.id].field(User::emailVerified).update(true)
                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    val routeAppLoginForWebsite: ContractRoute =
        "/v1/firebase/app-login".post(
            summary = "Generate an app login link in browser",
            tag = "Authentication",
            parameters = object {},
            responses = listOf(
                Status.OK to AppLoginLink("https://firebase…&mode=signIn&oobCode=PuIml223cMgW5HD…&apiKey=NVWjQfSM…"),
            ),
            receiving = exampleEmailProcessingRequest,
            handler = { request, _ ->
                val signedUserId = request.getJwtUser().id

                val user = usersCollection[signedUserId].fetch()
                    ?: throw NotFoundException("User $signedUserId was not found.")

                if (user.email == null) {
                    throw BadRequestException("User does not have an email set")
                }

                val body = lens<EmailProcessingRequest>(request)
                if (!request.userAgent!!.startsWith("Herohero App/")) {
                    throw ForbiddenException()
                }
                if (!body.redirectUrl.isUrlValid(hostNameMatcher)) {
                    throw BadRequestException("Invalid redirect URL")
                }

                val actionCodeSettings = ActionCodeSettings
                    .builder()
                    .setUrl(body.redirectUrl)
                    .setHandleCodeInApp(false)
                    .setIosBundleId(body.iosBundleId)
                    .build()

                val link = try {
                    // TODO expiration in matter of seconds, seems not possible to change?
                    //      https://stackoverflow.com/questions/74169424/firebase-authentication-expiration-duration
                    firebaseAuth.generateSignInWithEmailLink(user.email, actionCodeSettings)
                } catch (e: FirebaseAuthException) {
                    throw HttpStatusException(
                        e.httpResponse.statusCode,
                        e.message,
                        mapOf("userId" to user.id, "errorCode" to e.errorCode, "authErrorCode" to e.authErrorCode),
                        null,
                    )
                }

                Response(Status.OK)
                    .body(AppLoginLink(link))
            },
        )

    data class AppLoginLink(
        val loginUrl: String,
    )

    @Suppress("Unused")
    val routeDeleteUser: ContractRoute =
        "/v1/firebase/delete-user".post(
            summary = "Delete user after email confirmation",
            tag = "Authentication",
            parameters = object {},
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = DeleteMyAccountRequest("deleteToken"),
            handler = { request, _ ->
                val user = request.getJwtUser()
                val body = lens<DeleteMyAccountRequest>(request)
                // This email token was sent in [routeInitEmailVerification], and this way we verify, that only
                // owner of the mailbox can call this endpoint and delete the account
                validateDeleteToken(requesterId = user.id, deleteToken = body.deleteToken)

                log.info("User ${user.id} is deleting of their profile.", mapOf("userId" to user.id))
                val deleteResponse = serviceCall("api", "/v1/users/${user.id}")
                    .httpDelete(
                        listOf(
                            "cancelSubscriptions" to true,
                            "refundSubscriptions" to false,
                            "deletedReason" to DeletedReason.LEAVING_HH,
                        ),
                    )
                    .header("Content-Type", "application/json")
                    .header("Cookie", "$ACCESS_TOKEN=${user.id.authorization(user.sessionId)}")
                    .response()
                    .second

                if (deleteResponse.statusCode >= 300) {
                    throw HttpStatusException(
                        deleteResponse.statusCode,
                        "Could not delete ${user.id}: ${deleteResponse.statusCode}",
                        mapOf("userId" to user.id),
                        null,
                    )
                }

                Response(Status.NO_CONTENT)
                    .withDeletedAccessTokenCookie()
                    .withDeletedImpersonateTokenCookie()
            },
        )

    @Suppress("Unused")
    val routeInitEmailVerification: ContractRoute =
        "/v1/firebase/init-email-verification".post(
            summary = "Initiate email verification",
            tag = "Authentication",
            parameters = object {
                val isDeletion = Query.boolean().defaulted(
                    "isDeletion",
                    false,
                    "Marks this email confirmation as account deletion.",
                )
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = exampleEmailProcessingRequest,
            handler = { request, parameters ->
                val signedUserId = request.getJwtUser().id
                val user = usersCollection[signedUserId].get()
                val body = lens<EmailProcessingRequest>(request)
                if (!body.redirectUrl.isUrlValid(hostNameMatcher)) {
                    throw BadRequestException("Invalid redirect URL")
                }
                val isDeletion = parameters.isDeletion(request)
                val email = user.email ?: throw BadRequestException("User ${user.id} does not have an email set")
                log.info(
                    "User ${user.id} requests " +
                        (if (isDeletion) "deletion confirmation" else "verification") +
                        " of their profile.",
                    mapOf("userId" to user.id),
                )

                val link = try {
                    firebaseAuth.generateEmailVerificationLink(email)
                        ?.let {
                            val firebaseLink = Uri.of(it).removeQuery("apiKey")
                            val redirectUri = Uri.of(body.redirectUrl)
                            val additionalQueries = if (isDeletion) {
                                "&deleteToken=${deleteToken(signedUserId)}"
                            } else {
                                "&verifyEmailToken=${verifyEmailToken(signedUserId)}"
                            }

                            Uri(
                                scheme = redirectUri.scheme,
                                userInfo = redirectUri.userInfo,
                                host = redirectUri.host,
                                port = redirectUri.port,
                                path = redirectUri.path,
                                query = firebaseLink.query + additionalQueries,
                                fragment = redirectUri.fragment,
                            ).toString()
                        }
                } catch (e: FirebaseAuthException) {
                    throw HttpStatusException(
                        e.httpResponse.statusCode,
                        e.message,
                        mapOf("userId" to user.id, "errorCode" to e.errorCode, "authErrorCode" to e.authErrorCode),
                        null,
                    )
                }

                pubSub.publish(
                    EmailPublished(
                        to = email,
                        template = if (isDeletion) "sign-off" else "email-verification",
                        variables = listOf(
                            "user-name" to user.name,
                            "verification-link" to link,
                            "requested-at" to formatDateTime(
                                Instant.now(),
                                user.language,
                                body.timeZone ?: "Europe/Prague",
                            ),
                        ),
                        language = user.language,
                    ),
                )

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    val routeInitEmailChange: ContractRoute =
        "/v1/firebase/init-email-change".post(
            summary = "Initiate email change",
            tag = "Authentication",
            parameters = object {
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = exampleInitiateUpdateEmailRequest,
            handler = { request, _ ->
                val signedUserId = request.getJwtUser().id
                val body = lens<InitiateUpdateEmailRequest>(request)
                val user = usersCollection[signedUserId].get()

                // at least some validation before we try to send it out
                if ('@' !in body.email) {
                    throw BadRequestException("Not an email")
                }
                if (!body.redirectUrl.isUrlValid(hostNameMatcher)) {
                    throw BadRequestException("Invalid redirect URL")
                }

                val usersWithGivenEmail = usersCollection.where(User::email).isEqualTo(body.email).fetchAll()
                if (usersWithGivenEmail.isNotEmpty()) {
                    throw ConflictException("Email ${body.email} is already taken")
                }

                val changeEmailToken = changeEmailToken(signedUserId, body.email)
                val redirectUri = Uri.of(body.redirectUrl).query("token", changeEmailToken)

                pubSub.publish(
                    EmailPublished(
                        to = body.email,
                        template = "email-change-request",
                        variables = listOf(
                            "old-email" to user.email,
                            "new-email" to body.email,
                            "confirmation-link" to redirectUri.toString(),
                        ),
                        language = user.language,
                    ),
                )
                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    val routeEmailChange: ContractRoute =
        "/v1/firebase/email-change".post(
            summary = "Change email",
            tag = "Authentication",
            parameters = object {
            },
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = exampleUpdateEmailRequest,
            handler = { request, _ ->
                val userId = request.getJwtUser().id
                val body = lens<UpdateEmailRequest>(request)
                val email = validateChangeEmailToken(
                    requesterId = request.getJwtUser().id,
                    changeEmailToken = body.token,
                )

                val usersWithGivenEmail = usersCollection.where(User::email).isEqualTo(email).fetchAll()
                if (usersWithGivenEmail.isNotEmpty()) {
                    throw ConflictException("Email $email is already taken")
                }

                usersCollection[userId].field(User::email).update(email)

                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("Unused")
    // TODO (maybe) rename path to /init-password-reset
    val routeInitPasswordReset: ContractRoute =
        "/v1/firebase/password-reset".post(
            summary = "Initiate password reset flow",
            tag = "Authentication",
            parameters = object {},
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            receiving = exampleResetUserPasswordRequest,
            handler = { request, _ ->
                val body = lens<ResetUserPasswordRequest>(request)
                val email = body.email.trim().lowercase()
                val user = usersCollection
                    .where(User::status).isNotEqualTo(UserStatus.DELETED)
                    .and(User::email).isEqualTo(email).fetchSingle()

                if (user == null) {
                    log.error("Cannot reset password for non-existing user: $email")
                    return@post Response(Status.NO_CONTENT)
                }

                try {
                    firebaseAuth.getUserByEmail(email)
                } catch (e: FirebaseAuthException) {
                    if ("No user record found" in (e.message ?: "")) {
                        // for users which were previously signing up with our legacy logins, we need to create
                        // new firebase user before requesting a password reset
                        firebaseAuth.createUser(UserRecord.CreateRequest().setEmail(email))
                    }
                }
                log.info("User ${user.id} is requesting a reset password link.", mapOf("userId" to user.id))

                val resetLink = try {
                    firebaseAuth.generatePasswordResetLink(email)
                        ?.let {
                            val firebaseLink = URI(it)
                            val hostname = URI(hostname)
                            val path = body.callbackPath ?: (if (body.isOauthFlow) "/oauth-login" else "")
                            val query = firebaseLink.query + "&" + (body.callbackQuery ?: "")
                            URI(hostname.scheme, hostname.authority, path, query, "").toString()
                        }
                } catch (e: FirebaseAuthException) {
                    throw HttpStatusException(
                        e.httpResponse.statusCode,
                        e.message,
                        mapOf("userId" to user.id, "errorCode" to e.errorCode, "authErrorCode" to e.authErrorCode),
                        null,
                    )
                }

                // we don't return any error status,
                // so this endpoint cannot be used to check if user exists in our database
                if (resetLink == null) {
                    log.fatal("Could not generate a password reset link for: $email")
                    Response(Status.NO_CONTENT)
                } else {
                    pubSub.publish(
                        EmailPublished(
                            to = email,
                            template = "password-reset",
                            variables = listOf(
                                "user-name" to user.name,
                                "password-reset-link" to resetLink,
                            ),
                            language = user.language,
                        ),
                    )
                    Response(Status.NO_CONTENT)
                }
            },
        )

    @Suppress("Unused")
    val routeSignUp: ContractRoute =
        "/v1/firebase/sign-up".post(
            summary = "Sign up and login a new user with an email-password combination. " +
                "Their email must be verified separately.",
            tag = "Authentication",
            parameters = object {},
            responses = listOf(Status.OK to exampleAuthResponse),
            receiving = exampleSignUpRequest,
            handler = { request, _ ->
                val body = lens<SignUpRequest>(request)
                val email = body.email.trim().lowercase()
                val user = usersCollection
                    .where(User::status).isNotEqualTo(UserStatus.DELETED)
                    .and(User::email).isEqualTo(email).fetchSingle()

                if (user != null) {
                    throw BadRequestException("User with email $email already exists")
                }

                val result = passwordValidator.validate(PasswordData(body.password))
                if (!result.isValid) {
                    throw BadRequestException(
                        "Invalid password",
                        body = UpdateUserPasswordErrorResponse(result.details),
                    )
                }

                val firebaseUser = try {
                    val firebaseUser = firebaseAuth.getUserByEmail(email)
                    // TODO how can this happen? this should never be the case?
                    log.error(
                        "User $email did not exist in Herohero, but existed in Firebase. " +
                            "That is highly suspicious and require a double-check.",
                    )
                    firebaseUser
                } catch (e: FirebaseAuthException) {
                    if (e.errorCode != ErrorCode.NOT_FOUND) {
                        throw IllegalStateException(e)
                    }
                    firebaseAuth.createUser(UserRecord.CreateRequest().setEmail(email).setPassword(body.password))
                }
                val extractedUser = ExtractedUser(
                    id = firebaseUser.uid,
                    email = email,
                    name = email.replace("@.*".toRegex(), ""),
                    provider = OAuthProvider.FIREBASE,
                    meta = emptyMap(),
                    tokenExpiresAt = null,
                    language = request.header("accept-language")?.take(2),
                    accessToken = null,
                    refreshToken = null,
                    imageUrl = null,
                    secondaryId = null,
                    userId = null,
                )

                val response = userFactory(extractedUser)

                log.info("User ${response.userId} is being signed up.", mapOf("userId" to response.userId))

                val (refreshToken, accessToken) = generateTokens(request, response, SignInProvider.PASSWORD)

                val authResponse = authResponse(response, refreshToken, accessToken)
                Response(Status.OK).body(authResponse)
                    .withRefreshTokenCookie(refreshToken.token, RefreshController.refreshFullPath)
                    .withAccessTokenCookie(accessToken).let {
                        // in case that impersonation token is sent during login
                        if (request.cookie(IMPERSONATION_TOKEN) != null) {
                            it.withDeletedImpersonateTokenCookie()
                        } else {
                            it
                        }
                    }
            },
        )

    private fun generateTokens(
        request: Request,
        response: UserIdResponse,
        signInProvider: SignInProvider,
    ): Pair<RefreshToken, String> {
        val (accessTokenExpiry, refreshTokenExpiry) = tokenExpirations()
        val userAgent = request.userAgent ?: throw BadRequestException("Missing user agent")
        val generateTokenCommand = GenerateRefreshToken(
            userId = response.userId,
            refreshTokenExpiresAt = refreshTokenExpiry,
            userAgent = userAgent,
            deviceId = request.deviceId,
            signInProvider = signInProvider,
            ipAddress = request.remoteAddress,
            signInLocation = request.location,
        )
        val refreshToken = refreshTokenCommandService.execute(generateTokenCommand)
        val accessToken = JwtUser(
            response.userId,
            accessTokenExpiry,
            response.role.ordinal,
            refreshToken.sessionId,
        ).toJwt()

        return refreshToken to accessToken
    }
}

private fun authResponse(
    response: UserIdResponse,
    refreshToken: RefreshToken,
    accessToken: String,
) = AuthResponse(
    status = Status.OK.code,
    userId = response.userId,
    role = response.role.ordinal,
    isNew = response.state == UserStateChange.CREATED,
    userState = response.state,
    sessionId = refreshToken.sessionId,
    accessToken = accessToken,
    refreshToken = refreshToken.token,
)

private fun userFactory(user: ExtractedUser): UserIdResponse =
    try {
        serviceCall("api", "/v1/users")
            .httpPost()
            .header("Content-Type", "application/json")
            .body(user.map().toJwt())
            .fetch<UserIdResponse>()
    } catch (e: FuelException) {
        log.info("User ${user.userId}/${user.email} cannot log in: ${e.message}", mapOf("userId" to user.id))
        throw HttpStatusException(e.status, body = null, labels = mapOf())
    }

private val passwordValidator = PasswordValidator(
    listOf(
        LengthRule(8, 50),
        WhitespaceRule(),
    ),
)

private fun formatDateTime(
    dateTime: Instant,
    language: String?,
    timeZone: String = "",
): String? =
    when (language?.lowercase()) {
        "cs", "de", "es", "sk" -> defaultDateTimeFormatter.withZone(ZoneId.of(timeZone)).format(dateTime)
        else -> englishDateTimeFormatter.withZone(ZoneId.of(timeZone)).format(dateTime)
    }

private val englishDateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
private val defaultDateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm")

private fun FirebaseToken.toExtractedUser(
    language: String?,
    affiliateSource: String?,
): ExtractedUser {
    val firebaseClaim = claims["firebase"]
    val meta = (firebaseClaim as? Map<*, *>)
        ?.let { it["identities"] as? Map<*, *> }
        ?.let { it["facebook.com"] as? List<*>? }
        ?.firstNotNullOfOrNull { it as? String }
        ?.let {
            mapOf("facebookId" to it)
        } ?: mapOf()

    return ExtractedUser(
        id = uid,
        secondaryId = null,
        userId = null,
        email = email,
        accessToken = null,
        imageUrl = null,
        tokenExpiresAt = null,
        refreshToken = null,
        meta = meta,
        name = name ?: email?.replace("@.*".toRegex(), "") ?: "",
        provider = OAuthProvider.FIREBASE,
        language = language,
        affiliateSource = affiliateSource,
    )
}

data class VerifyFirebaseTokenRequest(val token: String)

data class DeleteMyAccountRequest(val deleteToken: String)

data class VerifyEmailRequest(val verifyEmailToken: String)

data class EmailProcessingRequest(
    val redirectUrl: String,
    val timeZone: String? = null,
    val iosBundleId: String? = null,
)

data class InitiateUpdateEmailRequest(
    val email: String,
    val redirectUrl: String,
)

data class UpdateEmailRequest(
    val token: String,
)

data class ResetUserPasswordRequest(
    val email: String,
    val callbackPath: String?,
    val callbackQuery: String?,
    // TODO remove after https://linear.app/herohero/issue/HH-4116/change-route-of-generated-link-for-reset-password#comment-caba5984
    @Deprecated("Use `callbackPath` + `callbackQuery`.")
    val isOauthFlow: Boolean = false,
)

data class SignUpRequest(val email: String, val password: String, val language: String)

data class UpdateUserPasswordErrorResponse(val details: List<RuleResultDetail>)

private fun containsSupportedProviderIds(providerIds: Iterable<String>) =
    supportedProviderIds.intersect(providerIds.toSet()).isNotEmpty()

private val supportedProviderIds = setOf("google.com", "facebook.com", "apple.com")
