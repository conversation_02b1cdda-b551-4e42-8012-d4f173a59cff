package hero.auth.controller.dto

import hero.auth.controller.AuthResponse
import hero.auth.controller.EmailProcessingRequest
import hero.auth.controller.InitiateUpdateEmailRequest
import hero.auth.controller.ResetUserPasswordRequest
import hero.auth.controller.SignUpRequest
import hero.auth.controller.UpdateEmailRequest
import hero.model.UserStateChange
import java.util.UUID

val exampleAuthResponse = AuthResponse(
    status = 200,
    userId = "user-id",
    role = 0,
    isNew = true,
    userState = UserStateChange.DELETED,
    sessionId = UUID.randomUUID().toString(),
)

val exampleEmailProcessingRequest = EmailProcessingRequest(
    redirectUrl = "auth-devel.herohero.co",
    iosBundleId = "bundle-id",
)

val exampleInitiateUpdateEmailRequest = InitiateUpdateEmailRequest(
    email = "<EMAIL>",
    redirectUrl = "auth-devel.herohero.co",
)

val exampleUpdateEmailRequest = UpdateEmailRequest(
    token = "token",
)

val exampleResetUserPasswordRequest = ResetUserPasswordRequest(
    email = "<EMAIL>",
    callbackPath = "/callback-path",
    callbackQuery = "view=default&isTerezaCool=true",
    isOauthFlow = false,
)

val exampleSignUpRequest = SignUpRequest(
    email = "<EMAIL>",
    password = "SecurePassword",
    language = "en",
)
