package hero.auth.service

import hero.baseutils.plus
import hero.test.IntegrationTest
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class SessionCommandServiceIT : IntegrationTest() {
    @Nested
    inner class RevokeSession {
        @Test
        fun `should revoke session`() {
            val underTest = SessionCommandService(TestRepositories.sessionRepository, lazyTestContext)
            val now = Instant.ofEpochSecond(1729166826)
            val session = testHelper.createSession("cestmir", createdAt = now, refreshedAt = now, revoked = false)

            underTest.execute(RevokeSession(session.id, "cestmir"))

            val updatedSession = TestRepositories.sessionRepository.getById(session.id)
            assertThat(updatedSession.revoked).isTrue()
        }
    }

    @Nested
    inner class RevokeAllSessions {
        @Test
        fun `should revoke all sessions, but only user's sessions`() {
            val underTest = SessionCommandService(TestRepositories.sessionRepository, lazyTestContext)
            val now = Instant.ofEpochSecond(1729166826)
            val then = now + 5.seconds
            val session1 = testHelper.createSession("cestmir", createdAt = now, refreshedAt = now, revoked = false)
            val session2 = testHelper.createSession("cestmir", createdAt = then, refreshedAt = then, revoked = false)

            val anotherUsersSession = testHelper.createSession("themag", revoked = false)

            underTest.execute(RevokeAllSessions("cestmir"))

            assertThat(TestRepositories.sessionRepository.getById(session1.id)).isEqualTo(session1.copy(revoked = true))
            assertThat(TestRepositories.sessionRepository.getById(session2.id)).isEqualTo(session2.copy(revoked = true))
            assertThat(TestRepositories.sessionRepository.getById(anotherUsersSession.id))
                .isEqualTo(anotherUsersSession)
        }

        @Test
        fun `should revoke all sessions except excluded ones`() {
            val underTest = SessionCommandService(TestRepositories.sessionRepository, lazyTestContext)
            val now = Instant.ofEpochSecond(1729166826)
            val then = now + 5.seconds
            val session1 = testHelper.createSession("cestmir", createdAt = now, refreshedAt = now, revoked = false)
            val session2 = testHelper.createSession("cestmir", createdAt = then, refreshedAt = then, revoked = false)

            underTest.execute(RevokeAllSessions("cestmir", setOf(session1.id)))

            assertThat(TestRepositories.sessionRepository.getById(session1.id)).isEqualTo(session1)
            assertThat(TestRepositories.sessionRepository.getById(session1.id).revoked).isFalse()
            assertThat(TestRepositories.sessionRepository.getById(session2.id)).isEqualTo(session2.copy(revoked = true))
        }
    }
}
