plugins {
    id("hero.http-function-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:Jwt"))
    implementation(projectModule(":Modules:Exceptions"))
    implementation(projectModule(":Modules:Contract"))
    implementation(projectModule(":Function:Rss:RssFeedGenerator"))
    implementation(projectModule(":Modules:SQL"))
    implementation(projectModule(":Modules:Repository"))

    testImplementation(projectModule(":Modules:IntegrationTesting"))
}
