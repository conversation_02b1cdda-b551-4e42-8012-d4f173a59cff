Kotlin/Function/UploadEventHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/UploadEventHandler/variables:
  variables:
    FUNCTION_NAME: "upload-event-handler"
    CLASS_NAME: "hero.functions.UploadEventHandler"
    TOPIC: "UploadEvent"
    ADDITIONAL_PARAMETERS: "--retry"

Kotlin/Function/UploadEventHandler/deploy-devel:
  needs:
    - Kotlin/Function/UploadEventHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/UploadEventHandler/variables

Kotlin/Function/UploadEventHandler/deploy-staging:
  needs:
    - Kotlin/Function/UploadEventHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/UploadEventHandler/variables

Kotlin/Function/UploadEventHandler/deploy-prod:
  needs:
    - Kotlin/Function/UploadEventHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/UploadEventHandler/variables
  variables:
    MAX_INSTANCES: 20
