package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.jackson.toJson
import hero.model.topics.UploadEvent
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.UPLOAD_TRACKING
import hero.sql.jooq.tables.records.UploadTrackingRecord
import org.jooq.DSLContext
import org.jooq.JSONB
import org.jooq.Record
import org.jooq.UpdateSetStep
import org.jooq.impl.DSL

class UploadEventHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val logger: Logger = log,
) : PubSubSubscriber<UploadEvent>(systemEnv) {
    private val context by lazyContext

    override fun consume(payload: UploadEvent) {
        logger.info("Received event $payload")
        when (payload) {
            is UploadEvent.SinglePartUploadInitiated -> handleSinglePartUploadInitiated(payload)
            is UploadEvent.MultipartUploadInitiated -> handleMultipartUploadInitiated(payload)
            is UploadEvent.MultipartUploadAborted -> handleMultipartUploadAborted(payload)
            is UploadEvent.MultipartUploadCompleted -> handleMultipartUploadCompleted(payload)
            is UploadEvent.AssetEncodingInitiated -> handleAssetEncodingInitiated(payload)
            is UploadEvent.AssetEncodingCompleted -> handleAssetEncodingCompleted(payload)
        }
        logger.info("Finished processing event $payload")
    }

    private fun handleSinglePartUploadInitiated(event: UploadEvent.SinglePartUploadInitiated) {
        logger.info("User ${event.userId} has initiated a single part upload request key ${event.requestKey}")
        val uploadTrackingRecord = UploadTrackingRecord()
            .apply {
                this.userId = event.userId
                this.mimeType = event.mimeType
                this.partsNumber = 0
                this.contentLength = 0
                this.uploadId = null
                this.requestKey = event.requestKey
                this.preSignedUrl = event.preSignedUrl
                this.createdAt = event.timestamp
                this.updatedAt = event.timestamp
                this.events = JSONB.valueOf("[]")
            }

        context
            .insertInto(UPLOAD_TRACKING)
            .set(uploadTrackingRecord)
            .execute()
    }

    private fun handleMultipartUploadInitiated(event: UploadEvent.MultipartUploadInitiated) {
        logger.info("User ${event.userId} has initiated a multipart upload with id ${event.uploadId}")
        val uploadTrackingRecord = UploadTrackingRecord()
            .apply {
                this.userId = event.userId
                this.mimeType = event.mimeType
                this.partsNumber = event.partsNumber
                this.contentLength = event.contentLength
                this.uploadId = event.uploadId
                this.requestKey = event.requestKey
                this.preSignedUrl = event.preSignedUrl
                this.createdAt = event.timestamp
                this.updatedAt = event.timestamp
                this.events = JSONB.valueOf("[]")
            }

        context
            .insertInto(UPLOAD_TRACKING)
            .set(uploadTrackingRecord)
            .execute()
    }

    private fun handleMultipartUploadAborted(event: UploadEvent.MultipartUploadAborted) {
        logger.info("User ${event.userId} has aborted a multipart upload with request key ${event.requestKey}")
        context
            .update(UPLOAD_TRACKING)
            .set(UPLOAD_TRACKING.UPDATED_AT, event.timestamp)
            .set(UPLOAD_TRACKING.ABORTED_AT, event.timestamp)
            .addEvent(event)
            .where(UPLOAD_TRACKING.REQUEST_KEY.eq(event.requestKey))
            .execute()
    }

    private fun handleMultipartUploadCompleted(event: UploadEvent.MultipartUploadCompleted) {
        logger.info("User ${event.userId} has completed a multipart upload with request key ${event.requestKey}")
        context
            .update(UPLOAD_TRACKING)
            .set(UPLOAD_TRACKING.UPDATED_AT, event.timestamp)
            .let { if (event.result == true) it.set(UPLOAD_TRACKING.COMPLETED_AT, event.timestamp) else it }
            .addEvent(event)
            .where(UPLOAD_TRACKING.REQUEST_KEY.eq(event.requestKey))
            .execute()
    }

    private fun handleAssetEncodingInitiated(event: UploadEvent.AssetEncodingInitiated) {
        val match = requestKeyFromURLRegex.find(event.url)
        val requestKey = match?.groupValues?.get(1) ?: error("Failed to extract requestKey from ${event.url}")
        logger.info("Initiating encoding of asset ${event.assetId} for requestKey $requestKey")

        val updatedCount = context
            .update(UPLOAD_TRACKING)
            .set(UPLOAD_TRACKING.UPDATED_AT, event.timestamp)
            .let {
                if (event.assetId != null) {
                    it
                        .set(UPLOAD_TRACKING.ENCODING_STARTED_AT, event.timestamp)
                        .set(UPLOAD_TRACKING.ASSET_ID, event.assetId)
                } else {
                    it
                }
            }
            .addEvent(event)
            .where(UPLOAD_TRACKING.REQUEST_KEY.eq(requestKey))
            .execute()

        if (updatedCount < 1) {
            // we throw an error and we let pubsub retry
            // this can happen for single part uploads when the SinglePartUploadInitiated is processed after
            // the AssetEncodingInitiated event.
            error("Failed to record asset encoding for event $event")
        }
    }

    private fun handleAssetEncodingCompleted(event: UploadEvent.AssetEncodingCompleted) {
        context
            .update(UPLOAD_TRACKING)
            .set(UPLOAD_TRACKING.UPDATED_AT, event.timestamp)
            .set(UPLOAD_TRACKING.ENCODING_COMPLETED_AT, event.timestamp)
            .addEvent(event)
            .where(UPLOAD_TRACKING.ASSET_ID.eq(event.assetId))
            .execute()
    }

    private fun UpdateSetStep<out Record>.addEvent(uploadEvent: UploadEvent) =
        this.set(
            UPLOAD_TRACKING.EVENTS,
            DSL.field("{0} || {1}", JSONB::class.java, UPLOAD_TRACKING.EVENTS, JSONB.valueOf(uploadEvent.toJson())),
        )
}

private val requestKeyFromURLRegex = """/uploads/([a-f0-9]{32})/""".toRegex()
