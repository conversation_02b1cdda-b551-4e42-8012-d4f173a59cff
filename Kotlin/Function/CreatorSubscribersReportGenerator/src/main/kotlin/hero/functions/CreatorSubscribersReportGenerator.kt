package hero.functions

import com.stripe.model.Coupon
import hero.baseutils.SystemEnv
import hero.exceptions.http.ForbiddenException
import hero.gcloud.BatchUpdateSpreadsheet
import hero.gcloud.CellHorizontalAlignment.RIGHT
import hero.gcloud.DATE_ONLY_PATTERN
import hero.gcloud.Format
import hero.gcloud.Format.Companion.MONO_FONT
import hero.gcloud.FormattedValue
import hero.gcloud.MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR
import hero.gcloud.PERCENT_PATTERN
import hero.gcloud.SHEETS_MAX_CHAR_IN_CELL
import hero.gcloud.SheetsCurrency
import hero.gcloud.SpreadsheetRef
import hero.gcloud.addSheet
import hero.gcloud.appendFormatted
import hero.gcloud.batched
import hero.gcloud.countRange
import hero.gcloud.currency
import hero.gcloud.date
import hero.gcloud.emptyCell
import hero.gcloud.firestore
import hero.gcloud.formula
import hero.gcloud.number
import hero.gcloud.renameSheet
import hero.gcloud.resizeColumns
import hero.gcloud.shareableLink
import hero.gcloud.string
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gcloud.writeHeader
import hero.http4k.controller.HeaderUtils
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.serverless.CloudHttpFunction
import hero.model.CouponMethod
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.Tier
import hero.model.User
import hero.model.UserStatus
import hero.model.topics.GenerateSubscribersReportRequest
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.tables.Coupon.COUPON
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status.Companion.FOUND
import org.http4k.core.Status.Companion.OK
import org.http4k.serverless.GoogleCloudHttpFunction
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Instant

@Suppress("Unused")
class CreatorSubscribersReportGenerator(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
) : CloudHttpFunction() {
    @Suppress("Unused")
    class EntryClass : GoogleCloudHttpFunction(CreatorSubscribersReportGenerator())

    // this must be lazy otherwise function deployment fails when the function is first instantiated
    private val context: DSLContext by lazyContext
    private val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    private val subscriberRepository = firestore.typedCollectionOf(Subscriber)
    private val userRepository = firestore.typedCollectionOf(User)

    @Suppress("unused")
    private val routeGenerateReport: ContractRoute =
        "/".post(
            summary = "Generate subscribers report",
            tag = "CreatorSubscribersReportGenerator",
            parameters = object {},
            responses = listOf(OK to Unit),
            receiving = null,
            handler = { request, _ ->
                if (!HeaderUtils.hasValidApiKey(request)) {
                    throw ForbiddenException()
                }
                val body = lens<GenerateSubscribersReportRequest>(request)
                val spreadsheet = SpreadsheetRef(body.spreadsheetId)
                val reportLink = spreadsheet.shareableLink()
                    ?: error("Failed to generate subs report for creator ${body.creatorId}")

                generateSubscribers(spreadsheet, body.creatorId, body.privacyPolicyEffectiveAt)
                generateInvites(spreadsheet, body.creatorId)
                generateGifts(spreadsheet, body.creatorId)

                Response(FOUND)
                    .header("Location", reportLink)
            },
        )

    override fun contractRoutes(): List<ContractRoute> = listOf(routeGenerateReport)

    private fun generateSubscribers(
        spreadsheet: SpreadsheetRef,
        creatorId: String,
        policyEffectiveAt: Instant?,
    ) {
        val subs = subscriberRepository.where(Subscriber::creatorId).isEqualTo(creatorId).fetchAll()
        val (activeSubs, inactiveSubs) = subs
            .sortedBy { it.subscribed }
            .partition { it.status == SubscriberStatus.ACTIVE }

        val userById = subs
            .asSequence()
            .map { it.userId }
            .chunked(MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR)
            .map { userRepository.where(User::id).isIn(it).fetchAll() }
            .flatten()
            .associateBy { it.id }

        val activeSheet = prepareSubscribersData(activeSubs, userById, policyEffectiveAt, true)
        spreadsheet
            .batched()
            .renameSheet(0, "Subscribers")
            .writeSubsSheet(activeSheet)
            .execute()

        val inactiveSheetId = spreadsheet.addSheet("Inactive subscribers")
        val inactiveSheet = prepareSubscribersData(inactiveSubs, userById, policyEffectiveAt)
        spreadsheet
            .batched()
            .writeSubsSheet(inactiveSheet, inactiveSheetId)
            .execute()
    }

    private fun BatchUpdateSpreadsheet.writeSubsSheet(
        sheetData: SheetData,
        sheetId: Int = 0,
    ) = this
        .writeHeader(sheetData.header, sheetId)
        .appendFormatted(sheetData.data, sheetId)
        .appendFormatted(sheetData.footer, sheetId)
        .resizeColumns(1, sheetData.header.size, 150, sheetId)

    private fun prepareSubscribersData(
        activeSubs: List<Subscriber>,
        userByIds: Map<String, User>,
        policyEffectiveAt: Instant?,
        activeSheet: Boolean = false,
    ): SheetData {
        val discountColumn = string("Discount", Format(horizontalAlignment = RIGHT))
        val header = listOfNotNull(
            string("Name"),
            if (policyEffectiveAt != null) string("Email") else null,
            string("Subscription value", Format(horizontalAlignment = RIGHT)),
            string("Status", Format(horizontalAlignment = RIGHT)),
            string("Subscribed", Format(horizontalAlignment = RIGHT)),
            string("Ending", Format(horizontalAlignment = RIGHT)),
            discountColumn,
            string("Discount ends", Format(horizontalAlignment = RIGHT)),
            string("Invite used"),
            string("Campaign"),
        )

        val data = activeSubs
            .map { renderSubscribersData(it, userByIds, policyEffectiveAt) }

        // discount column index
        val i = 'A' + header.indexOf(discountColumn)
        val footer = if (activeSheet) {
            val endRow = 1 + data.size
            listOf(
                footer(data),
                listOf(
                    string("Paying subscribers", Format(bold = true)),
                    if (data.isNotEmpty()) {
                        formula("=ROWS(${i}2:$i$endRow) - COUNTIF(${i}2:$i$endRow, \"100%\")", Format(bold = true))
                    } else {
                        number(0, Format(bold = true))
                    },
                ),
                listOf(
                    string("Free subscribers", Format(bold = true)),
                    if (data.isNotEmpty()) {
                        formula("=COUNTIF(${i}2:$i$endRow, \"100%\")", Format(bold = true))
                    } else {
                        number(0, Format(bold = true))
                    },
                ),
            )
        } else {
            listOf(footer(data))
        }
        return SheetData(header, data, footer)
    }

    private fun footer(data: List<List<FormattedValue>>): List<FormattedValue> =
        listOf(
            string("Total", Format(bold = true)),
            if (data.isNotEmpty()) {
                formula(countRange("A2", "A${1 + data.size}"), Format(bold = true))
            } else {
                number(0, Format(bold = true))
            },
        )

    private fun renderSubscribersData(
        it: Subscriber,
        userByIds: Map<String, User>,
        policyEffectiveAt: Instant?,
    ): List<FormattedValue> {
        // we propagate email only for subscriptions which are either active or were cancelled AFTER
        // the creators' exports were enabled (given by `policyEffectiveAt`)
        val renderEmail = policyEffectiveAt != null &&
            (
                it.status == SubscriberStatus.ACTIVE ||
                    // some old subscriptions may have cancelledAt null
                    (it.cancelledAt != null && policyEffectiveAt < it.cancelledAt)
            )
        val tier = Tier.ofId(it.tierId)
        val cancelledOrExpiresAt = (if (it.cancelAtPeriodEnd) it.expires else null)
            ?: it.cancelAt ?: it.cancelledAt

        val couponPercentOff =
            if (it.couponExpiresAt != null && it.couponExpiresAt!! < Instant.now())
                null
            else
                it.couponPercentOff?.toBigDecimal()?.divide(100.toBigDecimal())

        val status = if (it.status == SubscriberStatus.ACTIVE && it.cancelAtPeriodEnd)
            "ending soon"
        else
            it.status.name.lowercase().replace("_", " ")

        val user = userByIds[it.userId]

        val userName = if (user == null || user.status == UserStatus.DELETED) {
            "Deleted user"
        } else {
            user.name
        }

        return listOfNotNull(
            string(userName, note = "https://herohero.co/${it.userId}"),
            when {
                renderEmail -> string(user?.email)
                policyEffectiveAt != null -> emptyCell()
                else -> null
            },
            currency(tier.priceCents / 100, SheetsCurrency.valueOf(tier.currency.name)),
            string(status, Format(horizontalAlignment = RIGHT), note = statusNote(it.status)),
            date(it.subscribed, Format(horizontalAlignment = RIGHT), DATE_ONLY_PATTERN),
            cancelledOrExpiresAt?.let { date(it, Format(horizontalAlignment = RIGHT), DATE_ONLY_PATTERN) }
                ?: string(""),
            number(couponPercentOff, Format(horizontalAlignment = RIGHT), PERCENT_PATTERN),
            it.couponExpiresAt
                ?.let { expires -> date(expires, Format(horizontalAlignment = RIGHT), DATE_ONLY_PATTERN) }
                ?: string(if (it.couponPercentOff != null) "∞" else null, Format(horizontalAlignment = RIGHT)),
            string(it.couponId),
            string(it.couponCampaign),
        )
    }

    private fun generateGifts(
        spreadsheet: SpreadsheetRef,
        creatorId: String,
    ) {
        val couponsSheetId = spreadsheet.addSheet("Gifts")
        val couponsData = context
            .select(
                COUPON.STRIPE_ID,
                COUPON.CREATED_AT,
                COUPON.PURCHASED_BY,
                COUPON.AMOUNT_OFF,
                COUPON.PERCENT_OFF,
                COUPON.CURRENCY,
                COUPON.MAX_REDEMPTIONS,
                COUPON.TIMES_REDEEMED,
                COUPON.REDEEM_BY,
                COUPON.CAMPAIGN,
                COUPON.DURATION_IN_MONTHS,
            )
            .from(COUPON)
            .where(DSL.value(creatorId).eq(COUPON.CREATOR_ID))
            .and(DSL.value(CouponMethod.VOUCHER.name).eq(COUPON.METHOD))
            .and(COUPON.DELETED_AT.isNull)
            .fetch()
            .map {
                Coupon().also { coupon ->
                    coupon.id = it[COUPON.STRIPE_ID]
                    coupon.created = it[COUPON.CREATED_AT].epochSecond
                    coupon.timesRedeemed = it[COUPON.TIMES_REDEEMED]?.toLong()
                    coupon.amountOff = it[COUPON.AMOUNT_OFF]?.toLong()
                    coupon.currency = it[COUPON.CURRENCY]
                    coupon.redeemBy = it[COUPON.REDEEM_BY]?.epochSecond
                    coupon.percentOff = it[COUPON.PERCENT_OFF]?.toBigDecimal()
                    coupon.maxRedemptions = it[COUPON.MAX_REDEMPTIONS]?.toLong()
                    coupon.durationInMonths = it[COUPON.DURATION_IN_MONTHS]?.toLong()
                    coupon.metadata = mapOf("campaign" to it[COUPON.CAMPAIGN])
                }
            }

        val couponsSheet = prepareCouponsData(couponsData, true)
        spreadsheet
            .batched()
            .writeCouponsSheet(couponsSheet, couponsSheetId)
            .execute()
    }

    private fun generateInvites(
        spreadsheet: SpreadsheetRef,
        creatorId: String,
    ) {
        val couponsSheetId = spreadsheet.addSheet("Invites")
        val sumTimesRedeemed = DSL.sum(COUPON.TIMES_REDEEMED)
        val sumMaxRedemptions = DSL.sum(COUPON.MAX_REDEMPTIONS)
        val couponsData = context
            .select(
                DSL.groupConcat(COUPON.STRIPE_ID).`as`(COUPON.STRIPE_ID),
                DSL.groupConcatDistinct(COUPON.CREATOR_ID).`as`(COUPON.CREATOR_ID),
                DSL.min(COUPON.CREATED_AT).`as`(COUPON.CREATED_AT),
                DSL.min(COUPON.PURCHASED_BY).`as`(COUPON.PURCHASED_BY),
                COUPON.AMOUNT_OFF,
                COUPON.PERCENT_OFF,
                COUPON.CURRENCY,
                sumMaxRedemptions,
                sumTimesRedeemed,
                DSL.min(COUPON.REDEEM_BY).`as`(COUPON.REDEEM_BY),
                COUPON.CAMPAIGN,
                COUPON.DURATION_IN_MONTHS,
            )
            .from(COUPON)
            .where(DSL.value(creatorId).eq(COUPON.CREATOR_ID))
            .and(DSL.value(CouponMethod.VOUCHER.name).notEqual(COUPON.METHOD))
            .and(COUPON.DELETED_AT.isNull)
            .groupBy(
                COUPON.CAMPAIGN,
                COUPON.AMOUNT_OFF,
                COUPON.PERCENT_OFF,
                COUPON.CURRENCY,
                COUPON.DURATION_IN_MONTHS,
            )
            .fetch()
            .map {
                Coupon().also { coupon ->
                    coupon.id = it[COUPON.STRIPE_ID].replace(",", ", ").take(SHEETS_MAX_CHAR_IN_CELL)
                    coupon.created = it[COUPON.CREATED_AT].epochSecond
                    coupon.timesRedeemed = it[sumTimesRedeemed]?.toLong()
                    coupon.amountOff = it[COUPON.AMOUNT_OFF]?.toLong()
                    coupon.currency = it[COUPON.CURRENCY]
                    coupon.redeemBy = it[COUPON.REDEEM_BY]?.epochSecond
                    coupon.percentOff = it[COUPON.PERCENT_OFF]?.toBigDecimal()
                    coupon.maxRedemptions = it[sumMaxRedemptions]?.toLong()
                    coupon.durationInMonths = it[COUPON.DURATION_IN_MONTHS]?.toLong()
                    coupon.metadata = mapOf("campaign" to it[COUPON.CAMPAIGN])
                }
            }

        val couponsSheet = prepareCouponsData(couponsData, false)

        spreadsheet
            .batched()
            .writeCouponsSheet(couponsSheet, couponsSheetId)
            .execute()
    }

    private fun prepareCouponsData(
        coupons: List<Coupon>,
        isGift: Boolean,
    ): SheetData {
        val header = listOfNotNull(
            string(if (isGift) "Gift" else "Invite"),
            string(if (isGift) "Value" else "Discount", Format(horizontalAlignment = RIGHT)),
            string("Period (months)", Format(horizontalAlignment = RIGHT)),
            string("Created", Format(horizontalAlignment = RIGHT)),
            string("Redemptions", Format(horizontalAlignment = RIGHT)),
            if (isGift) null else string("Redeem by", Format(horizontalAlignment = RIGHT)),
            if (isGift) null else string("Campaign"),
        )

        val data = coupons
            .map { renderCouponsData(it, isGift) }

        return SheetData(header, data, listOf(footer(data)))
    }

    private fun renderCouponsData(
        coupon: Coupon,
        isGift: Boolean,
    ): List<FormattedValue> =
        listOfNotNull(
            string(coupon.id, Format(fontFamily = MONO_FONT)),
            if (coupon.percentOff != null) {
                number(
                    coupon.percentOff?.movePointLeft(2),
                    Format(horizontalAlignment = RIGHT),
                    PERCENT_PATTERN,
                )
            } else {
                currency(
                    coupon.amountOff?.toLong()?.div(100),
                    coupon.currency?.let { SheetsCurrency.valueOf(it.uppercase()) } ?: SheetsCurrency.EUR,
                )
            },
            number(coupon.durationInMonths),
            date(Instant.ofEpochSecond(coupon.created), Format(horizontalAlignment = RIGHT), DATE_ONLY_PATTERN),
            string("${coupon.timesRedeemed} / ${coupon.maxRedemptions}", Format(horizontalAlignment = RIGHT)),
            if (isGift) null else date(coupon.redeemBy?.let { Instant.ofEpochSecond(it) }),
            if (isGift) null else string(coupon.metadata["campaign"]),
        )

    private fun BatchUpdateSpreadsheet.writeCouponsSheet(
        sheetData: SheetData,
        sheetId: Int = 0,
    ) = this
        .writeHeader(sheetData.header, sheetId)
        .appendFormatted(sheetData.data, sheetId)
        .resizeColumns(1, sheetData.header.size, 150, sheetId)

    // https://mrcoles.com/stripe-api-subscription-status/
    private fun statusNote(status: SubscriberStatus): String? =
        when (status) {
            SubscriberStatus.ACTIVE -> null
            SubscriberStatus.UNPAID -> null
            SubscriberStatus.TRIALING -> null
            SubscriberStatus.INCOMPLETE -> null
            SubscriberStatus.INCOMPLETE_EXPIRED -> null
            SubscriberStatus.PAST_DUE -> "The most recent payment didn't go through or is still pending."
            SubscriberStatus.CANCELLED -> null
        }
}

private data class SheetData(
    val header: List<FormattedValue>,
    val data: List<List<FormattedValue>>,
    val footer: List<List<FormattedValue>>,
)
