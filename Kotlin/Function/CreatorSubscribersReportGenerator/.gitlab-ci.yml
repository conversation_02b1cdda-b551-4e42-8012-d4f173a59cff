Kotlin/Function/CreatorSubscribersReportGenerator/build:
  stage: build-services
  needs:
    - Kotlin/Modules/Stripe/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Http4k/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/CreatorSubscribersReportGenerator/variables:
  variables:
    FUNCTION_NAME: "subscribers-report-generator"
    CLASS_NAME: "hero.functions.CreatorSubscribersReportGenerator$$EntryClass"
    TRIGGER: "--trigger-http"
    TIMEOUT: "540s"
    # for now, we cannot use `internal`, because request from a different function or run
    # is not actually `internal`, see: https://linear.app/herohero/issue/HH-2596/internal-and-cloud-load-balancing
    MEMORY: 8192Mi
    MAX_INSTANCES: 10
    # TODO limit service account Google sheets

Kotlin/Function/CreatorSubscribersReportGenerator/deploy-devel:
  needs:
    - Kotlin/Function/CreatorSubscribersReportGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/CreatorSubscribersReportGenerator/variables

Kotlin/Function/CreatorSubscribersReportGenerator/deploy-staging:
  needs:
    - Kotlin/Function/CreatorSubscribersReportGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/CreatorSubscribersReportGenerator/variables

Kotlin/Function/CreatorSubscribersReportGenerator/deploy-prod:
  needs:
    - Kotlin/Function/CreatorSubscribersReportGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/CreatorSubscribersReportGenerator/variables
