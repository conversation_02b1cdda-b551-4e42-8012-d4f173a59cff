Kotlin/Function/UnreadDirectMessageNotifier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/UnreadDirectMessageNotifier/variables:
  variables:
    FUNCTION_NAME: "unread-direct-message-notifier"
    CLASS_NAME: "hero.functions.UnreadDirectMessageNotifier"
    TOPIC: "Hourly"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/UnreadDirectMessageNotifier/deploy-devel:
  needs:
    - Kotlin/Function/UnreadDirectMessageNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/UnreadDirectMessageNotifier/variables

# We omit staging deployment to avoid clashing with production.
Kotlin/Function/UnreadDirectMessageNotifier/deploy-production:
  needs:
    - Kotlin/Function/UnreadDirectMessageNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/UnreadDirectMessageNotifier/variables
  variables:
    MEMORY: 4096MB
