package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.plusDays
import hero.baseutils.systemEnv
import hero.exceptions.http.NotFoundException
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.gcloud.isNotNull
import hero.gcloud.isNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.CancelledByRole
import hero.model.CouponMethod
import hero.model.Currency
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.model.topics.EmailPublished
import hero.model.topics.Hourly
import hero.model.topics.RefundMethod
import hero.model.topics.SubscriptionCancelRequest
import hero.stripe.model.StripeKeys
import hero.stripe.service.StripeClients
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider
import java.time.Instant

@Suppress("unused")
class GiftedSubscriptionExpirationNotifier(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    stripeKeysRepository: TypedCollectionReference<StripeKeys> =
        TypedCollectionReference<StripeKeys>(firestore.firestore["constants"]),
    keysEu: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-eu"].get(),
    keysUs: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-us"].get(),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val subscribersCollection: TypedCollectionReference<Subscriber> = firestore.typedCollectionOf(Subscriber),
    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs),
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    private val stripeService: StripeService = StripeService(
        clients = stripeClients,
        pubSub = pubSub,
    ),
    private val stripePaymentMethods: StripePaymentMethodsService = StripePaymentMethodsService(
        stripeClients,
        stripeService,
        pubSub,
    ),
    private val subscriptionService: StripeSubscriptionService = StripeSubscriptionService(
        clients = stripeClients,
        paymentMethodsService = stripePaymentMethods,
        production = production,
        countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping(),
    ),
    private val hostname: String = SystemEnv.hostname,
) : PubSubSubscriber<Hourly>() {
    override fun consume(payload: Hourly) {
        val subscribersToNotify = subscribersCollection
            .where(Subscriber::couponExpiresNotifiedAt).isNull()
            .and(Subscriber::couponExpiresAt).isNotNull()
            .and(Subscriber::couponExpiresAt).isLessThan(Instant.now().plusDays(NUM_OF_DAYS_BEFORE))
            .fetchAll()

        subscribersToNotify
            .map { subscriber ->
                val user = usersCollection[subscriber.userId].get()
                val creator = usersCollection[subscriber.creatorId].get()

                Triple(subscriber, user, creator)
            }
            .forEach { (subscriber, user, creator) -> processSubscription(subscriber, user, creator) }
    }

    private fun processSubscription(
        subscriber: Subscriber,
        user: User,
        creator: User,
    ) {
        try {
            log.info(
                "Notifying ${user.id} about gifted subscription ${subscriber.id} expiration",
                mapOf("userId" to user.id),
            )

            val updatedSubscriber = subscriber.copy(couponExpiresNotifiedAt = Instant.now())
            subscribersCollection[updatedSubscriber.id].set(updatedSubscriber)
            if (subscriber.couponMethod == CouponMethod.TRIAL) {
                log.info("Subscription ${subscriber.id} is a trial, skipping")
                return
            }

            sendEmail(user, creator, updatedSubscriber)
            val tier = Tier.ofId(subscriber.tierId)
            cancelSubscription(user, creator, tier.currency)
        } catch (e: Exception) {
            log.fatal(
                "Failed to notify user about gifted subscription expiration",
                mapOf("userId" to user.id),
                cause = e,
            )
        }
    }

    private fun sendEmail(
        user: User,
        creator: User,
        subscriber: Subscriber,
    ) {
        val email = user.email
        val tier = subscriber.tierId.let { Tier.ofId(it) }

        if (email != null) {
            pubSub.publish(
                EmailPublished(
                    to = email,
                    template = "gifted-subscription-expires",
                    variables = listOf(
                        "user-name" to user.name,
                        "creator-name" to creator.name,
                        "creator-link" to "$hostname/${creator.path}",
                        "expires-in" to NUM_OF_DAYS_BEFORE,
                        "currency" to tier.currency,
                        "subscription-cost" to tier.priceCents / 100,
                    ),
                    language = user.language,
                ),
            )
        }
    }

    private fun cancelSubscription(
        user: User,
        creator: User,
        currency: Currency,
    ) {
        val labels = mapOf("userId" to user.id, "creatorId" to creator.id)
        val stripeSubscription = subscriptionService
            .getSubscriptionsByCustomer(
                customerId = user.customerId(creator.creator) ?: error("${user.id} is not a Stripe customer"),
                metaCreatorId = creator.id,
                filterActive = true,
                currency = currency,
            )
            .firstOrNull()
            ?: throw NotFoundException(
                "No Stripe subscription was found for user ${user.id} and creator ${creator.id}.",
                labels,
            )

        pubSub.publish(
            SubscriptionCancelRequest(
                subscriptionId = stripeSubscription.id,
                cancelledBy = this::class.java.simpleName,
                atPeriodEnd = true,
                cancelledByRole = CancelledByRole.MODERATOR,
                refundMethod = RefundMethod.NONE,
                currency = currency,
            ),
        )
    }
}

private const val NUM_OF_DAYS_BEFORE = 7L
