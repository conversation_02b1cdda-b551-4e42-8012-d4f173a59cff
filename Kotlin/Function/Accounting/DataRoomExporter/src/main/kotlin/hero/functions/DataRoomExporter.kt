package hero.functions

import hero.baseutils.SystemEnv
import hero.gcloud.FirestoreRef
import hero.gcloud.SharedDriveIds
import hero.gcloud.SheetsCurrency
import hero.gcloud.SpreadsheetRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.createSpreadsheet
import hero.gcloud.firestore
import hero.gcloud.get
import hero.http4k.extensions.enum
import hero.http4k.extensions.get
import hero.http4k.extensions.year
import hero.http4k.serverless.CloudHttpFunction
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.Uri
import org.http4k.core.with
import org.http4k.lens.Header
import org.http4k.lens.Query
import org.http4k.serverless.GoogleCloudHttpFunction
import java.time.LocalDate

fun interface DataExporter : (SpreadsheetRef) -> Unit

@Suppress("Unused")
class DataRoomExporter(
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, false),
) : CloudHttpFunction() {
    @Suppress("Unused")
    class EntryClass : GoogleCloudHttpFunction(DataRoomExporter())

    private val routeRootGet: ContractRoute =
        "/".get(
            summary = "Generate data room sheet",
            tag = "DataRoomExporter",
            parameters = object {
                val financialsCurrency = Query.enum<SheetsCurrency>().optional("financialsCurrency")
                val financialsSince = Query.year().optional("financialsSince")
            },
            responses = listOf(Status.OK to Unit),
            handler = { _, _ ->
                val spreadsheet = exportDataRoom()
                val spreadsheetLink = "https://docs.google.com/spreadsheets/d/${spreadsheet.spreadsheetId}"
                Response(Status.FOUND)
                    .with(Header.LOCATION of Uri.of(spreadsheetLink))
            },
        )

    private fun exportDataRoom(): SpreadsheetRef {
        val constants = firestore.firestore["constants"]
        val stripeValues: StripeValues = TypedCollectionReference<StripeValues>(constants)["prod-stripe"].get()
        val spreadsheetRef = createSpreadsheet("Data room - ${LocalDate.now()}", SharedDriveIds.DATA_ROOM)

        listOf(
            StripeSubscriptionMetricsExporter,
            StripeCohortsExporter(stripeValues.hostSessionKey),
        ).forEach {
            it(spreadsheetRef)
        }

        return spreadsheetRef
    }

    override fun contractRoutes() = listOf(routeRootGet)
}

data class StripeValues(val hostSessionKey: String)
