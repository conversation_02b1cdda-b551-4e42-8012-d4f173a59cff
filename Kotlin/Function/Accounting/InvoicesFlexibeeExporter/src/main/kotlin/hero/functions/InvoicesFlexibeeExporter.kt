package hero.functions

import com.github.kittinunf.fuel.core.extensions.authentication
import com.github.kittinunf.fuel.httpGet
import com.github.kittinunf.fuel.httpPut
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.baseutils.instantOf
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.baseutils.systemEnv
import hero.baseutils.zoneEuPrg
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.jackson.toJson
import hero.model.Invoice
import hero.model.InvoiceItemType
import hero.model.User
import hero.model.UserCompany
import hero.model.VatPayer
import hero.model.euCountries
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant

/** https://podpora.flexibee.eu/cs/collections/2592813-dokumentace-rest-api */
@Suppress("Unused")
class InvoicesFlexibeeExporter(
    private val isProduction: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction),
    private val host: String = "https://herohero.flexibee.eu",
    private val companyId: String = "herohero",
    private val flexibeeUser: String = "herohero-api",
    private val flexibeePassword: String = systemEnv("FLEXIBEE_PASSWORD"),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val invoicesCollection: TypedCollectionReference<Invoice> = firestore.typedCollectionOf(Invoice),
) : FirestoreEventSubcriber() {
    override fun consume(event: FirestoreEvent) {
        exportInvoice(event.documentId)
    }

    internal fun exportInvoice(invoiceId: String) {
        val invoice = invoicesCollection[invoiceId].get()
        // we don't need to re-export already exported invoices
        if (invoice.exportedAt != null) {
            log.warn("Invoice ${invoice.id} was exported on ${invoice.exportedAt}, skipping.")
            return
        }

        if (invoice.euOneStopShop && invoice.timestamp < instantOf("2024-10-05T00:00:00Z")) {
            error("One stop shop should not be there before 5 Oct")
        }

        val user = usersCollection[invoice.userId].get()
        val adresar = translateCompany(user, invoice.invoicedCompany)
        val faktura = translateInvoice(invoice)

        if (!isProduction) {
            log.error(
                """
                Not writing non-production invoice ${invoice.invoiceId} to Flexibee accounting.
                - Adresar: $adresar
                - FakturaVydana: $invoice
                """.trimIndent(),
            )
            return
        }

        if (invoice.timestamp < instantOf("2023-10-14T00:00:00Z")) {
            log.error(
                """
                Not writing pre-2023/10 invoice ${invoice.invoiceId} as these were manually added and must not touched via API.
                - Adresar: $adresar
                - FakturaVydana: $invoice
                """.trimIndent(),
            )
            return
        }

        val flexiInvoiceId = writePayload(adresar, faktura)
        val firestoreInvoice = invoicesCollection[invoice.id]

        // for CZ invoices, we need to store conversion rates to display on Invoice
        if (invoice.eurConversionRateCents == null &&
            (invoice.invoicedCompany.country?.uppercase() ?: "CZ") == "CZ"
        ) {
            val response = "$host/c/$companyId/faktura-vydana/$flexiInvoiceId.json"
                .httpGet()
                .authentication().basic(flexibeeUser, flexibeePassword)
                .fetch<InvoiceData>()
            val exportedInvoice = response.winstrom.fakturaVydana!!.first()
            val conversionRate = exportedInvoice.kurz!!
                .toBigDecimal().movePointRight(2).toLong()
            log.info("Setting conversion rate $conversionRate for invoice ${invoice.id}/$flexiInvoiceId.")
            firestoreInvoice.field(Invoice::eurConversionRateCents).update(conversionRate)
        }
        firestoreInvoice.field(Invoice::exportedAt).update(Instant.now())
        firestoreInvoice.field(Invoice::exportedId).update(flexiInvoiceId)
    }

    private fun translateCompany(
        user: User,
        company: UserCompany,
    ): Adresar =
        Adresar(
            id = "code:" + user.id.take(20).uppercase(),
            nazev = company.name ?: "${company.firstName} ${company.lastName}",
            ulice = company.address.nullIfEmpty(),
            mesto = company.city.nullIfEmpty(),
            psc = company.postalCode.nullIfEmpty(),
            email = user.email,
            mobil = company.phone,
            platceDph = company.vatType == VatPayer.VAT_PAYER,
            stat = "code:" + (company.country ?: "CZ"),
            // in case the user fills in some wrong ID, flexibee accepts max 20 chars
            ic = company.id.nullIfEmpty()?.take(20),
            dic = company.vatId.nullIfEmpty(),
        )

    private fun translateInvoice(invoice: Invoice): FakturaVydanaData {
        // see: https://linear.app/herohero/issue/HH-1353/only-eu-countries-should-be-charged-with-vat
        val varData = when {
            // all countries out of EU and without vatId are without VAT
            invoice.countryOfDestination !in euCountries -> InvoiceVariableData(
                clenDph = "code:26",
                clenKonVykDph = "code:0.0.",
                popis = "mimo EU",
                typUcOp = "code:TRŽBY MIMO EU BEZ D",
                typSzbDphK = "typSzbDph.dphOsv",
            )
            invoice.euOneStopShop -> InvoiceVariableData(
                clenDph = "code:24",
                clenKonVykDph = "code:0.0.",
                popis = "OSS",
                typUcOp = "code:OSS",
                typSzbDphK = "typSzbDph.dphZakl",
            )
            // EU countries out of CZ with vatId are without VAT
            invoice.countryOfDestination != "CZ" &&
                invoice.invoicedCompany.vatId.nullIfEmpty() != null -> InvoiceVariableData(
                clenDph = "code:21",
                clenKonVykDph = "code:0.0.",
                popis = "EU",
                typUcOp = "code:TRŽBY EU",
                typSzbDphK = "typSzbDph.dphOsv",
            )
            // CZ or EU countries without vatId pay VAT
            else -> InvoiceVariableData(
                clenDph = "code:01-02",
                clenKonVykDph = "code:A.4-5.AUTO",
                popis = "EU",
                typUcOp = "code:TRŽBA SLUŽBY",
                typSzbDphK = "typSzbDph.dphZakl",
            )
        }

        val timestamp = invoice.timestamp.atZone(zoneEuPrg).toLocalDate()
        return FakturaVydanaData(
            id = "code:${invoice.invoiceId}",
            poznam = "https://svc-prod.herohero.co/invoice-generator/" +
                "?invoiceId=${invoice.invoiceId}&userId=${invoice.userId}&locale=cs&authToken=${invoice.authToken}",
            varSym = invoice.invoiceId.replace("-", ""),
            konSym = "code:0008",
            datVyst = timestamp.toString(),
            datUcto = timestamp.toString(),
            duzpPuv = timestamp.toString(),
            datUhr = timestamp.toString(),
            datSplat = timestamp.plusDays(7).toString(),
            bankovniUcet = "code:FIO EUR",
            typDoklBan = "code:FIO EUR",
            firma = "code:" + invoice.userId.take(20).uppercase(),
            typUcOp = varData.typUcOp,
            typDokl = "code:FAKTURA" + if (invoice.euOneStopShop) " OSS" else "",
            statDph = "code:" +
                if (invoice.euOneStopShop && invoice.countryOfDestination in euCountries)
                    invoice.countryOfDestination
                else
                    "CZ",
            popis = "Tržby z prodeje služeb – ${varData.popis}",
            mena = "code:${invoice.currencyInvoice.name}",
            stavUhrK = "stavUhr.uhrazenoRucne",
            clenKonVykDph = varData.clenKonVykDph,
            clenDph = varData.clenDph,
            polozkyFaktury = invoice.items
                .filter { item -> item.type == InvoiceItemType.FEE }
                .groupBy { it.vatCents }
                .map { (vatCents, items) ->
                    PolozkaFakturyData(
                        id = "ext:${invoice.invoiceId}_Fee_$vatCents",
                        nazev = "Sum of fees",
                        mnozMj = BigDecimal.ONE,
                        szbDph = vatCents.toBigDecimal(),
                        cenaMj = null,
                        sumZklMen = null,
                        sumDphMen = items.sumOf { it.lineVatTotal }.setScale(2, RoundingMode.HALF_UP),
                        sumCelkemMen = items.sumOf { it.lineTotal }.setScale(2, RoundingMode.HALF_UP),
                        mena = "code:${invoice.currencyInvoice.name}",
                        typSzbDphK = varData.typSzbDphK,
                        clenKonVykDph = varData.clenKonVykDph,
                        clenDph = varData.clenDph,
                    )
                },
        )
    }

    private fun writePayload(
        adresar: Adresar,
        faktura: FakturaVydanaData,
    ): String {
        val response = "$host/c/$companyId/faktura-vydana.json"
            .httpPut()
            .authentication().basic(flexibeeUser, flexibeePassword)
            .body(
                InvoiceData(
                    WinStromData(
                        adresar = listOf(adresar),
                        fakturaVydana = listOf(faktura),
                    ),
                ).toJson(),
            )
            .fetch<Any>()

        log.info("Invoice ${faktura.id} at ${faktura.poznam} was exported: $response")
        return faktura.id!!
    }
}

/** helper method for refreshing individual or batched invoices */
fun main() {
    val production = true
    val flexi = InvoicesFlexibeeExporter(isProduction = production)
    val fs = firestore(SystemEnv.cloudProject, production)
    val invoices = fs.typedCollectionOf(Invoice)

    val checkInvoicesAfter: String? = "2023-10-01T00:00:00Z"
    val invoiceIds: List<String>? = """
        202310-483758
        202411-997881
    """.trimMargin()
        .split("\\s+".toRegex())

    val query = when {
        invoiceIds != null -> invoices.where(Invoice::invoiceId).isIn(invoiceIds)
        checkInvoicesAfter != null -> invoices.where(Invoice::timestamp).isGreaterThan(instantOf(checkInvoicesAfter))
        else -> error("Either checkInvoicesAfter or invoiceId must be given.")
    }

    query
        .fetchAll()
        .forEach {
            try {
                flexi.exportInvoice(it.id)
            } catch (e: Exception) {
                log.info(e.message)
            }
        }
}
