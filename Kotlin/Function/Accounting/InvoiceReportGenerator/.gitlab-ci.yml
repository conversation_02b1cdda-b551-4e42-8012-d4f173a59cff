Kotlin/Function/Accounting/InvoiceReportGenerator/build:
  stage: build-services
  needs:
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Stripe/build
    - Kotlin/Modules/Http4k/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Accounting/InvoiceReportGenerator/variables:
  variables:
    FUNCTION_NAME: "invoice-report-generator"
    CLASS_NAME: "hero.functions.InvoiceReportGenerator$$EntryClass"
    TRIGGER: "--trigger-http"
    TIMEOUT: "1200s"
    # TODO limit service account Google sheets

Kotlin/Function/Accounting/InvoiceReportGenerator/deploy-devel:
  needs:
    - Kotlin/Function/Accounting/InvoiceReportGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Accounting/InvoiceReportGenerator/variables

Kotlin/Function/Accounting/InvoiceReportGenerator/deploy-staging:
  needs:
    - Kotlin/Function/Accounting/InvoiceReportGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Accounting/InvoiceReportGenerator/variables

Kotlin/Function/Accounting/InvoiceReportGenerator/deploy-prod:
  needs:
    - Kotlin/Function/Accounting/InvoiceReportGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Accounting/InvoiceReportGenerator/variables
