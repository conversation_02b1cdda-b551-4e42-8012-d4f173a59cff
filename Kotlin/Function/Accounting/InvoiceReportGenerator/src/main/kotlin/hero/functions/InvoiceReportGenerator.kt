package hero.functions

import com.stripe.model.BalanceTransaction
import com.stripe.model.Charge
import com.stripe.model.Refund
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.systemEnv
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.BatchUpdateSpreadsheet
import hero.gcloud.CellHorizontalAlignment.LEFT
import hero.gcloud.CellHorizontalAlignment.RIGHT
import hero.gcloud.Color.Companion.BRIGHT_RED
import hero.gcloud.Color.Companion.HERO_GRAY
import hero.gcloud.Color.Companion.HERO_PURPLE
import hero.gcloud.Color.Companion.LIGHT_GRAY
import hero.gcloud.FirestoreRef
import hero.gcloud.Format
import hero.gcloud.FormattedValue
import hero.gcloud.PERCENT_PATTERN
import hero.gcloud.PivotRowDescriptor
import hero.gcloud.PivotValueDescriptor
import hero.gcloud.SheetsCurrency
import hero.gcloud.SpreadsheetRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.addSheet
import hero.gcloud.appendFormatted
import hero.gcloud.batched
import hero.gcloud.countRange
import hero.gcloud.createPivotTable
import hero.gcloud.currency
import hero.gcloud.date
import hero.gcloud.emptyCell
import hero.gcloud.entry
import hero.gcloud.firestore
import hero.gcloud.formatRange
import hero.gcloud.formula
import hero.gcloud.get
import hero.gcloud.number
import hero.gcloud.renameSheet
import hero.gcloud.resizeColumns
import hero.gcloud.root
import hero.gcloud.string
import hero.gcloud.sumRange
import hero.gcloud.toHyperlink
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gcloud.writeHeader
import hero.gcloud.writeRangeFormatted
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.serverless.CloudHttpFunction
import hero.model.Creator
import hero.model.Currency
import hero.model.GenerateInvoiceReportRequest
import hero.model.Invoice
import hero.model.InvoiceItemType.TURNOVER
import hero.model.User
import hero.model.VatPayer
import hero.model.euCountries
import hero.stripe.model.PayoutChargeItem
import hero.stripe.model.PayoutItem
import hero.stripe.model.PayoutRefundItem
import hero.stripe.model.StripeKeys
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.VatMapping
import hero.stripe.service.VatMappingProvider
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.serverless.GoogleCloudHttpFunction
import java.math.BigDecimal
import java.time.Instant

@Suppress("Unused")
class InvoiceReportGenerator(
    private val hostname: String = SystemEnv.hostname,
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : CloudHttpFunction() {
    @Suppress("Unused")
    class EntryClass : GoogleCloudHttpFunction(InvoiceReportGenerator())

    private val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
    private val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    private val stripeService = StripeService(stripeClients, null)

    private val invoiceRepository = firestore.typedCollectionOf(Invoice)
    private val userRepository = firestore.typedCollectionOf(User)

    private val countryToVatMapping: VatMapping

    init {
        val vatMappingProvider = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD"))
        countryToVatMapping = vatMappingProvider.countryToVatMapping()
    }

    private val routeRootPost: ContractRoute =
        "/".post(
            summary = "Generate invoice report",
            tag = "InvoiceReportGenerator",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, _ ->
                val reportRequest = lens<GenerateInvoiceReportRequest>(request)
                try {
                    generate(
                        invoiceId = reportRequest.invoiceId,
                        currency = reportRequest.currency,
                        spreadsheetId = reportRequest.spreadsheetId,
                        authToken = reportRequest.authToken,
                    )
                } catch (e: Exception) {
                    handleFailedInvoiceReportGeneration(
                        invoiceId = reportRequest.invoiceId,
                        spreadsheetId = reportRequest.spreadsheetId,
                    )
                    throw IllegalStateException("Cannot generate report for $reportRequest: ${e.message}", e)
                }
                Response(Status.NO_CONTENT)
            },
        )

    internal fun generate(
        invoiceId: String,
        currency: Currency,
        spreadsheetId: String,
        authToken: String,
    ) {
        val invoice = invoiceRepository.where(Invoice::invoiceId).isEqualTo(invoiceId).fetchSingle()
            ?: throw NotFoundException("Failed to find invoice $invoiceId")

        if (authToken != invoice.authToken) {
            throw ForbiddenException()
        }

        val creator = userRepository[invoice.userId].get()
        val logProperties = mapOf("invoiceId" to invoiceId)
        log.info("Will generate report for invoice $invoiceId", logProperties)

        val payout = stripeService.getPayout(invoice.stripeAccountId, invoice.stripePayoutId, currency)

        if (!payout.automatic) {
            log.info("Payout for invoice $invoiceId was initiated manually, skipping.", logProperties)
            SpreadsheetRef(spreadsheetId)
                .batched()
                .writeRangeFormatted(
                    listOf(listOf(string("Invoice report is not available for payout initiated manually"))),
                    1,
                    1,
                )
                .execute()

            return
        }

        val payoutCharges = payoutItems(invoice.stripeAccountId, invoice.stripePayoutId, currency)
        val chargesCount = invoice.items.filter { it.type == TURNOVER && it.count > 0 }.sumOf { it.count }
        val refundsCount = invoice.items.filter { it.type == TURNOVER && it.count < 0 }.sumOf { it.count }
        val total = chargesCount + refundsCount
        val chunkSize = 300

        SpreadsheetRef(spreadsheetId)
            .apply {
                batched()
                    .renameSheet(0, "Transactions")
                    .writeHeader(formattedColumns)
                    .also {
                        formattedColumns.forEachIndexed { index, col ->
                            col.format.width?.let { width ->
                                it.resizeColumns(index + 1, 1, width)
                            }
                        }
                    }
                    .writeProgressBar(-1, chunkSize, total)
                    .execute()

                val lastRowIndex = payoutCharges
                    .chunked(chunkSize)
                    .onEachIndexed { chunkIndex, items ->
                        log.debug("Writing another $chunkSize rows", logProperties)
                        batched()
                            .appendFormatted(prepareSheetData(items, creator))
                            .writeProgressBar(chunkIndex, chunkSize, total)
                            .execute()
                    }
                    .map { it.size }
                    .sum() + 1

                batched()
                    .appendFormatted(prepareLastRow(lastRowIndex))
                    .removeProgressBar()
                    .execute()
                log.info("Done writing Transactions sheet", logProperties)

                val newSheetId = addSheet("Countries")
                batched()
                    .createPivotTable(
                        sourceSheetId = 0,
                        sourceRowStartEnd = 1 to lastRowIndex,
                        // columns are indexed from 1 (not from 0), 4: Amount, 5: Country
                        sourceColumnStartEnd = 4 to 5,
                        // source column is the second from the processed input: Country
                        rowDescriptors = listOf(PivotRowDescriptor(2)),
                        valueDescriptors = listOf(
                            PivotValueDescriptor("SUM", 1, "Turnover"),
                            PivotValueDescriptor("COUNT", 1, "Count"),
                        ),
                        targetSheetId = newSheetId,
                        targetRowColumn = 1 to 1,
                    )
                    .writeHeader(listOf(), newSheetId)
                    .formatRange(listOf((1..2).map { Format(horizontalAlignment = RIGHT) }), 1, 2, newSheetId)
                    .execute()
            }

        log.info("Done creating report for invoice $invoiceId", logProperties)
    }

    private fun handleFailedInvoiceReportGeneration(
        invoiceId: String,
        spreadsheetId: String,
    ) {
        SpreadsheetRef(spreadsheetId)
            .batched()
            // If we wanted to have rich text in the cell (although it's a bit hard to use)
            // see https://developers.google.com/sheets/api/reference/rest/v4/spreadsheets/cells#TextFormatRun
            .writeAlertMessage(
                """
                Invoice report generation failed, contact our <NAME_EMAIL>
                Invoice: $invoiceId, link: https://docs.google.com/spreadsheets/d/$spreadsheetId
                """.trimIndent(),
            )
            .execute()

        val invoice = invoiceRepository.where(Invoice::invoiceId).isEqualTo(invoiceId).fetchSingle()
        if (invoice == null) {
            log.error("Failed to find invoice with invoice id $invoiceId")
            return
        }
        invoiceRepository[invoice.id].field(Invoice::sheetReportLink).update(null)
    }

    private fun payoutItems(
        sourceAccountId: String,
        payoutId: String,
        currency: Currency,
    ): Sequence<PayoutItem> {
        val balanceTransactions = stripeService
            .listPayoutTransactions(sourceAccountId, payoutId, currency)
            .asSequence()
            .filter { it.reportingCategory !in setOf("payout", "payout_reversal") }

        return balanceTransactions
            .mapNotNull { mapTransactionToPayoutItem(it, payoutId, currency) }
    }

    private fun mapTransactionToPayoutItem(
        transaction: BalanceTransaction,
        payoutId: String,
        currency: Currency,
    ): PayoutItem? =
        when (val sourceObject = transaction.sourceObject) {
            is Charge -> {
                val sourceTransfer = sourceObject.sourceTransferObject
                val metadata = sourceTransfer.metadata
                val originalCharge: Charge = sourceTransfer.sourceTransactionObject
                    ?: sourceTransfer.transferGroup?.let { group ->
                        stripeService.getOriginalChargeByGroup(group, currency)
                    }
                    ?: error("Source transaction object was missing for ${sourceObject.sourceTransfer}: $metadata")

                PayoutChargeItem(originalCharge)
            }

            is Refund -> {
                val sourceTransfer = sourceObject
                    .chargeObject
                    .sourceTransferObject

                // sourceTransaction would be 4th level expansion, so it cannot be fetched during listPayoutTransactions
                val sourceTransaction = sourceTransfer.sourceTransaction?.let { stripeService.charge(it, currency) }

                val originalCharge: Charge = sourceTransaction
                    ?: sourceTransfer.transferGroup
                        ?.let { group -> stripeService.getOriginalChargeByGroup(group, currency) }
                    ?: error("Source transaction object was missing for $sourceTransfer: ${sourceTransfer.metadata}")

                PayoutRefundItem(sourceObject, originalCharge)
            }

            null -> {
                log.error("Transaction ${transaction.id} has null source object in payout $payoutId")
                null
            }

            else -> {
                error("The code should never reach this branch")
            }
        }

    private fun BatchUpdateSpreadsheet.writeProgressBar(
        chunkIndex: Int,
        chunkSize: Int,
        totalRows: Int,
    ): BatchUpdateSpreadsheet {
        val progressMessage = string(
            "Processed ${(chunkIndex + 1) * chunkSize} / $totalRows",
            Format(backgroundColor = LIGHT_GRAY),
        )
        return writeRangeFormatted(listOf(listOf(progressMessage)), 1, formattedColumns.size + 1)
    }

    private fun BatchUpdateSpreadsheet.writeAlertMessage(message: String): BatchUpdateSpreadsheet {
        val formattedMessage = string(message, Format(backgroundColor = BRIGHT_RED))

        return this
            .writeRangeFormatted(listOf(listOf(formattedMessage)), 1, formattedColumns.size + 1)
            .resizeColumns(formattedColumns.size + 1, 1, 1000)
    }

    private fun BatchUpdateSpreadsheet.removeProgressBar() =
        writeRangeFormatted(listOf(listOf(emptyCell(Format(backgroundColor = LIGHT_GRAY)))), 1, 7)

    private fun prepareLastRow(lastRowIndex: Int): List<List<FormattedValue>> =
        listOf(
            listOf(
                string("Total", Format(bold = true)),
                formula(countRange("B2", "B$lastRowIndex"), Format(bold = true, horizontalAlignment = LEFT)),
                formula(sumRange("C2", "C$lastRowIndex"), Format(bold = true)),
            ),
        )

    private fun prepareSheetData(
        payoutCharges: List<PayoutItem>,
        creator: User,
    ): List<List<FormattedValue>> {
        val customerIdsWithCurrency = payoutCharges
            // guests do not have customerId
            // for example https://dashboard.stripe.com/guests/gcus_1OKnZYB6ZCHekl2ReyzGgDQF in invoice 202312-826851
            .mapNotNull { if (it.charge.customer == null) null else it.charge.customer to it.charge.currency }
            .distinct()

        val usersByStripeId = customerIdsWithCurrency.associate { (customerId, currency) ->
            val user = userRepository
                .where(root(User::customerIds).entry(currency.uppercase())).isEqualTo(customerId)
                .fetchSingle()
                ?: deletedUser

            customerId to user
        }

        return payoutCharges.map {
            val user = usersByStripeId[it.charge.customer]

            when (it) {
                is PayoutChargeItem -> {
                    dataRow(
                        Instant.ofEpochSecond(it.charge.created),
                        it.charge.id,
                        user,
                        creator,
                        BigDecimal(it.charge.amount).movePointLeft(2).toDouble(),
                        it.charge,
                    )
                }

                is PayoutRefundItem -> {
                    dataRow(
                        Instant.ofEpochSecond(it.refund.created),
                        it.refund.id,
                        user,
                        creator,
                        BigDecimal(it.charge.amount).movePointLeft(2).negate().toDouble(),
                        it.charge,
                    )
                }
            }
        }
    }

    private fun dataRow(
        createdAt: Instant,
        transactionId: String,
        user: User?,
        creator: User,
        amount: Double,
        charge: Charge,
    ): List<FormattedValue> =
        listOf(
            date(
                createdAt,
                Format(horizontalAlignment = LEFT),
            ),
            formula(
                user?.let {
                    toHyperlink(
                        "$hostname/${user.id}",
                        user.name,
                    )
                },
                Format(horizontalAlignment = LEFT, textColor = HERO_PURPLE),
            ),
            formula(
                user?.let {
                    toHyperlink(
                        "$hostname/receipt?chargeId=$transactionId&userId=${user.id}",
                        transactionId.takeLast(8),
                    )
                },
                Format(horizontalAlignment = RIGHT, textColor = HERO_PURPLE),
            ),
            currency(
                amount,
                getSheetsCurrency(charge),
                Format(horizontalAlignment = RIGHT),
            ),
            string(
                charge.paymentMethodDetails.card.country,
                Format(horizontalAlignment = RIGHT, textColor = HERO_GRAY),
            ).apply {
                if (charge.paymentMethodDetails.card.country.uppercase() != (creator.company?.country ?: "CZ")) {
                    note = "'One stop shop' can be enabled in your Herohero profile or <NAME_EMAIL>."
                }
            },
            number(
                getVat(charge, creator, Instant.ofEpochSecond(charge.created)),
                Format(horizontalAlignment = RIGHT),
                PERCENT_PATTERN,
            ),
            string(
                charge.description
                    // <del>Payment for jankachajervspzn's </del>coupon CQUGGVEOYS
                    ?.replace(".*coupon".toRegex(), "Coupon"),
            ),
        )

    private fun getVat(
        charge: Charge,
        creator: User,
        timestamp: Instant,
    ): BigDecimal {
        if (creator.company?.vatType != VatPayer.VAT_PAYER) {
            return BigDecimal.ZERO
        }
        // https://linear.app/herohero/issue/HH-3635/oss-for-creators
        val country = if (creator.oneStopShopAt != null && creator.oneStopShopAt!! < timestamp) {
            charge.paymentMethodDetails.card.country.uppercase()
        } else {
            creator.company?.country ?: "CZ"
        }

        return if (country in euCountries) {
            countryToVatMapping[country, Instant.ofEpochSecond(charge.created)].toBigDecimal().movePointLeft(2)
        } else {
            BigDecimal.ZERO
        }
    }

    private fun getSheetsCurrency(charge: Charge): SheetsCurrency =
        when (charge.currency) {
            "eur" -> SheetsCurrency.EUR
            "czk" -> SheetsCurrency.CZK
            "usd" -> SheetsCurrency.USD
            else -> error("Unknown currency ${charge.currency}")
        }

    private val formattedColumns = listOf(
        // WARN when touching the column order, you must check the order of the Country Pivot table
        string("Date", Format(horizontalAlignment = LEFT, width = 150)),
        string("Subscriber", Format(horizontalAlignment = LEFT, width = 150)),
        string("Receipt", Format(horizontalAlignment = RIGHT, width = 100)),
        // WARN the following two columns are used for the Pivot table
        string("Amount", Format(horizontalAlignment = RIGHT, width = 100)),
        string("Country", Format(horizontalAlignment = RIGHT, width = 100)),
        string("VAT %", Format(horizontalAlignment = RIGHT, width = 100)),
        string("Description", Format(horizontalAlignment = LEFT, width = 250)),
    )

    override fun contractRoutes() = listOf(routeRootPost)
}

private val deletedUser = User(id = "deleted", path = "", creator = Creator(tierId = "EUR05"), name = "Deleted user")

// use with caution for debugging
fun main() {
    InvoiceReportGenerator(
        hostname = "https://herohero.co",
        production = true,
    ).generate(
        invoiceId = "202412-810288",
        currency = Currency.EUR,
        spreadsheetId = "1r4IcTDzaQjV65OajMHzrSphn2h0iOAwFpGDzhk52YQk",
        authToken = "unyvfnuauhlafg",
    )
}
