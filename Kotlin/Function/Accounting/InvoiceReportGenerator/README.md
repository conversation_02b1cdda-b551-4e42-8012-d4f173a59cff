### InvoiceReportGenerator

To run locally, you need to export service account credentials for `<EMAIL>`
and put link to it in env vars as below:

```bash
GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/.config/gcloud/service-account.json \
SERVICE_NAME=invoice-report-generator \
HOSTNAME=local.herohero.co \
CLOUD_PROJECT=heroheroco \
ENVIRONMENT=devel \
PRODUCTION=false \
SERVICE_TYPE=cloud_function \
CLOUD_REGION=europe \
gradle runFunction -Prun.functionTarget=hero.functions.InvoiceReportGenerator
```

Usage:
```bash
curl "localhost:8080/202305-192702?refresh=false" -H 'Cookie: accessToken=xxxxtoken' -v
```

Set the `refresh` param to true, to force regeneration of the report.
