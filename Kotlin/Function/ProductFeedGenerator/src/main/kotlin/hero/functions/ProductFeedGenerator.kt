package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.escapeUrl
import hero.gcloud.firestore
import hero.gcloud.isTrue
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.http4k.extensions.get
import hero.http4k.serverless.CloudHttpFunction
import hero.model.Creator
import hero.model.Tier
import hero.model.User
import hero.model.UserStatus
import org.apache.commons.text.StringEscapeUtils.escapeXml10
import org.http4k.contract.ContractRoute
import org.http4k.core.ContentType
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.contentType
import org.http4k.serverless.GoogleCloudHttpFunction
import java.time.Instant

class ProductFeedGenerator(
    production: Boolean = SystemEnv.isProduction,
) : CloudHttpFunction() {
    @Suppress("Unused")
    class EntryClass : GoogleCloudHttpFunction(ProductFeedGenerator())

    private val firestore = firestore(SystemEnv.cloudProject, production)
    private val usersCollection = firestore.typedCollectionOf(User)

    private val routeRootGet: ContractRoute =
        "/".get(
            summary = "Generate product feed",
            tag = "ProductFeedGenerator",
            parameters = object {
                val featuredOnly = Query.boolean().optional("featuredOnly")
            },
            responses = listOf(Status.OK to Unit),
            handler = { request, parameters ->
                val featuredOnly = parameters.featuredOnly(request) == true
                Response(Status.OK)
                    .contentType(ContentType.TEXT_XML)
                    .body(generateXml(featuredOnly))
            },
        )

    private fun generateXml(featuredOnly: Boolean): String {
        val timestamp = Instant.now().epochSecond
        return buildString {
            append(
                """
                <?xml version="1.0" encoding="utf-8"?>
                <rss version="2.0" xmlns:g="http://base.google.com/ns/1.0">
                <channel>
                    <title>HeroHero creator's feed</title>
                    <description>List of creators publishing on Herohero.co.</description>
                <link>https://herohero.co</link>
                """.trimIndent(),
            )

            usersCollection
                .where(User::status).isEqualTo(UserStatus.ACTIVE)
                .and(root(User::creator).path(Creator::stripeAccountActive)).isTrue()
                .let { if (featuredOnly) it.and(User::featured).isTrue() else it }
                .fetchAll()
                .asSequence()
                .filter { !it.explicit }
                .filter { it.bio.trim().isNotEmpty() }
                .filter { !it.image?.id.isNullOrEmpty() }
                .forEach { user ->
                    val tier = Tier.ofId(user.creator.tierId)
                    append(
                        """
                        <item>
                            <g:id>${user.id}</g:id>
                            <title>${escapeXml10(user.name)}</title>
                            <description>${escapeXml10(user.bio)}</description>
                            <link>https://herohero.co/${user.path.escapeUrl()}</link>
                            <g:price>${tier.priceCents.div(100)} ${tier.currency}</g:price>
                            <g:condition>new</g:condition>
                            <g:availability>in stock</g:availability>
                            <g:identifier_exists>false</g:identifier_exists>
                            <g:image_link>https://storage.googleapis.com/heroheroco-ogimages/${user.id}.png?$timestamp</g:image_link>
                            <g:google_product_category>5710</g:google_product_category>
                        </item>
                        """.trimIndent(),
                    )
                }

            append(
                """
                    </channel>
                </rss>
                """.trimIndent(),
            )
        }
    }

    override fun contractRoutes() = listOf(routeRootGet)
}
