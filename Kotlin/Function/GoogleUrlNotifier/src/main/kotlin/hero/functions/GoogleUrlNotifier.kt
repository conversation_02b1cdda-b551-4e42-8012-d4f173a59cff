package hero.functions

import com.github.kittinunf.fuel.httpPost
import com.google.auth.oauth2.GoogleCredentials
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.jackson.toJson
import hero.model.UserStateChange
import hero.model.UserStateChanged

// https://stackoverflow.com/questions/60553749/google-indexing-api-permission-denied-failed-to-verify-the-url-ownership
@Suppress("Unused")
class GoogleUrlNotifier : PubSubSubscriber<UserStateChanged>() {
    private val scopes = "https://www.googleapis.com/auth/indexing"
    private val production = SystemEnv.isProduction
    private val credentials = GoogleCredentials
        .getApplicationDefault()
        .createScoped(scopes)

    override fun consume(payload: UserStateChanged) {
        val user = payload.user
        if (payload.stateChange != UserStateChange.CREATED) {
            log.debug("State change is not CREATED, skipping: ${payload.stateChange}.", mapOf("userId" to user.id))
            return
        }

        val content = mapOf("url" to "https://herohero.co/${user.path}", "type" to "URL_UPDATED")
        if (!production) {
            log.debug("Skipping notifying google in devel environment: ${content["url"]}.", mapOf("userId" to user.id))
            return
        }

        credentials.refreshIfExpired()

        val request = "https://indexing.googleapis.com/v3/urlNotifications:publish"
            .httpPost()
            .set("Content-Type", "application/json")
            // auth header-keys are case-sensitive
            .set("Authorization", "Bearer ${credentials.accessToken.tokenValue}")
            .body(content.toJson())

        val response = request.response()
        val reformattedResponse = String(response.second.data).replace("\\s+".toRegex(), " ")
        log.info("Notified google for: ${content["url"]}: $reformattedResponse", mapOf("userId" to user.id))
    }
}
