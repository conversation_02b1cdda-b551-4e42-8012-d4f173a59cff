package hero.functions

import hero.baseutils.minus
import hero.baseutils.plus
import hero.model.CancelledByRole
import hero.model.Notification
import hero.model.NotificationType
import hero.model.StorageEntityType
import hero.model.SubscriberStatus.CANCELLED
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChange.SUBSCRIBED
import hero.model.topics.SubscriberStatusChange.UNSUBSCRIBED
import hero.model.topics.SubscriberStatusChanged
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

/**
 * TODO add tests for email sending
 */
class SubscriptionNotifierIT : IntegrationTest() {
    @Test
    fun `should create NEW_SUBSCRIPTION notification for creator when user subscribes a creator`() {
        val now = Instant.ofEpochSecond(1741610376)
        val underTest = SubscriptionNotifier(
            systemEnvs = TestEnvironmentVariables,
            lazyContext = lazyTestContext,
            subscribersCollection = TestCollections.subscribersCollection,
            notificationRepository = TestRepositories.notificationRepository,
            userRepository = TestRepositories.userRepository,
            logger = TestLogger,
            clock = TestClock(now),
            pubSub = pubSubMock,
        )

        val creator = testHelper.createUser("cestmir")
        val subscriber = testHelper.createUser("franta")
        testHelper.createSubscriber("cestmir", "franta", subscribedAt = now)

        underTest.consume(subscriberStatusChanged(subscriber.id, creator.id, SUBSCRIBED))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "cestmir",
                type = NotificationType.NEW_SUBSCRIPTION,
                actorIds = listOf("franta"),
                objectId = "franta",
                objectType = StorageEntityType.USER,
                created = now,
                timestamp = now,
                seenAt = null,
                checkedAt = null,
                id = "cestmir-1741610376",
            ),
        )
    }

    @Test
    fun `should group NEW_SUBSCRIPTION notifications when multiple users subscribe creator on the same day`() {
        val now = Instant.ofEpochSecond(1741610376)
        val testClock = TestClock(now)
        val underTest = SubscriptionNotifier(
            systemEnvs = TestEnvironmentVariables,
            lazyContext = lazyTestContext,
            subscribersCollection = TestCollections.subscribersCollection,
            notificationRepository = TestRepositories.notificationRepository,
            userRepository = TestRepositories.userRepository,
            logger = TestLogger,
            clock = testClock,
            pubSub = pubSubMock,
        )

        val creator = testHelper.createUser("cestmir")
        val subscriber1 = testHelper.createUser("franta")

        testHelper.createSubscriber("cestmir", "franta", subscribedAt = now)

        val subscriber2 = testHelper.createUser("pepa")
        testHelper.createSubscriber("cestmir", "pepa", subscribedAt = now + 5.seconds)

        val subscriber3 = testHelper.createUser("filip")
        testHelper.createSubscriber("cestmir", "filip", subscribedAt = now - 5.seconds, status = CANCELLED)

        testHelper.createUser("erem")
        // this notification should not be modified when we group notification
        val anotherNotification = testHelper.createNotification(
            "erem",
            type = NotificationType.NEW_SUBSCRIPTION,
            createdAt = now,
        )

        underTest.consume(subscriberStatusChanged(subscriber1.id, creator.id, SUBSCRIBED))

        val notifications1 = TestRepositories.notificationRepository.find { this }
        assertThat(notifications1)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("id")
            .containsExactlyInAnyOrder(
                Notification(
                    userId = "cestmir",
                    type = NotificationType.NEW_SUBSCRIPTION,
                    actorIds = listOf("franta"),
                    objectId = "franta",
                    objectType = StorageEntityType.USER,
                    created = now,
                    timestamp = now,
                    seenAt = null,
                    checkedAt = null,
                    id = "cestmir-1741610376",
                ),
                anotherNotification,
            )

        testClock += 5.seconds

        underTest.consume(subscriberStatusChanged(subscriber2.id, creator.id, SUBSCRIBED))

        val notifications2 = TestRepositories.notificationRepository.find { this }
        assertThat(notifications2)
            .containsExactlyInAnyOrder(
                Notification(
                    userId = "cestmir",
                    type = NotificationType.NEW_SUBSCRIPTION,
                    actorIds = listOf("franta", "pepa"),
                    objectId = "franta",
                    objectType = StorageEntityType.USER,
                    created = now.plusSeconds(5),
                    timestamp = now.plusSeconds(5),
                    seenAt = null,
                    checkedAt = null,
                    id = notifications1.first { it.userId == "cestmir" }.id,
                ),
                anotherNotification,
            )

        testClock += 5.seconds

        // this user unsubscribes, should create new notification and not group with the existing
        underTest.consume(subscriberStatusChanged(subscriber3.id, creator.id, UNSUBSCRIBED))

        val notifications3 = TestRepositories.notificationRepository.find { this }
        assertThat(notifications3).hasSize(3)
        assertThat(notifications3).contains(*notifications2.toTypedArray())
        assertThat(notifications3)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("id")
            .contains(
                Notification(
                    userId = "filip",
                    type = NotificationType.CANCELLED_SUBSCRIPTION_OTHER,
                    actorIds = listOf("cestmir"),
                    objectId = "cestmir",
                    objectType = StorageEntityType.USER,
                    created = now.plusSeconds(10),
                    timestamp = now.plusSeconds(10),
                    seenAt = null,
                    checkedAt = null,
                    id = "filip-1741610386",
                ),
            )
    }

    @Test
    fun `should create CANCELLED_SUBSCRIPTION_ENDED notification for user when sub ends and user cancelled it`() {
        val underTest = SubscriptionNotifier(
            systemEnvs = TestEnvironmentVariables,
            lazyContext = lazyTestContext,
            subscribersCollection = TestCollections.subscribersCollection,
            notificationRepository = TestRepositories.notificationRepository,
            userRepository = TestRepositories.userRepository,
            logger = TestLogger,
            pubSub = pubSubMock,
        )

        val creator = testHelper.createUser("cestmir")
        val subscriber = testHelper.createUser("franta")
        val cancelAt = Instant.ofEpochSecond(1741610444)
        testHelper.createSubscriber(
            "cestmir",
            "franta",
            cancelAt = cancelAt,
            status = CANCELLED,
            cancelAtPeriodEnd = true,
        )

        underTest.consume(subscriberStatusChanged(subscriber.id, creator.id, UNSUBSCRIBED))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "franta",
                type = NotificationType.CANCELLED_SUBSCRIPTION_ENDED,
                actorIds = listOf("cestmir"),
                objectId = "cestmir",
                objectType = StorageEntityType.USER,
                created = cancelAt,
                timestamp = cancelAt,
                seenAt = null,
                checkedAt = null,
                id = "franta-1741610444",
            ),
        )
    }

    @Test
    fun `should create CANCELLED_SUBSCRIPTION_REFUSED notification for user when sub was refused`() {
        val underTest = SubscriptionNotifier(
            systemEnvs = TestEnvironmentVariables,
            lazyContext = lazyTestContext,
            subscribersCollection = TestCollections.subscribersCollection,
            notificationRepository = TestRepositories.notificationRepository,
            userRepository = TestRepositories.userRepository,
            logger = TestLogger,
            pubSub = pubSubMock,
        )

        val creator = testHelper.createUser("cestmir")
        val subscriber = testHelper.createUser("franta")
        val cancelAt = Instant.ofEpochSecond(1741610444)
        testHelper.createSubscriber(
            "cestmir",
            "franta",
            cancelAt = cancelAt,
            status = CANCELLED,
            refused = true,
        )

        underTest.consume(subscriberStatusChanged(subscriber.id, creator.id, UNSUBSCRIBED))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "franta",
                type = NotificationType.CANCELLED_SUBSCRIPTION_REFUSED,
                actorIds = listOf("cestmir"),
                objectId = "cestmir",
                objectType = StorageEntityType.USER,
                created = cancelAt,
                timestamp = cancelAt,
                seenAt = null,
                checkedAt = null,
                id = "franta-1741610444",
            ),
        )
    }

    @Test
    fun `should create CANCELLED_SUBSCRIPTION_REFUNDED notification for user when sub was refunded`() {
        val underTest = SubscriptionNotifier(
            systemEnvs = TestEnvironmentVariables,
            lazyContext = lazyTestContext,
            subscribersCollection = TestCollections.subscribersCollection,
            notificationRepository = TestRepositories.notificationRepository,
            userRepository = TestRepositories.userRepository,
            logger = TestLogger,
            pubSub = pubSubMock,
        )

        val creator = testHelper.createUser("cestmir")
        val subscriber = testHelper.createUser("franta")
        val cancelAt = Instant.ofEpochSecond(1741610444)
        testHelper.createSubscriber(
            "cestmir",
            "franta",
            cancelAt = cancelAt,
            status = CANCELLED,
            refunded = true,
        )

        underTest.consume(subscriberStatusChanged(subscriber.id, creator.id, UNSUBSCRIBED))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "franta",
                type = NotificationType.CANCELLED_SUBSCRIPTION_REFUNDED,
                actorIds = listOf("cestmir"),
                objectId = "cestmir",
                objectType = StorageEntityType.USER,
                created = cancelAt,
                timestamp = cancelAt,
                seenAt = null,
                checkedAt = null,
                id = "franta-1741610444",
            ),
        )
    }

    @Test
    fun `should create CANCELLED_SUBSCRIPTION_BY_CREATOR notification for user when sub was cancelled by creator`() {
        val underTest = SubscriptionNotifier(
            systemEnvs = TestEnvironmentVariables,
            lazyContext = lazyTestContext,
            subscribersCollection = TestCollections.subscribersCollection,
            notificationRepository = TestRepositories.notificationRepository,
            userRepository = TestRepositories.userRepository,
            logger = TestLogger,
            pubSub = pubSubMock,
        )

        val creator = testHelper.createUser("cestmir")
        val subscriber = testHelper.createUser("franta")
        val cancelAt = Instant.ofEpochSecond(1741610444)
        testHelper.createSubscriber(
            "cestmir",
            "franta",
            cancelAt = cancelAt,
            status = CANCELLED,
            cancelledByRole = CancelledByRole.CREATOR,
        )

        underTest.consume(subscriberStatusChanged(subscriber.id, creator.id, UNSUBSCRIBED))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "franta",
                type = NotificationType.CANCELLED_SUBSCRIPTION_BY_CREATOR,
                actorIds = listOf("cestmir"),
                objectId = "cestmir",
                objectType = StorageEntityType.USER,
                created = cancelAt,
                timestamp = cancelAt,
                seenAt = null,
                checkedAt = null,
                id = "franta-1741610444",
            ),
        )
    }

    private fun subscriberStatusChanged(
        userId: String,
        creatorId: String,
        change: SubscriberStatusChange,
    ) = SubscriberStatusChanged(
        userId = userId,
        creatorId = creatorId,
        statusChange = change,
        refused = false,
        cancelledReason = null,
        refunded = false,
        ended = false,
        doNotNotify = false,
    )
}
