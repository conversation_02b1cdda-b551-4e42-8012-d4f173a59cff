package hero.functions

import hero.baseutils.SystemEnv
import hero.gcloud.PubSub
import hero.model.SlackBlock
import hero.model.SlackBlockText
import hero.model.SlackMessage
import hero.model.topics.Daily
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.UPLOAD_TRACKING
import hero.sql.jooq.tables.records.UploadTrackingRecord
import org.jooq.DSLContext
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

@Suppress("Unused")
class UploadTrackingAlerter(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
) : PubSubSubscriber<Daily>() {
    private val context by lazyContext

    override fun consume(payload: Daily) {
        val yesterday = LocalDate.now().minusDays(1)
        val startOfYesterday = yesterday.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
        val endOfYesterday = yesterday.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant()
        val uploads = context
            .selectFrom(UPLOAD_TRACKING)
            .where(UPLOAD_TRACKING.CREATED_AT.ge(startOfYesterday))
            .and(UPLOAD_TRACKING.CREATED_AT.lt(endOfYesterday))
            .fetch()

        val groupedUploads = uploads
            .groupBy {
                when {
                    it.isAbortedUpload -> {
                        ABORTED_KEY
                    }

                    it.isCompleteUpload -> {
                        COMPLETE_KEY
                    }

                    else -> {
                        INCOMPLETE_KEY
                    }
                }
            }

        val blocks = groupedUploads
            .getOrDefault(INCOMPLETE_KEY, emptyList())
            .chunked(30)
            .map { chunk ->
                SlackBlock(
                    text = SlackBlockText(
                        chunk.joinToString(separator = "\n", prefix = "```", postfix = "```") {
                            "Request key: ${it.requestKey}, userId: ${it.userId}"
                        },
                    ),
                )
            }

        val incompleteUploadsSize = groupedUploads.getOrDefault(INCOMPLETE_KEY, emptyList()).size
        val completeUploadsSize = groupedUploads.getOrDefault(COMPLETE_KEY, emptyList()).size
        val abortedUploadsSize = groupedUploads.getOrDefault(ABORTED_KEY, emptyList()).size

        val message = SlackMessage(
            channel = "alerts_uploads",
            blocks = listOf(
                SlackBlock(
                    text = SlackBlockText(
                        "Date: ${LocalDate.now()}, total uploads: ${uploads.size}," +
                            " *incomplete: $incompleteUploadsSize*," +
                            " complete: $completeUploadsSize, aborted: $abortedUploadsSize",
                    ),
                ),
            ) + blocks,
        )

        pubSub.publish(message)
    }
}

private const val INCOMPLETE_KEY = "INCOMPLETE_KEY"
private const val COMPLETE_KEY = "COMPLETE_KEY"
private const val ABORTED_KEY = "ABORTED_KEY"

private val UploadTrackingRecord.isSinglePart
    get() = this.contentLength == 0L

private val UploadTrackingRecord.isCompleteUpload
    get() = if (this.isSinglePart) {
        this.encodingCompletedAt != null
    } else {
        this.completedAt != null
    }

private val UploadTrackingRecord.isAbortedUpload
    get() = this.abortedAt != null && this.completedAt == null
