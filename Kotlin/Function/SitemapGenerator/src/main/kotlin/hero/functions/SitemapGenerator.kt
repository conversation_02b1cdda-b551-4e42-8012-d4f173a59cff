package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.escapeUrl
import hero.gcloud.firestore
import hero.gcloud.isTrue
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.http4k.extensions.get
import hero.http4k.serverless.CloudHttpFunction
import hero.model.Creator
import hero.model.User
import org.http4k.contract.ContractRoute
import org.http4k.core.ContentType
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.contentType
import org.http4k.serverless.GoogleCloudHttpFunction

@Suppress("Unused")
class SitemapGenerator : CloudHttpFunction() {
    @Suppress("Unused")
    class EntryClass : GoogleCloudHttpFunction(SitemapGenerator())

    private val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    private val usersCollection = firestore.typedCollectionOf(User)

    private val routeRootGet: ContractRoute =
        "/".get(
            summary = "Generate sitemap",
            tag = "SitemapGenerator",
            parameters = object {},
            responses = listOf(Status.OK to Unit),
            handler = { _, _ ->
                Response(Status.OK)
                    .contentType(ContentType.TEXT_XML)
                    .body(generateXml())
            },
        )

    private fun generateXml(): String =
        buildString {
            append(
                """
            <?xml version="1.0" encoding="UTF-8"?>
            <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
                """.trimIndent(),
            )

            usersCollection
                .where(root(User::creator).path(Creator::active)).isTrue()
                .fetchAll()
                .asSequence()
                .forEach {
                    append("<url><loc>https://herohero.co/${it.path.escapeUrl()}</loc></url>\n")
                }

            append(
                """
                </urlset>
                """.trimIndent(),
            )
        }

    override fun contractRoutes() = listOf(routeRootGet)
}
