Kotlin/Function/SitemapGenerator/build:
  stage: build-services
  needs:
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Http4k/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/SitemapGenerator/variables:
  variables:
    FUNCTION_NAME: "sitemap-generator"
    CLASS_NAME: "hero.functions.SitemapGenerator$$EntryClass"
    TRIGGER: "--trigger-http"
    MAX_INSTANCES: 32
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/SitemapGenerator/deploy-devel:
  needs:
    - Kotlin/Function/SitemapGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/SitemapGenerator/variables

Kotlin/Function/SitemapGenerator/deploy-staging:
  needs:
    - Kotlin/Function/SitemapGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/SitemapGenerator/variables

Kotlin/Function/SitemapGenerator/deploy-prod:
  needs:
    - Kotlin/Function/SitemapGenerator/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/SitemapGenerator/variables
