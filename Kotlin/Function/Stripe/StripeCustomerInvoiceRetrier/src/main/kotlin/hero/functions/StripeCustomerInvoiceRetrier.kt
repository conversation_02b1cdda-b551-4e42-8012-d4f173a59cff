package hero.functions

import com.stripe.exception.StripeException
import com.stripe.param.InvoiceSearchParams
import com.stripe.param.InvoiceUpdateParams
import com.stripe.param.PaymentMethodListParams
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.model.topics.CustomerPaymentMethodsUpdated
import hero.stripe.model.StripeKeys
import hero.stripe.service.StripeClients
import hero.stripe.service.stripeRetry

@Suppress("unused")
class StripeCustomerInvoiceRetrier(
    production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
) : PubSubSubscriber<CustomerPaymentMethodsUpdated>() {
    private val stripeKeysRepository: TypedCollectionReference<StripeKeys> =
        TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
    private val keysEu: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-eu"].get()
    private val keysUs: StripeKeys = stripeKeysRepository["${production.envPrefix}-stripe-us"].get()
    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)

    override fun consume(payload: CustomerPaymentMethodsUpdated) {
        val client = stripeClients[payload.currency]

        // in case of a new payment method, we try to charge the new one,
        // when payment method is deleted, we try to charge all available ones
        val paymentMethodIds = if (payload.paymentMethodId != null)
            listOf(payload.paymentMethodId)
        else
            client.paymentMethods()
                .list(
                    PaymentMethodListParams.builder().setCustomer(payload.customerId).build(),
                )
                .data
                .map { it.id }

        val invoicesOpen = client
            .invoices()
            .search(
                InvoiceSearchParams.builder()
                    .addExpand("data.subscription")
                    .setQuery("status:'open' AND customer:'${payload.customerId}'")
                    .build(),
            )
            .autoPagingIterable()
            .toList()

        val invoicesDraft = client
            .invoices()
            .search(
                InvoiceSearchParams.builder()
                    .addExpand("data.subscription")
                    .setQuery("status:'draft' AND customer:'${payload.customerId}'")
                    .build(),
            )
            .autoPagingIterable()
            .toList()

        val invoices = invoicesDraft + invoicesOpen

        if (invoices.isNotEmpty() && paymentMethodIds.isEmpty()) {
            log.info("No payment methods of ${payload.customerId} to try to pay ${invoices.map { it.id }}")
            return
        }

        invoices
            .filter { it.subscriptionObject.status == "past_due" }
            .forEach { invoice ->
                paymentMethodIds.forEach {
                    try {
                        log.info("Retrying payment for invoice ${invoice.id} with $it for ${payload.customerId}")
                        stripeRetry {
                            invoice
                                .update(InvoiceUpdateParams.builder().setDefaultPaymentMethod(it).build())
                                .pay()
                        }
                        // payment went through, no need to try other payment methods
                        return
                    } catch (e: StripeException) {
                        // payment method not accepted, will try next one
                        log.warn("Payment for ${invoice.id} with $it was denied: ${e.message}")
                    }
                }
            }
    }
}
