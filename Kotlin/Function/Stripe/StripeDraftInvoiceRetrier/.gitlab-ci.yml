Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/Stripe/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/variables:
  variables:
    FUNCTION_NAME: "stripe-draft-invoice-retrier"
    CLASS_NAME: "hero.functions.StripeDraftInvoiceRetrier"
    TOPIC: "Daily"
    TIMEOUT: "540s"
    MEMORY: "1024Mi"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/deploy-devel:
  needs:
    - Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/variables

Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/deploy-prod:
  needs:
    - Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Stripe/StripeDraftInvoiceRetrier/variables
  variables:
    TIMEOUT: "540s"
