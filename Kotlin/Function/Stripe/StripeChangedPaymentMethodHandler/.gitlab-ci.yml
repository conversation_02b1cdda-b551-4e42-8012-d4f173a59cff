Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/Stripe/build
    - Kotlin/Modules/SQL/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/variables:
  variables:
    FUNCTION_NAME: "stripe-changed-payment-method-handler"
    CLASS_NAME: "hero.functions.StripeChangedPaymentMethodHandler"
    TOPIC: "PaymentMethodChanged"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/deploy-devel:
  needs:
    - Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/variables

Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/deploy-staging:
  needs:
    - Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/variables

Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/deploy-prod:
  needs:
    - Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Stripe/StripeChangedPaymentMethodHandler/variables
  variables:
    TIMEOUT: "300s"
