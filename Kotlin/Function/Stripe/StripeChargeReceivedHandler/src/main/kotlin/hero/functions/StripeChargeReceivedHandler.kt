package hero.functions

import com.stripe.model.Charge
import com.stripe.param.ChargeRetrieveParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.jackson.parseEnum
import hero.model.Currency
import hero.model.MessageThread
import hero.model.Notification
import hero.model.NotificationType
import hero.model.PaymentType
import hero.model.PostPayment
import hero.model.StorageEntityType
import hero.model.User
import hero.model.topics.StripeChargeReceived
import hero.model.topics.StripeChargeReceived.WithId
import hero.model.topics.StripeChargeReceived.WithoutId
import hero.repository.notification.NotificationRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import hero.stripe.model.ChargeStatus
import hero.stripe.service.StripeClients
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import org.jooq.DSLContext
import java.time.Instant

@Suppress("unused")
class StripeChargeReceivedHandler(
    private val production: Boolean = SystemEnv.isProduction,
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
) : PubSubSubscriber<StripeChargeReceived>() {
    private val notificationRepository = NotificationRepository(lazyContext)
    private val postPaymentRepository = firestore.typedCollectionOf(PostPayment)
    private val messageThreadRepository = firestore.typedCollectionOf(MessageThread)
    private val userRepository = firestore.typedCollectionOf(User)
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)
    private val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    private val stripeChargeStatisticsHandler = StripeChargeStatisticsHandler(
        pubSub,
        firestore.typedCollectionOf(User),
        stripeClients,
        lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    )
    private val feeMetadataHandler = StripeFeeMetadataHandler(stripeClients)
    private val stripeService = StripeService(stripeClients, pubSub)
    private val stripePaymentMethods = StripePaymentMethodsService(stripeClients, stripeService, pubSub)

    override fun consume(payload: StripeChargeReceived) {
        when (payload) {
            is WithId -> consume(payload)
            is WithoutId -> consume(payload)
        }
    }

    private fun consume(payload: WithId) {
        log.info("Processing charge $payload")
        val charge = stripeClients[payload.currency]
            .charges()
            .retrieve(
                payload.chargeId,
                ChargeRetrieveParams.builder().addAllExpand(listOf("invoice.subscription", "transfer")).build(),
            )
        try {
            stripeChargeStatisticsHandler.handle(charge)
        } catch (e: Exception) {
            log.fatal("Couldn't store statistics for charge ${charge.id}: ${e.message}", cause = e)
        }

        val status = parseEnum<ChargeStatus>(charge.status)
        if (status == ChargeStatus.FAILED) {
            log.info("Charge ${charge.id} has status failed, skipping.")
            return
        }

        try {
            feeMetadataHandler.handle(charge)
        } catch (e: Exception) {
            log.fatal("Couldn't handle metadata for charge ${charge.id}: ${e.message}", cause = e)
        }

        if (charge.paymentIntent != null && status == ChargeStatus.SUCCEEDED) {
            unlockPostByCharge(charge, payload.currency)
        }

        log.info("Finished processing charge $payload")
    }

    private fun unlockPostByCharge(
        charge: Charge,
        currency: Currency,
    ) {
        val paymentIntent = stripeService.paymentIntent(charge.paymentIntent, currency)
        // pre 2022-05 charges had - instead of _, can be removed later
        if (paymentIntent.metadata["type"]?.replace("-", "_") != PaymentType.POST_UNLOCK.name.lowercase()) {
            // this charge is not related to post-unlocking
            log.debug("Charge ${charge.id} not related to post-unlocking")
            return
        }
        val postId = paymentIntent.metadata["postId"]
        val userId = paymentIntent.metadata["userId"]
        val creatorId = paymentIntent.metadata["creatorId"]
        val messageThreadId = paymentIntent.metadata["messageThreadId"]

        if (postId == null || userId == null || creatorId == null || messageThreadId == null) {
            log.fatal("Missing meta information to process ${charge.id}/${charge.paymentIntent}.")
            return
        }

        // https://linear.app/herohero/issue/HH-1902/add-info-about-retry-to-payment-section-after-payment-doesnt-go
        userRepository[userId].field(User::lastChargeFailedAt).update(null)

        val timestamp = Instant.ofEpochSecond(paymentIntent.created)
        // PostPayment is also created in StripePaymentsService
        if (postPaymentRepository[PostPayment.id(userId, postId)].fetch() == null) {
            savePayment(postId = postId, userId = userId, timestamp = timestamp)
        }

        val notification = notificationRepository.find {
            this
                .where(Tables.NOTIFICATION.TYPE.eq(NotificationType.PAID_POST.name))
                .and(Tables.NOTIFICATION.OBJECT_POST_ID.eq(postId))
        }

        if (notification.isEmpty()) {
            saveNotification(creatorId = creatorId, userId = userId, postId = postId, timestamp = timestamp)
        }
    }

    private fun consume(payload: WithoutId) {
        stripeChargeStatisticsHandler.handle(
            chargeId = payload.chargeId,
            paymentMethodId = payload.paymentMethodId,
            customerId = payload.customerId,
            succeededCharges = payload.succeededCharges,
            declineCode = payload.declineCode,
            declineMessage = payload.declineMessage,
            currency = payload.currency,
        )
    }

    private fun savePayment(
        postId: String,
        userId: String,
        timestamp: Instant,
    ) {
        PostPayment(
            postId = postId,
            userId = userId,
            timestamp = timestamp,
        ).also {
            postPaymentRepository[it.id].set(it)
        }
    }

    private fun saveNotification(
        creatorId: String,
        userId: String,
        postId: String,
        timestamp: Instant,
    ) {
        Notification(
            userId = creatorId,
            type = NotificationType.PAID_POST,
            actorIds = listOf(userId),
            objectId = postId,
            objectType = StorageEntityType.POST,
            created = timestamp,
            timestamp = timestamp,
        ).also {
            notificationRepository.save(it)
        }
    }
}
