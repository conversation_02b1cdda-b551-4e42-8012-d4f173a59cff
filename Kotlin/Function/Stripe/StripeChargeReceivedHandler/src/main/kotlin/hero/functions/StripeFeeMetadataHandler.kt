package hero.functions

import com.stripe.model.Charge
import com.stripe.net.RequestOptions
import com.stripe.param.ChargeUpdateParams
import hero.baseutils.log
import hero.model.Currency
import hero.model.Tier
import hero.stripe.service.StripeClients

/**
 * When creator is charged with application fee, we want to inform the creator about the
 * fee structure. This handler will extract subscription metadata and write them to corresponding
 * Payment in Creator's connected account.
 */
class StripeFeeMetadataHandler(
    private val stripeClients: StripeClients,
) {
    fun handle(charge: Charge) {
        storeApplicationFeesWithinCharges(charge)
        storeApplicationFeesInHumanReadbleFormForCreator(charge)
    }

    /**
     * Store details of Herohero/Stripe fees so that these can be correctly invoiced.
     */
    internal fun storeApplicationFeesWithinCharges(charge: Charge) {
        if (charge.currency != Currency.USD.name.lowercase()) {
            // for now, we perform this propagation only for USD
            return
        }

        // note that metadata from Post unlocks/Gift payments are propagated automatically
        val sourceMetadata = charge.invoiceObject?.subscriptionObject?.metadata
        if (sourceMetadata?.get("appFeeHerohero") == null) {
            // charge does not contain relevant information, we are done
            return
        }

        // TODO
        // At this point we are unsure of the future of US Stripe fees, so the final solution is
        // yet to be decided. Current solution disallow percentual discounts, so the values of
        // appFeeHerohero, appFeeStripe are always absolute and cannot be discounted.
        // So for now, we can safely take these and copy them in full to USD charges.
        charge.update(
            ChargeUpdateParams.builder()
                .setMetadata(
                    mapOf(
                        "appFeeHerohero" to sourceMetadata["appFeeHerohero"],
                        "appFeeStripeDynamic" to sourceMetadata["appFeeStripeDynamic"],
                        "appFeeStripeFixed" to sourceMetadata["appFeeStripeFixed"],
                    ),
                )
                .build(),
        )
    }

    /**
     * Store details of application fee so that the pricing is clear for creators.
     * It will allow them to distinguish how much was charged in the name of Herohero
     * and how much in the name of Stripe.
     *
     * For instance, see metadata of this Connected account payment:
     * https://dashboard.stripe.com/connect/accounts/acct_1RJJXpBOSXndGBvF/payments/py_1ROJ2cBOSXndGBvFjGcsdxl6
     */
    internal fun storeApplicationFeesInHumanReadbleFormForCreator(charge: Charge) {
        if (charge.currency != Currency.USD.name.lowercase()) {
            // for now, we perform this propagation only for USD
            return
        }

        // note that metadata from Post unlocks/Gift payments are propagated automatically
        val sourceMetadata = charge.invoiceObject?.subscriptionObject?.metadata
        if (sourceMetadata?.get("appFeeHerohero") == null) {
            // charge does not contain relevant information, we are done
            return
        }

        log.info(
            "Handling Stripe fee propagation for " +
                "${charge.currency}/${charge.invoiceObject?.id}/${charge.invoiceObject?.subscriptionObject?.id}",
        )

        val transfer = charge.transferObject

        val payment = stripeClients[charge.currency].charges()
            .retrieve(
                transfer.destinationPayment,
                RequestOptions.builder().setStripeAccount(transfer.destination).build(),
            )

        val tier = Tier.ofId(sourceMetadata["tierId"]!!)
        val symbol = tier.currency.symbol

        val metadata = mapOf(
            "Fee Herohero ${tier.heroheroFee}%" to symbol + sourceMetadata["appFeeHerohero"],
            "Fee Stripe ${tier.stripeFeeDynamic}%" to symbol + sourceMetadata["appFeeStripeDynamic"],
            "Fee Stripe ${symbol}${tier.stripeFeeFixed}" to symbol + sourceMetadata["appFeeStripeFixed"],
        )

        // copy related metadata to connected account payment
        payment.update(
            ChargeUpdateParams.builder()
                .setMetadata(metadata)
                .build(),
            RequestOptions.builder().setStripeAccount(transfer.destination).build(),
        )
    }
}
