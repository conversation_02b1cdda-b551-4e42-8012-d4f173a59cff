package hero.functions

import hero.baseutils.systemEnv
import hero.model.Currency
import hero.stripe.model.StripeFeeCsvReportGenerated
import hero.stripe.service.StripeClients
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Test

class StripeInterchangeFeesWriterIT {
    private val underTest = spyk(
        StripeInterchangeFeesWriter(
            StripeClients(systemEnv("STRIPE_API_KEY_EU_PROD"), systemEnv("STRIPE_API_KEY_EU_PROD")),
        ),
    )

    @Test
    fun `correctly process stripe fees CSV file`() {
        val currency = Currency.EUR
        every { underTest.writeMeta(any(), currency, any()) } just runs

        underTest.consume(
            StripeFeeCsvReportGenerated(
                currency,
                "https://files.stripe.com/v1/files/file_1RP0SKB6ZCHekl2RtX7YPUBO/contents",
            ),
        )

        verify(exactly = 17) { underTest.writeMeta(any(), currency, any()) }
        verify(exactly = 0) { underTest.writeMeta("", currency, any()) }
        verify(exactly = 1) {
            underTest.writeMeta(
                "ch_3RLNgkB6ZCHekl2R0No7rgpr",
                currency,
                mapOf(
                    "fee_stripe_fee" to "0.079400",
                    "fee_network_cost" to "0.130074",
                    "fee_total" to "0.209474",
                ),
            )
        }
    }
}
