Kotlin/Function/Post/PostStuckInProcessingAlerter/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Gjirafa/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/PostStuckInProcessingAlerter/variables:
  variables:
    FUNCTION_NAME: "post-stuck-in-processing-alerter"
    CLASS_NAME: "hero.functions.PostStuckInProcessingAlerter"
    TOPIC: "Minutely"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Post/PostStuckInProcessingAlerter/deploy-devel:
  needs:
    - Kotlin/Function/Post/PostStuckInProcessingAlerter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/PostStuckInProcessingAlerter/variables

Kotlin/Function/Post/PostStuckInProcessingAlerter/deploy-prod:
  needs:
    - Kotlin/Function/Post/PostStuckInProcessingAlerter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/PostStuckInProcessingAlerter/variables
