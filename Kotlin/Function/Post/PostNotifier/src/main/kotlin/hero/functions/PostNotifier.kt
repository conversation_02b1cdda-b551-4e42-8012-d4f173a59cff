package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusMinutes
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.isNull
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Post
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.User
import hero.model.topics.EmailPublished
import hero.model.topics.Minutely
import hero.model.topics.PostNotificationGroupPrepared
import hero.model.topics.PostState
import java.time.Instant
import kotlin.concurrent.thread

@Suppress("Unused")
class PostNotifier(
    private val hostname: String = SystemEnv.hostname,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val postsCollection: TypedCollectionReference<Post> = firestore.typedCollectionOf(Post),
    private val subscribersCollection: TypedCollectionReference<Subscriber> = firestore.typedCollectionOf(Subscriber),
    private val usersCollection: TypedCollectionReference<User> = firestore.typedCollectionOf(User),
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
) : PubSubSubscriber<Minutely>() {
    override fun consume(payload: Minutely) {
        val now = Instant.now()
        val minutes = if (SystemEnv.environment == "devel") 0 else CREATED_AT_THRESHOLD
        val threshold = Instant.now().minusMinutes(minutes)
        val postsToNotify = postsCollection
            .where(Post::notifiedAt).isNull()
            .and(Post::messageThreadId).isNull()
            .and(Post::parentId).isNull()
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .and(Post::published).isLessThan(Instant.now())
            // fetch all new posts
            .fetchAll()
            // notify only after 10 minutes of existence to prevent spamming for deleted posts
            .filter { it.created < threshold || it.isLivestreamLive() }

        postsToNotify
            .map { post -> Pair(post.userId, post.id) }
            // mark these posts as notified
            .onEach { (_, postId) -> thread { postsCollection[postId].field(Post::notifiedAt).update(now) } }
            // group postIds by creators
            .groupBy({ (creatorId, _) -> creatorId }, { (_, postId) -> postId })
            .forEach { (creatorId, postIds) -> processCreatorPosts(creatorId, postIds) }

        // notify creators for published posts
        postsToNotify
            // filter out scheduled posts, published at least 5 minutes after creation
            .filter { it.created < it.published.minusMinutes(PUBLISH_AT_THRESHOLD) }
            // find creators
            .map { post -> usersCollection[post.userId].fetch() to post }
            // clean non-notifiable creators
            .filter { (user, _) -> user != null && !user.email.isNullOrBlank() }
            // send emails to creators
            .forEach { (user, post) ->
                log.info(
                    "Notifying creator ${user!!.id} about published post ${post.id}" +
                        " at ${post.published}, created at ${post.created}.",
                    mapOf("creatorId" to user.id, "postId" to post.id),
                )
                pubSub.publish(
                    EmailPublished(
                        to = user.email!!,
                        template = "post-published",
                        language = user.language,
                        variables = listOf(
                            "user-name" to user.name,
                            "post-link" to "$hostname/${user.path}/post/${post.id}",
                        ),
                    ),
                )
            }
    }

    private fun processCreatorPosts(
        creatorId: String,
        postIds: List<String>,
    ) {
        val now = Instant.now()
        try {
            subscribersOf(creatorId)
                .chunked(CHUNK_SIZE)
                .map { chunkOfSubscriberIds -> Triple(creatorId, postIds, chunkOfSubscriberIds) }
                // and create chunks of notification requests
                .forEach { (creatorId, postIds, chunkOfSubscriberIds) ->
                    log.info(
                        "Created a notification request for $postIds of creator $creatorId with" +
                            " batch of ${chunkOfSubscriberIds.size} subscribers.",
                        mapOf("creatorId" to creatorId),
                    )
                    pubSub.publish(
                        PostNotificationGroupPrepared(
                            creatorId = creatorId,
                            postIds = postIds,
                            subscriberIds = chunkOfSubscriberIds,
                            timestamp = now,
                        ),
                    )
                }
        } catch (e: Exception) {
            log.fatal(
                "Failed to process posts ${postIds.joinToString()} of creator $creatorId",
                mapOf("creatorId" to creatorId),
                e,
            )

            // we need to try again
            postIds.forEach {
                postsCollection[it].field(Post::notifiedAt).update(null)
            }
        }
    }

    private fun subscribersOf(creatorId: String): List<String> =
        subscribersCollection
            .where(Subscriber::creatorId).isEqualTo(creatorId)
            .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
            .fetchAll()
            .map { it.userId }
}

private const val CREATED_AT_THRESHOLD = 10L
private const val CHUNK_SIZE = 100
private const val PUBLISH_AT_THRESHOLD = 5L
