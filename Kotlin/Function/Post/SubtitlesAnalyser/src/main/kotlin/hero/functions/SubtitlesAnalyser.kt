package hero.functions

import com.github.kittinunf.fuel.httpGet
import com.google.cloud.vertexai.VertexAI
import com.google.cloud.vertexai.generativeai.ContentMaker
import com.google.cloud.vertexai.generativeai.GenerativeModel
import com.google.cloud.vertexai.generativeai.ResponseHandler
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Post
import hero.model.PostAssetAnalysis
import hero.model.PostAssetAnalysisScore
import hero.model.SlackBlock
import hero.model.SlackBlockText
import hero.model.SlackMessage
import hero.model.topics.PostState
import hero.model.topics.SubtitlesGenerationCompleted
import hero.repository.post.JooqPostHelper
import hero.repository.post.PostType
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import org.jooq.impl.DSL

@Suppress("Unused")
class SubtitlesAnalyser(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val isProduction: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction),
    private val postsCollection: TypedCollectionReference<Post> = firestore.typedCollectionOf(Post),
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    private val hostname: String = SystemEnv.hostname,
) : PubSubSubscriber<SubtitlesGenerationCompleted>() {
    private val context by lazyContext

    override fun consume(payload: SubtitlesGenerationCompleted) {
        val post = findPostByAsset(payload.assetId)
        if (post == null) {
            log.info("Failed to find post for asset ${payload.assetId}")
            return
        }

        VertexAI(SystemEnv.cloudProject, "europe-west1").use {
            val model = GenerativeModel("gemini-2.0-flash-lite-001", it)

            val text = payload.filePath
                .httpGet()
                .response()
                .second
                .body()
                .asString("text/plain")
                .lines()
                .filter { line ->
                    line.isNotBlank() && !timestampRegex.matches(line) && !line.startsWith("WEBVTT")
                }
                .joinToString()

            val summaryResponse = model
                .generateContent(
                    ContentMaker.fromString(
                        """
                            Task: Summarize the provided video subtitles into a concise summary using the structure below.
                            Follow this same structure for all future summaries to keep them consistent and easy to review.
                            Every structure point should be covered with a single sentence. Keep every sentence concise. 

                            Summary Structure:

                            Topic/Context – What is the video about? Briefly introduce the subject or main question being addressed.
                            Main Ideas – Summarize the key points or arguments made in the video. Focus on the central themes.
                            Speaker Insights – Include any notable quotes or unique perspectives from the speaker(s).
                            Tone/Style – Describe the overall tone or mood (e.g. formal, conversational, humorous).
                            Conclusion/Takeaways – Summarize any final thoughts, conclusions, or lessons from the video.
                            Toxicity/Moderation – Note if the video includes any toxic, harmful, or inappropriate content. This includes things like personal attacks, hate speech, racism, harassment, or incitement. If nothing concerning is found, explicitly state: “No toxic or harmful content detected.”
                            
                            Use Slack markdown format.
                        $text
                        """.trimIndent(),
                    ),
                )
            val summary = ResponseHandler.getText(summaryResponse)

            val toxicityScaleResponse = model
                .generateContent(
                    ContentMaker.fromString(
                        """
                            You have previously created a summary based on the provided subtitles.

                            Here is the summary:
                            $summary

                            Here are the full subtitles:
                            $text

                            Now, evaluate the overall level of toxicity in the content.

                            Return a single numeric value from 1 to 5, based on the scale below:

                            1 – No toxicity. Completely clean content. No personal attacks, offensive language, or harmful behavior.  
                            2 – Slightly edgy or informal, but still acceptable. May include light sarcasm or frustration, but nothing harmful.  
                            3 – Borderline inappropriate. Includes mild insults, swearing, or disrespectful remarks that may be offensive to some.  
                            4 – Toxic behavior present. Includes bullying, harassment, targeted attacks, or clear disrespect toward others.  
                            5 – Highly toxic and unacceptable. Includes hate speech, racism, threats, or content that incites violence or should be escalated to a moderator.

                            Be strict but fair. Focus on the actual tone and language, not just the topic. If unsure, err on the side of caution.

                            Only reply with the number (1 to 5).
                            
                            Respond with a machine-readable CSV, following this example:
                            <1-5>,<short explanation of why this score was chosen>
                        """.trimIndent(),
                    ),
                )

            val toxicityScale = ResponseHandler.getText(toxicityScaleResponse)
                .replace("```json", "")
                .replace("```", "")
                .let { response ->
                    try {
                        val splitResponse = response.split(",", limit = 2)
                        require(splitResponse.size == 2) { "Got $splitResponse items instead of 2 items" }

                        val toxicityRating = splitResponse[0].trim().toInt()
                        val reason = splitResponse[1].trim()
                        ToxicityScaleResponse(toxicityRating, reason)
                    } catch (e: Exception) {
                        log.fatal("Failed to parse $response", cause = e)
                        ToxicityScaleResponse(1, "")
                    }
                }

            val postAssetAnalysis = PostAssetAnalysis(
                summary = summary,
                toxicityScore = PostAssetAnalysisScore(toxicityScale.toxicityRating, toxicityScale.reason),
            )

            val updatedAssets = post.assets.map { asset ->
                if (asset.gjirafa?.id == payload.assetId) {
                    asset.copy(analysis = postAssetAnalysis)
                } else {
                    asset
                }
            }
            postsCollection[post.id].set(post.copy(assets = updatedAssets))

            if (toxicityScale.toxicityRating > 2) {
                val body = """
<$hostname/${post.userId}/post/${post.id}|Post> was flagged as unsafe.
*Toxicity score: ${toxicityScale.toxicityRating}*
*Reason: ${toxicityScale.reason}*

${summary.trim()}
                """.trimIndent()

                pubSub.publish(
                    SlackMessage(
                        channel = "alerts_subtitles_${isProduction.envPrefix}",
                        blocks = listOf(SlackBlock(SlackBlockText(body))),
                    ),
                )
            }
            log.info(
                "Generated summary for ${payload.assetId} and post ${post.id}, summary: $summary," +
                    " rating: $toxicityScale",
            )
        }
    }

    private fun findPostByAsset(assetId: String): Post? {
        return context
            .select(JooqPostHelper.postFields)
            .from(Tables.POST_ASSET)
            .join(Tables.POST).on(Tables.POST_ASSET.POST_ID.eq(Tables.POST.ID))
            .where(DSL.jsonbGetAttributeAsText(Tables.POST_ASSET.METADATA, "id").eq(assetId))
            .and(Tables.POST.STATE.notEqual(PostState.REVISION.name))
            .and(Tables.POST.TYPE.eq(PostType.CONTENT_POST.name))
            .fetchOne()
            ?.map { JooqPostHelper.mapRecordToEntity(it) }
    }
}

private data class ToxicityScaleResponse(val toxicityRating: Int, val reason: String)

private val timestampRegex = Regex("""\d{2}:\d{2}\.\d{3} --> \d{2}:\d{2}\.\d{3}""")
