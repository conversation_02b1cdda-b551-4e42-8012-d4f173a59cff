Kotlin/Function/Post/ScheduledPostPublisher/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Repository/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/ScheduledPostPublisher/variables:
  variables:
    FUNCTION_NAME: "scheduled-post-publisher"
    CLASS_NAME: "hero.functions.ScheduledPostPublisher"
    TOPIC: "Minutely"
    # TODO limit service account to SQL + Firestore

Kotlin/Function/Post/ScheduledPostPublisher/deploy-devel:
  needs:
    - Kotlin/Function/Post/ScheduledPostPublisher/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/ScheduledPostPublisher/variables

Kotlin/Function/Post/ScheduledPostPublisher/deploy-prod:
  needs:
    - Kotlin/Function/Post/ScheduledPostPublisher/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/ScheduledPostPublisher/variables
