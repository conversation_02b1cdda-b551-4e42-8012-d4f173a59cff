Kotlin/Function/Post/CommentsNotifier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Repository/build
    - Kotlin/Modules/Firebase/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/CommentsNotifier/variables:
  variables:
    FUNCTION_NAME: "comments-notifier"
    CLASS_NAME: "hero.functions.CommentsNotifier"
    TOPIC: "PostStateChanged"
    ENV_VARS: "FF_PUSH_NOTIFICATION=enabled"

Kotlin/Function/Post/CommentsNotifier/deploy-devel:
  needs:
    - Kotlin/Function/Post/CommentsNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/CommentsNotifier/variables

Kotlin/Function/Post/CommentsNotifier/deploy-staging:
  needs:
    - Kotlin/Function/Post/CommentsNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Post/CommentsNotifier/variables

Kotlin/Function/Post/CommentsNotifier/deploy-prod:
  needs:
    - Kotlin/Function/Post/CommentsNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/CommentsNotifier/variables
