package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Path
import hero.model.UserStateChanged
import java.time.Instant

class UserPathHandler(
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val pathCollection: TypedCollectionReference<Path> = firestore.typedCollectionOf(Path),
) : PubSubSubscriber<UserStateChanged>() {
    override fun consume(payload: UserStateChanged) {
        val user = payload.user
        pathCollection
            .where(Path::userId).isEqualTo(user.id)
            .fetchAll()
            .forEach {
                // reset the abandoned timestamp
                pathCollection[it.id].field(Path::abandoned).update(null)
            }

        pathCollection
            .where(Path::userId).isEqualTo(user.id)
            .fetchAll()
            .onEach { path ->
                // all unused paths are marked as abandoned, otherwise are set null
                val newAbandoned = if (path.id == user.path || path.id == user.id) {
                    null
                } else {
                    path.abandoned ?: Instant.now()
                }
                if (newAbandoned != path.abandoned) {
                    log.info(
                        "Setting path ${path.id} (current ${user.path} || ${user.id}) to abandoned = $newAbandoned.",
                        mapOf("userId" to path.userId),
                    )
                    pathCollection[path.id].field(Path::abandoned).update(newAbandoned)
                }
            }

        pathCollection
            .where(Path::abandoned).isLessThan(Instant.now().minusDays(21))
            .fetchAll()
            .forEach { path ->
                log.info("Deleting abandoned path ${path.id} at ${path.abandoned}.", mapOf("userId" to path.userId))
                pathCollection[path.id].delete()
            }
    }
}
