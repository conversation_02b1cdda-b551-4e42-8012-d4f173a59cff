package hero.functions

import dev.fuxing.airtable.AirtableApi
import dev.fuxing.airtable.AirtableApi.Table
import dev.fuxing.airtable.AirtableRecord
import hero.baseutils.SystemEnv
import hero.baseutils.systemEnv
import hero.gcloud.FirestoreRef
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.serverless.CloudHttpFunction
import hero.model.DeviceType
import hero.model.User
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.serverless.GoogleCloudHttpFunction

@Suppress("unused")
class AirTableAppFeedback(
    airTableApp: String = "appeNHKgTC1cIpwDj",
    airTableTable: String = "tblHe9oUUNnWxK7ds",
    private val airtable: Table = AirtableApi(systemEnv("AIRTABLE_TOKEN")).base(airTableApp).table(airTableTable),
    firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
) : CloudHttpFunction() {
    private val usersCollection = firestore.typedCollectionOf(User)

    @Suppress("Unused")
    class EntryClass : GoogleCloudHttpFunction(AirTableAppFeedback())

    private val routePostFeedback: ContractRoute =
        "/".post(
            summary = "Post a feedback",
            tag = "Feedback",
            parameters = object {},
            responses = listOf(Status.NO_CONTENT to Unit),
            receiving = null,
            handler = { request, _ ->
                val jwtUser = request.getJwtUser()
                val body = lens<PostFeedbackRequest>(request)
                val user = usersCollection[jwtUser.id].get()

                val airtableRecord = AirtableRecord()
                    .apply {
                        putField("Feedback", body.feedback)
                        putField("DeviceType", body.platform.name)
                        putField("UserId", user.id)
                        if (user.email != null) {
                            putField("Email", user.email)
                        }
                        if (body.deviceDetails != null) {
                            putField("DeviceDetails", body.deviceDetails)
                        }
                        if (body.appVersion != null) {
                            putField("AppVersion", body.appVersion)
                        }
                    }

                airtable.post(airtableRecord)

                Response(Status.NO_CONTENT)
            },
        )

    override fun contractRoutes(): List<ContractRoute> = listOf(routePostFeedback)
}

data class PostFeedbackRequest(
    val feedback: String,
    val platform: DeviceType,
    val deviceDetails: String?,
    val appVersion: String?,
)
