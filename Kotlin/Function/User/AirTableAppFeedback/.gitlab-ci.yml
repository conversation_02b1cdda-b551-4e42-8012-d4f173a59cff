Kotlin/Function/User/AirTableAppFeedback/build:
  stage: build-services
  needs:
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Http4k/build
    - Kotlin/Modules/Jwt/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/User/AirTableAppFeedback/variables:
  variables:
    FUNCTION_NAME: "airtable-feedback"
    CLASS_NAME: "hero.functions.AirTableAppFeedback$$EntryClass"
    TRIGGER: "--trigger-http"
    SERVICE_ACCOUNT: "<EMAIL>"
    ENV_VARS: "AIRTABLE_TOKEN=$AIRTABLE_TOKEN"

Kotlin/Function/User/AirTableAppFeedback/deploy-devel:
  needs:
    - Kotlin/Function/User/AirTableAppFeedback/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/User/AirTableAppFeedback/variables

Kotlin/Function/User/AirTableAppFeedback/deploy-production:
  needs:
    - Kotlin/Function/User/AirTableAppFeedback/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/User/AirTableAppFeedback/variables
