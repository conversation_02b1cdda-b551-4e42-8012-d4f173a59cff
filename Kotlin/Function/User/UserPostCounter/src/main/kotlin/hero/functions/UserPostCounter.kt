package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.instantMin
import hero.baseutils.log
import hero.gcloud.contains
import hero.gcloud.firestore
import hero.gcloud.isNull
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Category
import hero.model.MessageThread
import hero.model.Post
import hero.model.SupportCounts
import hero.model.User
import hero.model.topics.PostState
import hero.model.topics.PostStateChanged
import java.time.Instant

@Suppress("Unused")
class UserPostCounter : PubSubSubscriber<PostStateChanged>() {
    private val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    private val usersCollection = firestore.typedCollectionOf(User)
    private val categoriesCollection = firestore.typedCollectionOf(Category)
    private val messageThreadsCollection = firestore.typedCollectionOf(MessageThread)
    private val postsCollection = firestore.typedCollectionOf(Post)

    override fun consume(payload: PostStateChanged) {
        val post = payload.post
        if (post.parentId != null) {
            // we don't count user's comments, only posts
            log.debug("Skipping comment (is not post) ${post.id}.", mapOf("userId" to post.userId))
            return
        }
        if (post.messageThreadId == null) {
            handleUserPostCounts(post)
        }
        if (post.messageThreadId != null) {
            handleUserMessageThreadCounts(post)
        }
    }

    private fun handleUserPostCounts(post: Post) {
        log.info("Handling counts for Post ${post.id}.", mapOf("userId" to post.userId))
        if (post.state == PostState.PUBLISHED) {
            usersCollection[post.userId].field(User::lastPostAt).update(Instant.now())
        }

        usersCollection[post.userId]
            .field(root(User::counts).path(SupportCounts::posts))
            .update(countUserPosts(post.userId))

        post.categories
            .forEach { categoryId ->
                categoriesCollection[categoryId]
                    .field(Category::postCount)
                    .update(countCategoryPosts(categoryId))
            }
    }

    private fun countUserPosts(userId: String): Long =
        postsCollection
            .where(Post::userId).isEqualTo(userId)
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .and(Post::parentId).isNull()
            .and(Post::messageThreadId).isNull()
            .count()

    private fun countCategoryPosts(categoryId: String): Long =
        postsCollection
            .where(Post::categories).contains(categoryId)
            .and(Post::parentId).isNull()
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .count()

    private fun handleUserMessageThreadCounts(post: Post) {
        log.info(
            "Handling post count for message thread ${post.messageThreadId}.",
            mapOf("userId" to post.userId, "messageThreadId" to post.messageThreadId),
        )

        val postCount = postsCollection
            .where(Post::messageThreadId).isEqualTo(post.messageThreadId)
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .count()

        val messageThreadReference = messageThreadsCollection[post.messageThreadId!!]
        val messageThread = messageThreadReference.get()
        val newMessageThread = messageThread
            .copy(posts = postCount)
            .let {
                if (it.lastMessageAt == null || it.lastMessageAt!! < post.created) {
                    it.copy(
                        lastMessageAt = post.created,
                        lastMessageBy = post.userId,
                        lastMessageId = post.id,
                        emailNotified = false,
                    )
                } else {
                    it
                }
            }
            .let {
                if ((it.seens[post.userId] ?: instantMin) < post.created) {
                    it.copy(
                        seens = it.seens.plus(post.userId to post.created),
                    )
                } else {
                    it
                }
            }
            .let {
                if ((it.checks[post.userId] ?: instantMin) < post.created) {
                    it.copy(
                        checks = it.checks.plus(post.userId to post.created),
                    )
                } else {
                    it
                }
            }
        messageThreadReference.set(newMessageThread)
    }
}
