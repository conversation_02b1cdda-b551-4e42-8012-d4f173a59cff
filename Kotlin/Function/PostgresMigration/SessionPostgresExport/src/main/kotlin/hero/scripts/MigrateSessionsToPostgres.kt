package hero.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.retryOn
import hero.core.data.Sort
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Session
import hero.repository.session.JooqSessionHelper
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.util.concurrent.ExecutionException

fun main() {
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val firestore = firestore(SystemEnv.cloudProject, true)
    val sessionsCollection = firestore.typedCollectionOf(Session)
    var lastId: String? = "21abdec4-9ba9-49e4-9933-34f4895cf580"
    var processed = 0

    do {
        println("Processing sessions from id $lastId")
        val sessions = retryOn(ExecutionException::class) {
            sessionsCollection.where(Session::id).isNotEqualTo("")
                .orderBy(Session::id, Sort.Direction.DESC)
                .limit(200)
                .startAfterIfNotNull(lastId)
                .fetchAll()
        }

        if (sessions.isEmpty()) {
            break
        }

        saveSessions(context, sessions)

        processed += sessions.size
        lastId = sessions.last().id
        println("Processed $processed sessions")
    } while (true)
}

private fun saveSessions(
    context: DSLContext,
    sessions: List<Session>,
) {
    val sessionRecords = sessions.map { JooqSessionHelper.mapEntityToRecord(it) }
    val sessionsIds = sessionRecords.map { it.id }
    context.deleteFrom(Tables.SESSION)
        .where(Tables.SESSION.ID.`in`(sessionsIds))
        .execute()

    context.insertInto(Tables.SESSION)
        .set(sessionRecords)
        .execute()
}
