package hero.scripts

import hero.baseutils.SystemEnv
import hero.core.data.Sort
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.User
import hero.repository.user.UserRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking

fun main() {
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val firestore = firestore(SystemEnv.cloudProject, true)
    val usersCollection = firestore.typedCollectionOf(User)
    val userRepository = UserRepository(context)
    var lastId: String? = null
    var processed = 0

    runBlocking(Dispatchers.Default) {
        do {
            println("Processing users from id $lastId")
            val users = usersCollection.where(User::id).isNotEqualTo("")
                .orderBy(User::id, Sort.Direction.DESC)
                .limit(100)
                .startAfterIfNotNull(lastId)
                .fetchAll()

            if (users.isEmpty()) {
                break
            }

            users
                .map {
                    async {
                        userRepository.save(it)
                    }
                }
                .awaitAll()

            processed += users.size
            lastId = users.last().id
            println("Processed $processed users")
        } while (true)
    }
}
