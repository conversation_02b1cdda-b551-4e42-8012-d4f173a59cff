package hero.repository.notification

import hero.model.NotificationType
import hero.model.StorageEntityType
import hero.repository.RepositoryTest
import hero.repository.notification
import hero.repository.post
import hero.repository.user
import hero.sql.jooq.Tables.NOTIFICATION
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class NotificationRepositoryIT : RepositoryTest() {
    @Nested
    inner class Save {
        @Test
        fun `should save single notification`() {
            val underTest = NotificationRepository(testContext)

            createUser(user("user-id"))
            val notification = notification(
                userId = "user-id",
                type = NotificationType.NEW_POST,
                createdAt = Instant.ofEpochSecond(1738769498),
                actorIds = listOf("cestmir"),
                seenAt = Instant.ofEpochSecond(1738769502),
                checkedAt = Instant.ofEpochSecond(1738769504),
                objectId = null,
                objectType = StorageEntityType.POST,
                id = "user-id-1738769498",
            )

            underTest.save(notification)

            val exportedNotification = testContext
                .selectFrom(NOTIFICATION)
                .fetchSingle()

            assertThat(exportedNotification[NOTIFICATION.ID]).isEqualTo("user-id-1738769498")
            assertThat(exportedNotification[NOTIFICATION.TYPE]).isEqualTo("NEW_POST")
            assertThat(exportedNotification[NOTIFICATION.ACTOR_IDS]).isEqualTo(arrayOf("cestmir"))
            assertThat(exportedNotification[NOTIFICATION.CREATED_AT]).isEqualTo(Instant.ofEpochSecond(1738769498))
            assertThat(exportedNotification[NOTIFICATION.SEEN_AT]).isEqualTo(Instant.ofEpochSecond(1738769502))
            assertThat(exportedNotification[NOTIFICATION.CHECKED_AT]).isEqualTo(Instant.ofEpochSecond(1738769504))
            assertThat(exportedNotification[NOTIFICATION.DELETED_AT]).isNull()
            assertThat(exportedNotification[NOTIFICATION.OBJECT_POST_ID]).isNull()
            assertThat(exportedNotification[NOTIFICATION.OBJECT_USER_ID]).isNull()
        }

        @Test
        fun `should save notification with storage type POST to objectPostId`() {
            val underTest = NotificationRepository(testContext)

            createUser(user("user-id"))
            createPost(post(userId = "user-id", id = "post-id"))
            val notification = notification(
                userId = "user-id",
                type = NotificationType.NEW_POST,
                objectId = "post-id",
                objectType = StorageEntityType.POST,
            )

            underTest.save(notification)

            val exportedNotification = testContext
                .selectFrom(NOTIFICATION)
                .fetchSingle()

            assertThat(exportedNotification[NOTIFICATION.OBJECT_POST_ID]).isEqualTo("post-id")
            assertThat(exportedNotification[NOTIFICATION.OBJECT_USER_ID]).isNull()
        }

        @Test
        fun `should save notification with storage type USER to objectUserId`() {
            val underTest = NotificationRepository(testContext)

            createUser(user("user-id"))
            val notification = notification(
                userId = "user-id",
                type = NotificationType.NEW_POST,
                objectId = "user-id",
                objectType = StorageEntityType.USER,
            )

            underTest.save(notification)

            val exportedNotification = testContext
                .selectFrom(NOTIFICATION)
                .fetchSingle()

            assertThat(exportedNotification[NOTIFICATION.OBJECT_USER_ID]).isEqualTo("user-id")
            assertThat(exportedNotification[NOTIFICATION.OBJECT_POST_ID]).isNull()
        }

        @Test
        fun `should save multiple notifications`() {
            val underTest = NotificationRepository(testContext)

            createUser(user("user-id"))

            val notification1 = notification(
                userId = "user-id",
                createdAt = Instant.ofEpochSecond(1738769498),
            )
            val notification2 = notification(
                userId = "user-id",
                createdAt = Instant.ofEpochSecond(1738769499),
            )

            underTest.saveAll(listOf(notification1, notification2))

            val exportedNotificationIds = testContext
                .selectFrom(NOTIFICATION)
                .fetch()
                .map { it[NOTIFICATION.ID] }

            assertThat(exportedNotificationIds).containsExactlyInAnyOrder(notification1.id, notification2.id)
        }
    }

    @Nested
    inner class Delete {
        @Test
        fun `should mark notification as deleted`() {
            val underTest = NotificationRepository(testContext)

            createUser(user("user-id"))
            val notification = underTest.save(
                notification(
                    userId = "user-id",
                    type = NotificationType.NEW_POST,
                ),
            )

            underTest.delete(notification.id, Instant.ofEpochSecond(1738769504))

            val exportedNotification = testContext
                .selectFrom(NOTIFICATION)
                .fetchSingle()

            assertThat(exportedNotification[NOTIFICATION.DELETED_AT]).isEqualTo(Instant.ofEpochSecond(1738769504))
        }
    }

    @Nested
    inner class Find {
        @Test
        fun `should find notifications for given user`() {
            val underTest = NotificationRepository(testContext)

            createUser(user("user-id"))
            createUser(user("another-user-id"))
            val notification1 = underTest.save(
                notification(
                    userId = "user-id",
                    type = NotificationType.NEW_POST,
                    createdAt = Instant.ofEpochSecond(1738769497),
                ),
            )

            val notification2 = underTest.save(
                notification(
                    userId = "user-id",
                    type = NotificationType.NEW_LIVESTREAM,
                    createdAt = Instant.ofEpochSecond(1738769498),
                ),
            )

            underTest.save(
                notification(
                    userId = "another-user-id",
                    type = NotificationType.NEW_LIVESTREAM,
                    createdAt = Instant.ofEpochSecond(1738769499),
                ),
            )

            val results = underTest.find {
                this
                    .where(NOTIFICATION.USER_ID.eq("user-id"))
                    .orderBy(NOTIFICATION.CREATED_AT.desc())
            }

            assertThat(results).hasSize(2)
            assertThat(results).containsExactly(notification2, notification1)
        }
    }

    @Nested
    inner class GetById {
        @Test
        fun `should get notification by id`() {
            val underTest = NotificationRepository(testContext)

            createUser(user("user-id"))
            createUser(user("another-user-id"))
            val expectedNotification = underTest.save(
                notification(
                    userId = "user-id",
                    type = NotificationType.NEW_POST,
                    createdAt = Instant.ofEpochSecond(1738769497),
                ),
            )
            underTest.save(
                notification(
                    userId = "user-id",
                    type = NotificationType.NEW_LIVESTREAM,
                    createdAt = Instant.ofEpochSecond(1738769498),
                ),
            )

            val notification = underTest.getById(expectedNotification.id)

            assertThat(expectedNotification).isEqualTo(notification)
        }
    }
}
