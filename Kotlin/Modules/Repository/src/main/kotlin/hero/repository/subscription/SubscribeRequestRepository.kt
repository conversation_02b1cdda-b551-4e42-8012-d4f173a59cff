package hero.repository.subscription

import hero.exceptions.http.NotFoundException
import hero.model.SubscribeRequest
import hero.sql.jooq.Tables.SUBSCRIBE_REQUEST
import hero.sql.jooq.tables.records.SubscribeRequestRecord
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.ResultQuery
import org.jooq.SelectJoinStep
import org.jooq.exception.NoDataFoundException
import java.time.Clock
import java.time.Instant

class SubscribeRequestRepository(
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context: DSLContext by lazyContext

    constructor(context: DSLContext, clock: Clock = Clock.systemUTC()) : this(lazy { context }, clock)

    fun save(subscribeRequest: SubscribeRequest): SubscribeRequest {
        val subscribeRequestRecord = SubscribeRequestRecord().apply {
            if (subscribeRequest.id != 0L) {
                this.id = subscribeRequest.id
            }
            this.userId = subscribeRequest.userId
            this.creatorId = subscribeRequest.creatorId
            this.createdAt = subscribeRequest.createdAt
            this.updatedAt = Instant.now(clock)
            this.acceptedAt = subscribeRequest.acceptedAt
            this.declinedAt = subscribeRequest.declinedAt
            this.deletedAt = subscribeRequest.deletedAt
        }

        val createdRecord = context
            .insertInto(SUBSCRIBE_REQUEST)
            .set(subscribeRequestRecord)
            .onDuplicateKeyUpdate()
            .set(subscribeRequestRecord)
            .returning()
            .fetchSingle()

        return subscribeRequest.copy(id = createdRecord.id)
    }

    fun findSingle(condition: SelectJoinStep<out Record>.() -> ResultQuery<out Record>): SubscribeRequest? {
        return context
            .select(JooqSubscribeRequestHelper.subscribeRequestFields)
            .from(SUBSCRIBE_REQUEST)
            .condition()
            .fetchOne()
            ?.map {
                JooqSubscribeRequestHelper.mapRecordToEntity(it)
            }
    }

    fun getById(subscribeRequestId: Long): SubscribeRequest {
        return context
            .select()
            .from(SUBSCRIBE_REQUEST)
            .where(SUBSCRIBE_REQUEST.ID.eq(subscribeRequestId))
            .runCatching { JooqSubscribeRequestHelper.mapRecordToEntity(fetchSingle()) }
            .getOrElse {
                when (it) {
                    is NoDataFoundException -> throw NotFoundException()
                    else -> throw it
                }
            }
    }
}

object JooqSubscribeRequestHelper {
    val subscribeRequestFields = listOf(
        SUBSCRIBE_REQUEST.ID,
        SUBSCRIBE_REQUEST.USER_ID,
        SUBSCRIBE_REQUEST.CREATOR_ID,
        SUBSCRIBE_REQUEST.CREATED_AT,
        SUBSCRIBE_REQUEST.ACCEPTED_AT,
        SUBSCRIBE_REQUEST.DECLINED_AT,
        SUBSCRIBE_REQUEST.DELETED_AT,
    )

    fun mapRecordToEntity(record: Record) =
        SubscribeRequest(
            id = record[SUBSCRIBE_REQUEST.ID],
            userId = record[SUBSCRIBE_REQUEST.USER_ID],
            creatorId = record[SUBSCRIBE_REQUEST.CREATOR_ID],
            createdAt = record[SUBSCRIBE_REQUEST.CREATED_AT],
            acceptedAt = record[SUBSCRIBE_REQUEST.ACCEPTED_AT],
            declinedAt = record[SUBSCRIBE_REQUEST.DECLINED_AT],
            deletedAt = record[SUBSCRIBE_REQUEST.DELETED_AT],
        )
}
