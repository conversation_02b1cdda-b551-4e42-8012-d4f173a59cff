package hero.http4k.controller

import hero.baseutils.systemEnvRelaxed
import hero.http4k.OPENAPI_DOCS_PATH
import org.http4k.contract.ui.swagger.swaggerUiWebjar
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.routing.ResourceLoader.Companion.Classpath
import org.http4k.routing.RoutingHttpHandler
import org.http4k.routing.bind
import org.http4k.routing.routes

val ENV_LOCAL = systemEnvRelaxed("ENVIRONMENT") == "local"
val SERVICE_NAME: String = systemEnvRelaxed("SERVICE_NAME") ?: "Unnamed"
val SERVICE_ROOT: String = if (!ENV_LOCAL) "/$SERVICE_NAME" else ""

/**
 * Exposes Swagger UI with "/docs" path as its entry point.
 * Note that this handler assumes to be bound on the "/" root path of the whole service.
 * @param descriptionPath absolute path to API description JSON. The UI will be configured to fetch it after load.
 * @see https://www.http4k.org/guide/howto/create_a_swagger_ui/
 */
fun swaggerUi(descriptionPath: String): RoutingHttpHandler =
    routes(
        // we need to redirect manually to the swagger path because of our services prefix,
        // eg. /image/docs -> /image/docs/index.html
        "/docs" bind Method.GET to {
            Response(Status.FOUND).header("Location", "$SERVICE_ROOT/docs/index.html")
        },
        stylesheet("/docs/swagger-ui.css"),
        stylesheet("/docs/swagger-ui-base.css"),
        stylesheet("/docs/swagger-ui-blue.css"),
        "/docs" bind swaggerUiWebjar {
            url = "$SERVICE_ROOT$OPENAPI_DOCS_PATH"
            pageTitle = "${SERVICE_NAME.capitalize()} swagger docs"
            displayOperationId = true
            deepLinking = true
        },
    )

/**
 * This is a hack suggested by David Denton directly in Slack to be able to access single file on classpath.
 * https://kotlinlang.slack.com/archives/C5AL3AKUY/p1617051240040900
 */
private fun stylesheet(
    resource: String,
    root: String = "/",
) = resource bind { r: Request ->
    Response(Status.OK)
        .header("content-type", "text/css; charset=UTF-8")
        .body(Classpath(root, muteWarning = true).load(resource.drop(1))!!.openStream())
}
