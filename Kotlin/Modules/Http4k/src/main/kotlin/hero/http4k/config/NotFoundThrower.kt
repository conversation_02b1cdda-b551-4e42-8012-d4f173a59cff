package hero.http4k.config

import hero.exceptions.http.NotFoundException
import org.http4k.core.Filter
import org.http4k.core.HttpHandler
import org.http4k.core.Request
import org.http4k.core.Status

// In case of no matching route the http4k router returns 404 right away.
// We catch it here and throw our custom exception so it can be later caught by the handleErrors Filter,
// which will transform it to our custom JSON error response.
fun notFoundThrower() =
    Filter { next: HttpHandler ->
        { request: Request ->
            val response = next(request)
            // If sending 404 manually (e.g. from OAuthController, this condition must not pass (bodyString will be non-empty).
            if (response.status == Status.NOT_FOUND && response.bodyString().isEmpty()) {
                throw NotFoundException(
                    response.bodyString().ifEmpty {
                        "${request.method} ${request.uri.path} was not found"
                    },
                    mapOf(),
                )
            }
            response
        }
    }
