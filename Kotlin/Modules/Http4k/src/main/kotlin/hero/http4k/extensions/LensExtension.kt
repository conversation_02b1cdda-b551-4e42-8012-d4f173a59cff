package hero.http4k.extensions

import hero.jackson.fromJson
import org.http4k.core.Response
import org.http4k.lens.BiDiLensSpec
import org.http4k.lens.BiDiMapping
import org.http4k.lens.map
import java.time.Year

inline fun <reified T : Any> Response.fromJson() = String(this.body.payload.array()).fromJson<T>()

fun <IN : Any> BiDiLensSpec<IN, String>.year() = map(BiDiMapping<String, Year>({ Year.parse(it) }, { it.toString() }))
