package hero.http4k.config

import hero.baseutils.FuelException
import hero.baseutils.log
import hero.exceptions.http.HttpStatusException
import hero.http4k.auth.parseJwtRelaxed
import hero.http4k.extensions.ErrorResponse
import hero.http4k.extensions.Response
import hero.http4k.extensions.WrappedLensFailure
import hero.http4k.extensions.userAgent
import hero.jwt.IMPERSONATION_TOKEN
import io.jsonwebtoken.JwtException
import io.sentry.Breadcrumb
import io.sentry.Sentry
import io.sentry.SentryLevel
import io.sentry.protocol.User
import org.http4k.core.Filter
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.core.cookie.cookie
import org.http4k.core.cookie.cookies
import org.http4k.core.then
import org.http4k.lens.LensFailure
import java.net.HttpURLConnection.HTTP_BAD_REQUEST
import java.net.HttpURLConnection.HTTP_INTERNAL_ERROR
import kotlin.random.Random
import io.sentry.protocol.Request as SentryRequest

fun WrappedLensFailure.toResponse() = Response(Status.BAD_REQUEST, ErrorResponse(message))

fun onRequest(
    production: Boolean,
    isLocalHost: Boolean,
): Filter = sentryFilter.then(logErrorFilter(production, isLocalHost))

/**
 * Logs all requests and catches all Throwables and translates them to appropriate Responses.
 * Note this method will be captured in logs, so it should be supersimple, eg.:
 *    2020-11-18 07:13:26.196 WARN  [] LoggingKt$onRequest$1$1:32
 */
private fun logErrorFilter(
    production: Boolean,
    isLocalHost: Boolean,
) = Filter { next ->
    { request ->
        val start = System.currentTimeMillis()
        val (response, parameters, exception) = try {
            Triple(next(request), mapOf(), null)
        } catch (le: LensFailure) {
            Triple(WrappedLensFailure(le).toResponse(), emptyMap(), WrappedLensFailure(le))
        } catch (wle: WrappedLensFailure) {
            Triple(wle.toResponse(), emptyMap(), wle)
        } catch (je: JwtException) {
            Triple(Response(Status.UNAUTHORIZED, ErrorResponse("Invalid JWT token.")), emptyMap(), je)
        } catch (hse: HttpStatusException) {
            Triple(Response(hse), hse.labels, hse)
        } catch (e: Throwable) {
            val errorMessage = if (production) {
                "Internal server error."
            } else {
                "${e.message} ${e.cause?.message ?: ""}"
            }
            Triple(Response(Status.INTERNAL_SERVER_ERROR, ErrorResponse(errorMessage)), emptyMap(), e)
        }

        val code = response.status.code
        val message = if (!isLocalHost) {
            ""
        } else {
            "$code Request in ${System.currentTimeMillis() - start} ms : ${request.method} ${request.uri}"
        }
        val enhancedParameters = parameters + buildMap {
            put("requestUri", request.uri)
            if (request.userAgent != null) {
                "userAgent" to request.userAgent
            }
        }

        if (isLocalHost) log.info(message, enhancedParameters)
        if (exception != null) {
            Sentry.configureScope {
                it.request = request.toSentryRequest()
                if (exception is FuelException) {
                    it.setContexts("Request curl", exception.cUrlString ?: "")
                }

                it.setContexts("Request raw body", String(request.body.payload.array()))
                it.setContexts(
                    "Additional info",
                    buildMap {
                        put("Is impersonation", request.cookie(IMPERSONATION_TOKEN) != null)
                    },
                )
                it.user = User().apply {
                    id = request.parseJwtRelaxed()?.subject
                    ipAddress = request.source?.address
                }
                if (code >= HTTP_INTERNAL_ERROR) {
                    it.level = SentryLevel.FATAL
                } else if (code >= HTTP_BAD_REQUEST) {
                    it.level = SentryLevel.WARNING
                }
            }

            if (shouldSendToSentry(response, production)) {
                Sentry.captureException(exception)
            }
            if (code >= HTTP_INTERNAL_ERROR) {
                log.fatal(exception.message, enhancedParameters, exception)
            } else {
                log.warn(exception.message, enhancedParameters)
            }
        }
        response
    }
}

private val sentryFilter = Filter { next ->
    { request ->
        Sentry.pushScope().use { _ ->
            Sentry.clearBreadcrumbs()
            Sentry.addBreadcrumb(Breadcrumb.http(request.uri.path, request.method.name))
            next(request)
        }
    }
}

private fun shouldSendToSentry(
    response: Response,
    isProduction: Boolean,
): Boolean =
    when {
        response.status.code >= HTTP_INTERNAL_ERROR -> {
            true
        }

        isProduction && response.status.code == HTTP_BAD_REQUEST -> {
            Random.nextInt(0, 1000) < 10
        }

        else -> false
    }

private fun Request.toSentryRequest(): SentryRequest {
    val sentryRequest = SentryRequest()
    sentryRequest.headers = headers.toMap()
    sentryRequest.url = uri.toString()
    sentryRequest.method = method.name
    sentryRequest.cookies = cookies().joinToString(";")
    sentryRequest.data = bodyString()

    return sentryRequest
}
