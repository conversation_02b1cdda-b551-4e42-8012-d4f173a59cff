package hero.http4k.config

import hero.exceptions.http.NotFoundException
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status.Companion.OK
import org.http4k.core.then
import org.http4k.routing.bind
import org.http4k.routing.routes
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class NotFoundThrowerTest {
    @Test
    fun notFoundResponseIsThrownAsNotFoundException() {
        val handler = notFoundThrower().then(
            routes("/foo" bind Method.GET to { Response(OK) }),
        )
        assertEquals(Response(OK), handler(Request(Method.GET, "/foo")))
        assertThrows<NotFoundException> {
            handler(Request(Method.GET, "/bar"))
        }
    }
}
