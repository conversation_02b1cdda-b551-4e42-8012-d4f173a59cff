package hero.sql

import hero.core.data.Sort
import org.jooq.Field

fun <T> Field<T>.cmp(
    value: T,
    sort: Sort,
) = when (sort.direction) {
    Sort.Direction.DESC -> this.lt(value)
    Sort.Direction.ASC -> this.gt(value)
}

fun <T> Field<T>.cmpBeforeCursor(
    value: T,
    sort: Sort,
) = when (sort.direction) {
    Sort.Direction.DESC -> this.gt(value)
    Sort.Direction.ASC -> this.lt(value)
}

fun <T> Field<T>.orderBy(sort: Sort) =
    when (sort.direction) {
        Sort.Direction.DESC -> this.desc().nullsLast()
        Sort.Direction.ASC -> this.asc().nullsLast()
    }

fun <T> Field<T>.orderByReversed(sort: Sort) =
    when (sort.direction) {
        Sort.Direction.DESC -> this.asc().nullsLast()
        Sort.Direction.ASC -> this.desc().nullsLast()
    }
