CREATE TABLE device
(
    device_id                   TEXT        NOT NULL,
    user_id                     TEXT        NOT NULL,
    app_version                 TEXT        NOT NULL,
    firebase_registration_token TEXT        NOT NULL,
    device_type                 TEXT        NOT NULL,
    created_at                  TIMESTAMPTZ NOT NULL,
    updated_at                  TIMESTAMPTZ NOT NULL,
    PRIMARY KEY (user_id, device_id)
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE device TO "<EMAIL>";
        END IF;
    END
$$;
