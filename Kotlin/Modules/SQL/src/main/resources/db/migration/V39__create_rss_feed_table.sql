CREATE TABLE rss_feed
(
    id                BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    user_id           TEXT        NOT NULL,
    type              TEXT        NOT NULL,
    xml               XML         NOT NULL,
    placeholder_token TEXT        NOT NULL,
    created_at        TIMESTAMPTZ NOT NULL,
    updated_at        TIMESTAMPTZ NOT NULL,

    CONSTRAINT "06323de0015c4a98a6e0f47b6b16cf5b_fk" FOREIGN KEY (user_id) REFERENCES "user" (id)
);

CREATE UNIQUE INDEX "8e585d2c1f4b40ef9830e5ae42feed7f" ON rss_feed (user_id, type);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE rss_feed TO "<EMAIL>";
        END IF;
    END
$$;
