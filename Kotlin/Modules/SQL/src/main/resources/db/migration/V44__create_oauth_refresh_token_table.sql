CREATE TABLE oauth_refresh_token
(
    id                    TEXT PRIMARY KEY,
    user_id               TEXT        NOT NULL,
    authorization_code_id TEXT        NOT NULL,
    created_at            TIMESTAMPTZ NOT NULL,
    refreshed_at          TIMESTAMPTZ NOT NULL,
    revoked_at            TIMESTAMPTZ NULL,

    CONSTRAINT "6783d81229184e6a800c9962a28a926f_fk" FOREIGN KEY (user_id) REFERENCES "user" (id),
    CONSTRAINT "a03e1cc299554e61b395403fe79dd7b1_fk" FOREIGN KEY (authorization_code_id) REFERENCES oauth_authorization_code (id)
);

CREATE UNIQUE INDEX "8fdbd4e3edb44fd39fde142275b19d71_ux" ON oauth_refresh_token (authorization_code_id);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE oauth_refresh_token TO "<EMAIL>";
        END IF;
    END
$$;
