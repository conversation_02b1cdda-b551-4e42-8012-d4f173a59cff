CREATE TABLE message_thread
(
    id              TEXT PRIMARY KEY,
    created_at      TIMESTAMPTZ NOT NULL,
    updated_at      TIMESTAMPTZ NOT NULL,
    last_message_by TEXT        NULL,
    last_message_at TIMESTAMPTZ NULL,
    last_message_id TEXT        NULL,
    posts_count     BIGINT      NOT NULL,
    email_notified  BOOLEAN     NOT NULL,

    CONSTRAINT "8564bf0f6af64921a2aa9d8d41715188_fk" FOREIGN KEY (last_message_id) REFERENCES post (id),
    CONSTRAINT "7ceb888a357042649e6596962cf67f86_fk" FOREIGN KEY (last_message_by) REFERENCES "user" (id)
);

CREATE TABLE message_thread_participant
(
    thread_id   TEXT        NOT NULL,
    user_id     TEXT        NOT NULL,
    seen_at     TIMESTAMPTZ NULL,
    checked_at  TIMESTAMPTZ NULL,
    archived_at TIMESTAMPTZ NULL,
    deleted_at  TIMESTAMPTZ NULL,
    is_deleted  <PERSON><PERSON><PERSON><PERSON><PERSON>     NOT NULL,

    <PERSON><PERSON><PERSON><PERSON> KEY (user_id, thread_id),
    CONSTRAINT "0feb3d70af2f434d832b4c5f315d62d9_fk" FOREIGN KEY (user_id) REFERENCES "user" (id),
    CONSTRAINT "4e400c32aa974e6396f507827fc7688e_fk" FOREIGN KEY (thread_id) REFERENCES message_thread (id)
);

CREATE INDEX "6631d312cdd14edfa19253997968be35_ix" ON message_thread_participant (thread_id);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE message_thread TO "<EMAIL>";
            GRANT ALL ON TABLE message_thread_participant TO "<EMAIL>";
        END IF;
    END
$$;
