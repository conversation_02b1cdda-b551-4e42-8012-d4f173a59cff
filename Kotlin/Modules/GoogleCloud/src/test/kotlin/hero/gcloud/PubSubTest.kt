package hero.gcloud

import hero.baseutils.SystemEnv
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PubSubTest {
    private val pubSub: PubSub = PubSub("test", SystemEnv.cloudProject)

    @Test
    fun `translate parent of regular classes to topic`() {
        assertEquals("test-String", pubSub.inferTopicName("Foo"))
        assertEquals("test-Long", pubSub.inferTopicName(1L))
    }

    @Test
    fun `translate parent of sealed classes to topic`() {
        assertEquals("test-Parent", pubSub.inferTopicName(Parent.Child1()))
        assertEquals("test-Parent", pubSub.inferTopicName(Parent.Child2()))
    }

    sealed class Parent {
        class Child1 : Parent()

        class Child2 : Parent()
    }
}
