package hero.gcloud

import com.google.api.core.ApiFuture
import com.google.cloud.firestore.DocumentReference
import com.google.cloud.firestore.DocumentSnapshot
import com.google.cloud.firestore.Transaction
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1

class TransactionDocument<R : Any> internal constructor(
    doc: DocumentReference,
    type: KClass<R>,
    private val transaction: Transaction,
) : Document<R>(doc, type) {
    override fun fetchSource(): ApiFuture<DocumentSnapshot> = transaction.get(doc)

    override fun set(value: R) {
        transaction.set(doc, value)
    }

    override fun <T, V> field(path: DocumentPathBuilder<R, T, V>): TransactionDocumentField<R, V> {
        val field = super.field(path)
        return TransactionDocumentField(field.doc, field.path, transaction = transaction)
    }

    override fun <T> field(property: KProperty1<R, T>): TransactionDocumentField<R, T> =
        TransactionDocumentField(doc, DocumentPath(DocumentRoot(type), property.name), transaction = transaction)
}

class TransactionDocumentField<R : Any, V> internal constructor(
    doc: DocumentReference,
    path: DocumentPath<R, V>,
    valueTransformer: (V) -> Any? = { it },
    private val transaction: Transaction,
) : DocumentField<R, V>(doc, path, valueTransformer) {
    override fun update(value: V) {
        transaction.update(doc, path.path, valueTransformer(value))
    }
}

fun <R : Any, V> DocumentField<R, V>.inTransaction(transaction: Transaction) =
    TransactionDocumentField(doc, path, valueTransformer, transaction)

fun <R : Any> Document<R>.inTransaction(transaction: Transaction) = TransactionDocument(doc, type, transaction)
