package hero.gcloud

import com.google.api.core.ApiFuture
import com.google.api.gax.rpc.DeadlineExceededException
import com.google.auth.oauth2.GoogleCredentials
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.DocumentReference
import com.google.cloud.firestore.DocumentSnapshot
import com.google.cloud.firestore.FieldValue
import com.google.cloud.firestore.Firestore
import com.google.cloud.firestore.Query
import com.google.cloud.firestore.QueryDocumentSnapshot
import com.google.cloud.firestore.Transaction
import com.google.cloud.firestore.WriteResult
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.cloud.FirestoreClient
import hero.baseutils.retryOn
import io.grpc.StatusRuntimeException
import kotlin.reflect.KMutableProperty1
import kotlin.reflect.KProperty1

fun firestore(projectId: String): Firestore =
    firestoreCache.computeIfAbsent(projectId) {
        FirebaseOptions
            .builder()
            .setProjectId(it)
            .setCredentials(GoogleCredentials.getApplicationDefault())
            .build()
            .run { FirebaseApp.initializeApp(this) }
            .run { FirestoreClient.getFirestore(this) }
    }

private val firestoreCache: MutableMap<String, Firestore> = mutableMapOf()

data class FirestoreRef(
    val firestore: Firestore,
    val isProduction: Boolean,
)

fun firestore(
    projectId: String,
    isProduction: Boolean,
): FirestoreRef = FirestoreRef(firestore(projectId), isProduction)

operator fun Firestore.get(collectionId: String): CollectionReference = this.collection(collectionId)

inline fun <reified T : Any> DocumentSnapshot.to(): T? =
    try {
        this.toObject(T::class.java)
    } catch (e: Exception) {
        throw IllegalArgumentException(
            "Cannot deserialize object ${T::class.simpleName} from ${this.data}: ${e.message}",
            e,
        )
    }

inline fun <reified T : Any> QueryDocumentSnapshot.to(): T =
    try {
        this.toObject(T::class.java)
    } catch (e: Exception) {
        throw IllegalArgumentException("Cannot deserialize object ${T::class.simpleName} from ${this.data}.", e)
    }

inline fun <reified T : Any> Query.fetchAll(): List<T> =
    retryOnDeadline { this.get().get().documents.map { it.to<T>() } }

inline fun <reified T : Any> DocumentReference.fetchSingle(): T? = retryOnDeadline { this.get().get()?.to<T>() }

inline fun <reified T : Any> Query.fetchSingle(): T? =
    retryOnDeadline { this.get().get().documents.firstOrNull()?.to<T>() }

inline fun <reified T : Any> ApiFuture<DocumentSnapshot>.fetchSingle(): T? = retryOnDeadline { this.get().to<T>() }

inline fun <reified T : Any> DocumentReference.fetchSingle(transaction: Transaction): T? =
    retryOnDeadline { transaction.get(this).get().to<T>() }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
fun DocumentReference.with(transaction: Transaction): ApiFuture<DocumentSnapshot> =
    retryOnDeadline { transaction.get(this) }

operator fun CollectionReference.get(documentId: String): DocumentReference =
    retryOnDeadline { this.document(documentId) }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
operator fun CollectionReference.set(
    documentId: String,
    value: Any,
): WriteResult = retryOnDeadline { this.document(documentId).set(value).get() }

@Deprecated("Implement this to TypedCollection", replaceWith = ReplaceWith(""))
/** Note this cast only works for simple entities. */
inline operator fun <reified I : Any, reified T : Any?> CollectionReference.get(
    documentId: String,
    field1: KMutableProperty1<I, T>,
): T? = retryOnDeadline { this.document(documentId).get()[field1.name] as T? }

@Deprecated("Implement this to TypedCollection", replaceWith = ReplaceWith(""))
/** Note this cast only works for simple entities. */
inline operator fun <reified I1 : Any, reified I2 : Any, reified T : Any?> CollectionReference.get(
    documentId: String,
    field1: KProperty1<I1, I2>,
    field2: KProperty1<I2, T>,
): T? = retryOnDeadline { this.document(documentId).get()["${field1.name}.${field2.name}"] as T? }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith("collection.doc(documentId).field(root(field1)).update(value)"),
)
operator fun <I : Any, T : Any?> CollectionReference.set(
    documentId: String,
    field1: KProperty1<I, T>,
    value: T,
): WriteResult = retryOnDeadline { this.document(documentId).update(field1.name, value).get() }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith("collection.doc(documentId).field(root(field1).path(field2)).update(value)"),
)
operator fun <I1 : Any, I2 : Any, T : Any?> CollectionReference.set(
    documentId: String,
    field1: KProperty1<I1, I2>,
    field2: KProperty1<I2, T>,
    value: T,
): WriteResult = retryOnDeadline { this.document(documentId).update("${field1.name}.${field2.name}", value).get() }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith("collection.doc(documentId).field(root(field1).path(field2)).update(value)"),
)
operator fun CollectionReference.set(
    documentId: String,
    field1: KProperty1<out Any, Any>,
    field2: KProperty1<out Any, Any?>,
    value: FieldValue,
): WriteResult = retryOnDeadline { this.document(documentId).update("${field1.name}.${field2.name}", value).get() }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
operator fun <I : Any, T : Any?> DocumentReference.set(
    transaction: Transaction,
    field: KProperty1<I, T>,
    value: T,
): Transaction = retryOnDeadline { transaction.update(this, field.name, value) }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
operator fun <T : Any> DocumentReference.set(
    transaction: Transaction,
    value: T,
): Transaction = retryOnDeadline { transaction.set(this, value) }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
operator fun <I1 : Any, I2 : Any, T : Any?> DocumentReference.set(
    transaction: Transaction,
    field1: KProperty1<I1, I2>,
    field2: KProperty1<I2, T>,
    value: T,
): Transaction = retryOnDeadline { transaction.update(this, "${field1.name}.${field2.name}", value) }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
operator fun DocumentReference.set(
    transaction: Transaction,
    field1: KProperty1<out Any, Any>,
    field2: KProperty1<out Any, Any?>,
    value: FieldValue,
): Transaction = retryOnDeadline { transaction.update(this, "${field1.name}.${field2.name}", value) }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
operator fun ApiFuture<DocumentSnapshot>.get(field: String): Any? = retryOnDeadline { this.get().get(field) }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
operator fun ApiFuture<DocumentSnapshot>.get(
    field1: String,
    field2: String,
): Any? = retryOnDeadline { this.get().get("$field1.$field2") }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
inline fun <reified T : Any> ApiFuture<DocumentSnapshot>.getField(field: KProperty1<Any, T?>): T? =
    retryOnDeadline { this[field.name] as T? }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
inline fun <reified I1 : Any, reified I2 : Any, reified T : Any?> ApiFuture<DocumentSnapshot>.getField(
    field1: KProperty1<I1, I2>,
    field2: KProperty1<I2, T>,
): T? = retryOnDeadline { this[field1.name, field2.name] as T? }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
inline operator fun <reified T : Any> ApiFuture<DocumentSnapshot>.get(field: KMutableProperty1<Any, T?>): T? =
    this.getField(field)

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
inline operator fun <reified I1 : Any, reified I2 : Any, reified T : Any?> ApiFuture<DocumentSnapshot>.get(
    field1: KProperty1<I1, I2>,
    field2: KProperty1<I2, T>,
) = this.getField(field1, field2)

@Deprecated(
    "Use type safe alternative",
    ReplaceWith(""),
)
fun Query.whereInEmpty(
    key: String,
    list: List<Any>,
): Query =
    if (list.isNotEmpty()) {
        whereIn(key, list)
    } else {
        // cannot query empty collection, we enforce empty result like this
        whereEqualTo(key, "EMPTY")
    }

@Deprecated(
    "Use type safe alternative",
    ReplaceWith("collection.doc(documentId).field(root(field)).update(value)"),
)
fun <I : Any?, V : Any?> CollectionReference.store(
    postId: String,
    field: KMutableProperty1<I, V>,
    value: V,
) {
    retryOnDeadline { this[postId].update(field.name, value) }
}

@Deprecated(
    "Use type safe alternative",
    ReplaceWith("collection.doc(documentId).field(root(field1).path(field2)).update(value)"),
)
fun <I : Any?, V : Any?> CollectionReference.store(
    postId: String,
    // TODO make type-safe as well
    field1: KProperty1<*, *>,
    field2: KProperty1<I, V>,
    value: V?,
) {
    retryOnDeadline { this[postId].update("${field1.name}.${field2.name}", value) }
}

fun <T> retryOnDeadline(block: () -> T): T =
    retryOn(DeadlineExceededException::class, StatusRuntimeException::class) { block() }
