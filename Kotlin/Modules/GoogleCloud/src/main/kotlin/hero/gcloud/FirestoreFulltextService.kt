package hero.gcloud

import com.google.cloud.firestore.Query
import hero.baseutils.prefixes
import hero.baseutils.toAscii
import hero.baseutils.tokenize
import hero.core.annotation.NoArg
import hero.core.data.EntityCollection

/**
 * This is a temporary solution for fulltext indexing not (yet) supported by Firestore.
 * In case firestore supports fulltext search, this service should be completely removed.
 * Once we run in production, this service should be moved out to separate Cloud Run service
 * and be called via endpoints.
 */
class FirestoreFulltextService(private val fulltextDataSource: TypedCollectionReference<FulltextIndex>) {
    fun index(
        id: String,
        texts: List<String>,
        explicit: Boolean,
        subscribers: Int,
    ) {
        fulltextDataSource[id].set(
            FulltextIndex(
                id = id,
                tokens = texts.flatMap { it.tokenize().prefixes() },
                weight = subscribers,
            ),
        )
    }

    fun search(
        query: String,
        preferredIds: List<String>?,
        offset: Int?,
        limit: Int?,
    ): List<String> =
        fulltextDataSource
            // the fulltext index does not contain any spaces, we need to remove them
            .where(FulltextIndex::tokens)
            // TODO this seems a bit off, maybe .removeNonAscii?
            .contains(query.toAscii().lowercase().replace("[^0-9a-z]+".toRegex(), "").toAscii())
            .run { if (preferredIds != null) and(FulltextIndex::id).isIn(preferredIds) else this }
            .run { if (offset != null) offset(offset) else this }
            .run { if (limit != null) limit(limit) else this }
            .orderBy(FulltextIndex::weight, Query.Direction.DESCENDING)
            .fetchAll()
            .map { it.id }
}

@NoArg
data class FulltextIndex(
    val id: String,
    val tokens: List<String>,
    val weight: Int,
) {
    companion object : EntityCollection<FulltextIndex> {
        override val collectionName: String = "users-fulltext"
    }
}
