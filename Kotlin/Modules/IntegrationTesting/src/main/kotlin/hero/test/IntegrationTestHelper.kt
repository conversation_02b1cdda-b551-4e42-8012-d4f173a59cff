package hero.test

import hero.baseutils.EnvironmentVariables
import hero.baseutils.md5nice
import hero.baseutils.plusDays
import hero.baseutils.truncated
import hero.gcloud.TypedCollectionReference
import hero.gcloud.fetchAll
import hero.model.CancelledByRole
import hero.model.Category
import hero.model.CouponMethod
import hero.model.Creator
import hero.model.CreatorSubscribersStats
import hero.model.Currency
import hero.model.CustomerIds
import hero.model.DiscordMeta
import hero.model.GjirafaLivestreamMeta
import hero.model.ImageAsset
import hero.model.Invoice
import hero.model.MessageThread
import hero.model.Notification
import hero.model.NotificationType
import hero.model.Path
import hero.model.Post
import hero.model.PostAsset
import hero.model.PostCounts
import hero.model.PostPayment
import hero.model.Role
import hero.model.SavedPost
import hero.model.Session
import hero.model.SignInProvider
import hero.model.SpotifyMeta
import hero.model.StorageEntityType
import hero.model.SubscribeRequest
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriberType
import hero.model.SupportCounts
import hero.model.User
import hero.model.UserCompany
import hero.model.UserStatus
import hero.model.UserStore
import hero.model.UserStoreAttributes
import hero.model.UserSubscriptionsStats
import hero.model.topics.PostState
import hero.repository.notification.NotificationRepository
import hero.repository.post.PostRepository
import hero.repository.session.SessionRepository
import hero.repository.subscription.SubscribeRequestRepository
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.DAILY_POST_VIEW_STATISTICS
import hero.sql.jooq.Tables.WATCH_ACTIVITY
import hero.sql.jooq.tables.records.DailyPostViewStatisticsRecord
import hero.sql.jooq.tables.records.OauthAuthorizationCodeRecord
import hero.sql.jooq.tables.records.OauthClientRecord
import hero.sql.jooq.tables.records.SubscriptionRecord
import hero.sql.jooq.tables.records.WatchActivityRecord
import hero.test.gcloud.FirestoreTestDatabase
import hero.test.logging.TestLogger
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

class IntegrationTestHelper {
    fun createMessageThread(
        userId: String,
        participants: List<String>,
        lastMessageAt: Instant? = null,
        archivedFor: List<String> = listOf(),
        deletedFor: List<String> = listOf(),
        deletes: Map<String, Instant> = mapOf(),
    ) = MessageThread(
        userIds = participants + userId,
        createdAt = Instant.now().truncated(),
        seens = mapOf(userId to Instant.now().truncated()),
        checks = mapOf(userId to Instant.now().truncated()),
        archivedFor = archivedFor,
        deletedFor = deletedFor,
        deletes = deletes,
        lastMessageAt = lastMessageAt?.truncated(),
        activeFor = (participants + userId) - archivedFor.toSet() - deletedFor.toSet(),
    ).apply {
        messageThreadsCollection[this.id].set(this)
    }

    fun createPost(
        userId: String,
        parentUserId: String? = null,
        text: String = "Post text",
        textHtml: String = text,
        id: String = UUID.randomUUID().toString(),
        parentId: String? = null,
        siblingId: String? = null,
        state: PostState = PostState.PUBLISHED,
        messageThreadId: String? = null,
        assets: List<PostAsset> = listOf(),
        publishedAt: Instant = Instant.now(),
        pinnedAt: Instant? = null,
        price: Long? = null,
        categories: List<String> = listOf(),
        counts: PostCounts = PostCounts(),
        excludedFromRss: Boolean = false,
        views: Long = 0,
        createdAt: Instant = Instant.now().truncated(),
        updatedAt: Instant = Instant.now().truncated(),
    ) = Post(
        id = id,
        text = text,
        textHtml = textHtml,
        state = state,
        userId = userId,
        parentId = parentId,
        siblingId = siblingId,
        parentUserId = parentUserId,
        excludeFromRss = excludedFromRss,
        published = publishedAt.truncated(),
        pinnedAt = pinnedAt?.truncated(),
        messageThreadId = messageThreadId,
        assets = assets,
        assetIds = assets.mapNotNull { it.gjirafa?.id ?: it.gjirafaLive?.id },
        price = price,
        views = views,
        counts = counts,
        categories = categories,
        created = createdAt,
        updated = updatedAt,
    ).apply {
        postsCollection[id].set(this)
        if (internalTestContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq(userId)).fetchOne() == null) {
            createUser(userId)
        }
        TestRepositories.postRepository.save(this)
    }

    fun createWatchActivity(
        post: Post,
        assetId: String,
        userId: String,
        timestamp: Double = 420.0,
        finished: Boolean = false,
        sessionId: String = "session-id",
        watchedAt: Instant = Instant.now(),
        subscriptionActive: Boolean = true,
    ) {
        val record = WatchActivityRecord().apply {
            this.postId = post.id
            this.creatorId = post.userId
            this.assetId = assetId
            this.userId = userId
            this.sessionId = sessionId
            this.timestamp = timestamp
            this.createdAt = Instant.now().truncated()
            this.watchedAt = watchedAt.truncated()
            this.finished = finished
            this.deletedAt = null
            this.subscriptionActive = subscriptionActive
        }

        internalTestContext
            .insertInto(WATCH_ACTIVITY)
            .set(record)
            .execute()
    }

    fun createSavedPost(
        userId: String,
        post: Post,
        savedAt: Instant = Instant.now(),
        active: Boolean = true,
    ) = SavedPost(
        postId = post.id,
        creatorId = post.userId,
        userId = userId,
        subscriptionActive = active,
        savedAt = savedAt.truncated(),
        postPublishedAt = post.published,
    ).apply {
        savedPostsCollection[id].set(this)
    }

    fun createDailyPostViewsStats(
        post: Post,
        views: Int = 100,
        date: LocalDate = LocalDate.now(),
    ) {
        val record = DailyPostViewStatisticsRecord()
            .apply {
                this.postId = post.id
                this.creatorId = post.userId
                this.views = views
                this.date = date
            }

        internalTestContext
            .insertInto(DAILY_POST_VIEW_STATISTICS)
            .set(record)
            .execute()
    }

    fun createNotification(
        userId: String,
        type: NotificationType = NotificationType.NEW_POST,
        actorIds: List<String> = listOf(userId),
        seenAt: Instant? = null,
        checkedAt: Instant? = null,
        createdAt: Instant = Instant.now(),
        objectId: String? = null,
        objectType: StorageEntityType = StorageEntityType.POST,
        id: String = "$userId-${createdAt.epochSecond}",
    ) = Notification(
        userId = userId,
        type = type,
        actorIds = actorIds,
        seenAt = seenAt?.truncated(),
        checkedAt = checkedAt?.truncated(),
        created = createdAt.truncated(),
        timestamp = createdAt.truncated(),
        objectId = objectId,
        objectType = objectType,
        id = id,
    ).apply {
        notificationsCollection[id].set(this)
        TestRepositories.notificationRepository.save(this)
    }

    fun createPostPayment(
        userId: String,
        postId: String,
    ) = PostPayment(
        userId,
        postId,
        Instant.now().truncated(),
    ).apply {
        postPaymentsCollection[id].set(this)
    }

    fun createSubscriber(
        creatorId: String,
        userId: String,
        tierId: String = "EUR10",
        subscribedAt: Instant = Instant.now(),
        expiresAt: Instant? = null,
        status: SubscriberStatus = SubscriberStatus.ACTIVE,
        couponAppliedForMonths: Long? = null,
        cancelAt: Instant? = null,
        cancelAtPeriodEnd: Boolean = false,
        refused: Boolean = false,
        refunded: Boolean = false,
        cancelledByRole: CancelledByRole? = null,
    ) = Subscriber(
        userId = userId,
        creatorId = creatorId,
        tierId = tierId,
        expires = expiresAt,
        subscribed = subscribedAt.truncated(),
        subscriberType = SubscriberType.STRIPE,
        status = status,
        couponAppliedForMonths = couponAppliedForMonths,
        couponMethod = CouponMethod.VOUCHER,
        cancelAt = cancelAt?.truncated(),
        cancelAtPeriodEnd = cancelAtPeriodEnd,
        refused = refused,
        refunded = refunded,
        cancelledByRole = cancelledByRole,
    ).apply {
        subscribersCollection[this.id].set(this)
    }

    fun createUser(
        // note that id must be [a-z-]+
        id: String = "test-account-" + UUID.randomUUID().toString().md5nice(),
        currency: Currency = Currency.EUR,
        counts: SupportCounts = SupportCounts(),
        stripeAccountId: String? = null,
        role: Role = Role.USER,
        status: UserStatus = UserStatus.ACTIVE,
        tierId: String = "${currency}05",
        email: String? = "<EMAIL>",
        path: String = id,
        pathChangedAt: Instant = Instant.ofEpochSecond(0),
        companyCountry: String = "CZ",
        language: String = "en",
        lastPostAt: Instant? = null,
        permissions: Int = 0,
        name: String = "test-herohero-$currency-${System.currentTimeMillis()}",
        isOfAge: Boolean = false,
        featuredLanguages: List<String> = listOf(),
        bio: String = "bio",
        bioEn: String = "bio-en",
        bioHtml: String = "bio-html",
        bioHtmEn: String = "bio-htm-eng",
        firebaseId: String? = "firebaseId",
        facebookId: String? = "facebookId",
        googleId: String? = "googleId",
        airtableId: String? = "airtableId",
        isExplicit: Boolean = false,
        isFeatured: Boolean = false,
        hasLivestream: Boolean = false,
        hasSpotifyExport: Boolean = false,
        hasDrm: Boolean = false,
        hasRssFeed: Boolean = false,
        image: ImageAsset? = null,
        customerIds: CustomerIds = mutableMapOf(),
        discord: DiscordMeta? = null,
        spotify: SpotifyMeta? = null,
        verifiedAt: Instant? = null,
        gjirafaLivestream: GjirafaLivestreamMeta? = null,
        stripeAccountCreatedAt: Instant? = null,
        stripeAccountSuggestionSentAt: Instant? = null,
        stripeAccountActive: Boolean? = null,
        createdAt: Instant = Instant.now(),
    ) = User(
        id = id,
        name = name,
        path = path,
        pathChanged = pathChangedAt,
        created = createdAt.truncated(),
        email = email,
        counts = counts,
        language = language,
        moderatorPermissions = permissions,
        discord = discord,
        spotify = spotify,
        gjirafaLivestream = gjirafaLivestream,
        featuredBy = featuredLanguages,
        firebaseId = firebaseId,
        facebookId = facebookId,
        googleId = googleId,
        airTableId = airtableId,
        explicit = isExplicit,
        featured = isFeatured,
        hasLivestreams = hasLivestream,
        hasSpotifyExport = hasSpotifyExport,
        lastPostAt = lastPostAt,
        hasDrm = hasDrm,
        hasRssFeed = hasRssFeed,
        bio = bio,
        bioEn = bioEn,
        bioHtml = bioHtml,
        bioHtmlEn = bioHtmEn,
        company = UserCompany(
            namePublic = "$id project",
            name = "$id company",
            firstName = "owner first name",
            lastName = "owner last name",
            id = id,
            country = companyCountry,
            birthDate = "2001-10-10",
        ),
        image = image,
        role = role,
        status = status,
        isOfAge = isOfAge,
        verifiedAt = verifiedAt,
        creator = if (stripeAccountId == null) {
            Creator(tierId = tierId)
        } else {
            Creator(
                tierId = tierId,
                stripeAccountActive = stripeAccountActive ?: true,
                stripeAccountCreatedAt = stripeAccountCreatedAt,
                stripeAccountOnboarded = true,
                stripeAccountSuggestionSentAt = stripeAccountSuggestionSentAt,
                stripeAccountId = stripeAccountId,
                suspended = false,
                currency = currency,
            )
        },
        customerIds = customerIds,
    ).apply {
        usersCollection[id].set(this)
        TestRepositories.userRepository.save(this)
    }

    fun createUserMediaStore(
        userId: String,
        content: Map<String, Double> = emptyMap(),
    ) {
        UserStore(
            id = "media",
            attributes = UserStoreAttributes(content),
        ).apply {
            userStoresCollection["$userId-media"].set(this)
        }
    }

    fun createSession(
        userId: String,
        id: String = UUID.randomUUID().toString(),
        userAgent: String = "Mozilla 5/0",
        createdAt: Instant = Instant.now(),
        refreshedAt: Instant = Instant.now(),
        revoked: Boolean = false,
        deviceId: String? = UUID.randomUUID().toString(),
        signInProvider: SignInProvider = SignInProvider.GOOGLE,
        signInLocation: String = "Prague, Prague, Czechia",
    ) = Session(
        userId = userId,
        id = id,
        userAgent = userAgent,
        createdAt = createdAt.truncated(),
        refreshedAt = refreshedAt.truncated(),
        revoked = revoked,
        deviceId = deviceId,
        signInProvider = signInProvider,
        ipAddress = "*************",
        signInLocation = signInLocation,
    ).apply {
        TestRepositories.sessionRepository.save(this)
    }

    fun createPath(
        path: String,
        userId: String,
        abandoned: Instant? = null,
    ) = Path(
        id = path,
        userId = userId,
        created = Instant.now().truncated(),
        abandoned = abandoned,
    ).apply {
        pathsCollection[id].set(this)
    }

    fun createCategory(
        creatorId: String,
        name: String = "category-name",
        slug: String = "slug",
        id: String = UUID.randomUUID().toString(),
    ) = Category(
        id = id,
        name = name,
        slug = slug,
        userId = creatorId,
        createdAt = Instant.now().truncated(),
    ).apply {
        categoriesCollection[id].set(this)
    }

    fun createSubscriptionStats(
        id: String,
        stats: Map<String, String>,
    ) = UserSubscriptionsStats(stats.toMutableMap())
        .apply {
            subscriptionStatsCollection[id].set(this)
        }

    fun createSubscriberStats(
        id: String,
        stats: Map<String, String>,
    ) = CreatorSubscribersStats(stats.toMutableMap())
        .apply { subscriberStatsCollection[id].set(this) }

    fun createInvoice(
        timestamp: Instant = Instant.now(),
        payoutId: String = "po_123456789",
        invoiceId: String,
    ): Invoice =
        Invoice(
            timestamp = timestamp,
            userId = "creatorId",
            stripeAccountId = "acc_123456789",
            stripePayoutId = payoutId,
            total = 10_00L,
            total4D = 10_0000L,
            currency = Currency.EUR,
            currencyInvoice = Currency.EUR,
            countryOfDestination = "CZ",
            items = listOf(),
            invoiceId = invoiceId,
            issuingCompany = UserCompany(),
            invoicedCompany = UserCompany(),
            eurConversionRateCents = null,
            euReverseCharged = false,
            sheetReportLink = null,
        )

    /**
     * Creates a row in subscription table in Postgres database
     */
    fun createSubscription(
        userId: String,
        creatorId: String,
        id: String = UUID.randomUUID().toString(),
        createdAt: Instant = Instant.now(),
        status: SubscriberStatus = SubscriberStatus.ACTIVE,
    ) {
        val now = Instant.now().truncated()
        val subscriptionRecord = SubscriptionRecord().apply {
            stripeId = id
            this.userId = userId
            this.creatorId = creatorId
            customerId = "customer-id"
            startedAt = now
            endsAt = null
            this.status = status.name.lowercase()
            currency = "EUR"
            tierId = "EUR05"
            priceCents = 500
            this.createdAt = createdAt.truncated()
            updatedAt = now
            endsAt = now.plusDays(10)
        }

        internalTestContext
            .insertInto(Tables.SUBSCRIPTION)
            .set(subscriptionRecord)
            .execute()
    }

    fun createSubscribeRequest(
        userId: String,
        creatorId: String,
        createdAt: Instant = Instant.now(),
        acceptedAt: Instant? = null,
        declinedAt: Instant? = null,
        deletedAt: Instant? = null,
        id: Long = 0,
    ): SubscribeRequest {
        val subscribeRequest = SubscribeRequest(
            id = id,
            userId = userId,
            creatorId = creatorId,
            createdAt = createdAt.truncated(),
            acceptedAt = acceptedAt?.truncated(),
            declinedAt = declinedAt?.truncated(),
            deletedAt = deletedAt?.truncated(),
        )

        return TestRepositories.subscribeRequestRepository.save(subscribeRequest)
    }

    fun createOAuthClient(
        id: UUID = UUID.randomUUID(),
        name: String = "OAuth Client",
        userId: String? = null,
        redirectUris: List<String> = listOf(),
        secret: String = "SECRET",
        deletedAt: Instant? = null,
        disabledAt: Instant? = null,
    ) {
        val now = Instant.now().truncated()
        internalTestContext
            .insertInto(Tables.OAUTH_CLIENT)
            .set(
                OauthClientRecord().apply {
                    this.id = id
                    this.name = name
                    this.createdAt = now
                    this.updatedAt = now
                    this.redirectUris = redirectUris.toTypedArray()
                    this.secret = secret
                    this.userId = userId ?: createUser().id
                    this.deletedAt = deletedAt
                    this.disabledAt = disabledAt
                },
            )
            .execute()
    }

    fun createOAuthAuthorizationCode(
        oauthClientId: String,
        userId: String,
        id: String = UUID.randomUUID().toString(),
        usedAt: Instant? = null,
        redirectUri: String = "redirect-uri",
        createdAt: Instant? = null,
    ) {
        val now = Instant.now().truncated()

        internalTestContext
            .insertInto(Tables.OAUTH_AUTHORIZATION_CODE)
            .set(
                OauthAuthorizationCodeRecord().apply {
                    this.id = id
                    this.clientId = UUID.fromString(oauthClientId)
                    this.clientId = clientId
                    this.userId = userId
                    this.redirectUri = redirectUri
                    this.state = "state"
                    this.scopes = arrayOf()
                    this.responseType = "Code"
                    this.responseMode = null
                    this.codeChallenge = null
                    this.createdAt = createdAt ?: now
                    this.updatedAt = now
                    this.usedAt = usedAt
                },
            )
            .execute()
    }

    fun cleanup() {
        usersCollection.fetchAll()
            .forEach { usersCollection[it.id].delete() }

        userStoresCollectionInternal.get().get().documents.forEach {
            it.reference.delete()
        }

        pathsCollection.fetchAll()
            .forEach { pathsCollection[it.id].delete() }

        categoriesCollection.fetchAll()
            .forEach { categoriesCollection[it.id].delete() }

        messageThreadsCollection.fetchAll()
            .forEach { messageThreadsCollection[it.id].delete() }

        postsCollection.fetchAll()
            .forEach { postsCollection[it.id].delete() }

        savedPostsCollection.fetchAll()
            .forEach { savedPostsCollection[it.id].delete() }

        subscriptionStatsCollectionInternal.get().get().documents.forEach {
            it.reference.delete()
        }

        subscriberStatsCollectionInternal.get().get().documents.forEach {
            it.reference.delete()
        }

        notificationsCollection.fetchAll()
            .forEach { notificationsCollection[it.id].delete() }

        subscribersCollection.fetchAll()
            .forEach { subscribersCollection[it.id].delete() }
    }

    companion object TestCollections {
        val messageThreadsCollection = TypedCollectionReference<MessageThread>(FirestoreTestDatabase.testCollection())
        val postsCollection = TypedCollectionReference<Post>(FirestoreTestDatabase.testCollection())
        val savedPostsCollection = TypedCollectionReference<SavedPost>(FirestoreTestDatabase.testCollection())
        val postPaymentsCollection = TypedCollectionReference<PostPayment>(FirestoreTestDatabase.testCollection())
        val pathsCollection = TypedCollectionReference<Path>(FirestoreTestDatabase.testCollection())
        private val userStoresCollectionInternal = FirestoreTestDatabase.testCollection()
        val userStoresCollection = TypedCollectionReference<UserStore>(userStoresCollectionInternal)

        private val subscriptionStatsCollectionInternal = FirestoreTestDatabase.testCollection()
        val subscriptionStatsCollection = TypedCollectionReference<UserSubscriptionsStats>(
            subscriptionStatsCollectionInternal,
        )

        private val subscriberStatsCollectionInternal = FirestoreTestDatabase.testCollection()
        val subscriberStatsCollection = TypedCollectionReference<CreatorSubscribersStats>(
            subscriberStatsCollectionInternal,
        )

        val usersCollection = TypedCollectionReference<User>(FirestoreTestDatabase.testCollection())
        val categoriesCollection = TypedCollectionReference<Category>(FirestoreTestDatabase.testCollection())
        val subscribersCollection = TypedCollectionReference<Subscriber>(FirestoreTestDatabase.testCollection())
        val notificationsCollection = TypedCollectionReference<Notification>(FirestoreTestDatabase.testCollection())
    }
}

object TestRepositories {
    val sessionRepository = SessionRepository(internalTestContext)
    val postRepository = PostRepository(internalTestContext, TestLogger)
    val userRepository = UserRepository(internalTestContext)
    val notificationRepository = NotificationRepository(internalTestContext)
    val subscribeRequestRepository = SubscribeRequestRepository(internalTestContext)
}

object TestEnvironmentVariables : EnvironmentVariables {
    override val hostname: String = "test-hostname"
    override val hostnameServices: String = "test-hostname-services"
    override val cloudProject: String = "test-cloud-project"
    override val isProduction: Boolean = false
    override val environment: String = "test"
}

fun environmentVariables(
    hostname: String? = null,
    hostnameServices: String? = null,
    cloudProject: String? = null,
    isProduction: Boolean? = null,
    environment: String? = null,
): EnvironmentVariables =
    object : EnvironmentVariables {
        override val hostname = hostname ?: TestEnvironmentVariables.hostname
        override val hostnameServices = hostnameServices ?: TestEnvironmentVariables.hostnameServices
        override val cloudProject = cloudProject ?: TestEnvironmentVariables.cloudProject
        override val isProduction = isProduction ?: TestEnvironmentVariables.isProduction
        override val environment = environment ?: TestEnvironmentVariables.environment
    }
