package hero.stripe.service

import com.stripe.StripeClient
import hero.baseutils.SystemEnv
import hero.model.Currency

class StripeClients(
    keysEu: String,
    keysUs: String,
    production: Boolean = SystemEnv.isProduction,
) {
    val keyEu: String = keysEu
    // TODO for now we use EU client for both currencies in production
    val keyUs: String = if (production) keysEu else keysUs
    private val stripeClientEu: StripeClient = StripeClient(keyEu)
    private val stripeClientUs: StripeClient = StripeClient(keyUs)
    private val stripeClientUsNew: StripeClient = StripeClient(keysUs)

    operator fun get(currency: Currency): StripeClient =
        when (currency) {
            Currency.EUR -> stripeClientEu
            // TODO for now we use EU client for both currencies in production, see above
            Currency.USD -> stripeClientUs
            // WARN abusing PLN to access the new future Stripe client
            Currency.PLN -> stripeClientUsNew
            else -> error("Unsupported currency: $currency")
        }

    operator fun get(currency: String): StripeClient =
        when (currency.lowercase()) {
            "eur" -> stripeClientEu
            // TODO for now we use EU client for both currencies in production, see above
            "usd" -> stripeClientUs
            else -> error("Unsupported currency: $currency")
        }
}
