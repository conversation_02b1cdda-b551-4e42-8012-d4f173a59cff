package hero.stripe.model

import com.fasterxml.jackson.annotation.JsonProperty

/** See: https://stripe.com/docs/api/setup_intents/object#setup_intent_object-status */
@Suppress("unused")
enum class SetupIntentStatus {
    @JsonProperty("requires_payment_method")
    REQUIRES_PAYMENT_METHOD,
    @JsonProperty("requires_confirmation")
    REQUIRES_CONFIRMATION,
    @JsonProperty("requires_action")
    REQUIRES_ACTION,
    @JsonProperty("processing")
    PROCESSING,
    @JsonProperty("canceled")
    CANCELLED,
    @JsonProperty("succeeded")
    SUCCEEDED,
}

/** See: https://stripe.com/docs/declines/codes */
@Suppress("unused")
enum class StripeDeclineCode {
    @JsonProperty("authentication_required")
    AUTHENTICATION_REQUIRED,
    @JsonProperty("approve_with_id")
    APPROVE_WITH_ID,
    @JsonProperty("call_issuer")
    CALL_ISSUER,
    @JsonProperty("card_not_supported")
    CARD_NOT_SUPPORTED,
    @JsonProperty("card_velocity_exceeded")
    CARD_VELOCITY_EXCEEDED,
    @JsonProperty("currency_not_supported")
    CURRENCY_NOT_SUPPORTED,
    @JsonProperty("do_not_honor")
    DO_NOT_HONOR,
    @JsonProperty("do_not_try_again")
    DO_NOT_TRY_AGAIN,
    @JsonProperty("duplicate_transaction")
    DUPLICATE_TRANSACTION,
    @JsonProperty("expired_card")
    EXPIRED_CARD,
    @JsonProperty("fraudulent")
    FRAUDULENT,
    @JsonProperty("generic_decline")
    GENERIC_DECLINE,
    @JsonProperty("incorrect_cvc")
    INCORRECT_CVC,
    @JsonProperty("incorrect_number")
    INCORRECT_NUMBER,
    @JsonProperty("incorrect_pin")
    INCORRECT_PIN,
    @JsonProperty("insufficient_funds")
    INSUFFICIENT_FUNDS,
    @JsonProperty("invalid_account")
    INVALID_ACCOUNT,
    @JsonProperty("invalid_amount")
    INVALID_AMOUNT,
    @JsonProperty("invalid_cvc")
    INVALID_CVC,
    @JsonProperty("invalid_expiry_date")
    INVALID_EXPIRY_DATE,
    @JsonProperty("invalid_number")
    INVALID_NUMBER,
    @JsonProperty("issuer_not_available")
    ISSUER_NOT_AVAILABLE,
    @JsonProperty("lost_card")
    LOST_CARD,
    @JsonProperty("merchant_blacklist")
    MERCHANT_BLACKLIST,
    @JsonProperty("new_account_information_available")
    NEW_ACCOUNT_INFORMATION_AVAILABLE,
    @JsonProperty("no_action_taken")
    NO_ACTION_TAKEN,
    @JsonProperty("not_permitted")
    NOT_PERMITTED,
    @JsonProperty("pickup_card")
    PICKUP_CARD,
    @JsonProperty("pin_try_exceeded")
    PIN_TRY_EXCEEDED,
    @JsonProperty("processing_error")
    PROCESSING_ERROR,
    @JsonProperty("reenter_transaction")
    REENTER_TRANSACTION,
    @JsonProperty("restricted_card")
    RESTRICTED_CARD,
    @JsonProperty("revocation_of_all_authorizations")
    REVOCATION_OF_ALL_AUTHORIZATIONS,
    @JsonProperty("revocation_of_authorization")
    REVOCATION_OF_AUTHORIZATION,
    @JsonProperty("security_violation")
    SECURITY_VIOLATION,
    @JsonProperty("service_not_allowed")
    SERVICE_NOT_ALLOWED,
    @JsonProperty("stolen_card")
    STOLEN_CARD,
    @JsonProperty("stop_payment_order")
    STOP_PAYMENT_ORDER,
    @JsonProperty("testmode_decline")
    TESTMODE_DECLINE,
    @JsonProperty("transaction_not_allowed")
    TRANSACTION_NOT_ALLOWED,
    @JsonProperty("try_again_later")
    TRY_AGAIN_LATER,
    @JsonProperty("withdrawal_count_limit_exceeded")
    WITHDRAWAL_COUNT_LIMIT_EXCEEDED,
}

/** See: https://stripe.com/docs/error-codes */
@Suppress("unused")
enum class StripeErrorCode {
    @JsonProperty("account_already_exists")
    ACCOUNT_ALREADY_EXISTS,
    @JsonProperty("account_country_invalid_address")
    ACCOUNT_COUNTRY_INVALID_ADDRESS,
    @JsonProperty("account_invalid")
    ACCOUNT_INVALID,
    @JsonProperty("account_number_invalid")
    ACCOUNT_NUMBER_INVALID,
    @JsonProperty("alipay_upgrade_required")
    ALIPAY_UPGRADE_REQUIRED,
    @JsonProperty("amount_too_small")
    AMOUNT_TOO_SMALL,
    @JsonProperty("api_key_expired")
    API_KEY_EXPIRED,
    @JsonProperty("authentication_required")
    AUTHENTICATION_REQUIRED,
    @JsonProperty("balance_insufficient")
    BALANCE_INSUFFICIENT,
    @JsonProperty("bank_account_declined")
    BANK_ACCOUNT_DECLINED,
    @JsonProperty("bank_account_exists")
    BANK_ACCOUNT_EXISTS,
    @JsonProperty("bank_account_unusable")
    BANK_ACCOUNT_UNUSABLE,
    @JsonProperty("bank_account_unverified")
    BANK_ACCOUNT_UNVERIFIED,
    @JsonProperty("bitcoin_upgrade_required")
    BITCOIN_UPGRADE_REQUIRED,
    @JsonProperty("card_declined")
    CARD_DECLINED,
    @JsonProperty("charge_already_captured")
    CHARGE_ALREADY_CAPTURED,
    @JsonProperty("charge_already_refunded")
    CHARGE_ALREADY_REFUNDED,
    @JsonProperty("charge_disputed")
    CHARGE_DISPUTED,
    @JsonProperty("charge_expired_for_capture")
    CHARGE_EXPIRED_FOR_CAPTURE,
    @JsonProperty("country_unsupported")
    COUNTRY_UNSUPPORTED,
    @JsonProperty("coupon_expired")
    COUPON_EXPIRED,
    @JsonProperty("customer_max_subscriptions")
    CUSTOMER_MAX_SUBSCRIPTIONS,
    @JsonProperty("email_invalid")
    EMAIL_INVALID,
    @JsonProperty("expired_card")
    EXPIRED_CARD,
    @JsonProperty("idempotency_key_in_use")
    IDEMPOTENCY_KEY_IN_USE,
    @JsonProperty("incorrect_address")
    INCORRECT_ADDRESS,
    @JsonProperty("incorrect_cvc")
    INCORRECT_CVC,
    @JsonProperty("incorrect_number")
    INCORRECT_NUMBER,
    @JsonProperty("incorrect_zip")
    INCORRECT_ZIP,
    @JsonProperty("instant_payouts_unsupported")
    INSTANT_PAYOUTS_UNSUPPORTED,
    @JsonProperty("invalid_card_type")
    INVALID_CARD_TYPE,
    @JsonProperty("invalid_charge_amount")
    INVALID_CHARGE_AMOUNT,
    @JsonProperty("invalid_cvc")
    INVALID_CVC,
    @JsonProperty("invalid_expiry_month")
    INVALID_EXPIRY_MONTH,
    @JsonProperty("invalid_expiry_year")
    INVALID_EXPIRY_YEAR,
    @JsonProperty("invalid_number")
    INVALID_NUMBER,
    @JsonProperty("invalid_source_usage")
    INVALID_SOURCE_USAGE,
    @JsonProperty("invoice_no_customer_line_items")
    INVOICE_NO_CUSTOMER_LINE_ITEMS,
    @JsonProperty("invoice_no_subscription_line_items")
    INVOICE_NO_SUBSCRIPTION_LINE_ITEMS,
    @JsonProperty("invoice_not_editable")
    INVOICE_NOT_EDITABLE,
    @JsonProperty("invoice_upcoming_none")
    INVOICE_UPCOMING_NONE,
    @JsonProperty("livemode_mismatch")
    LIVEMODE_MISMATCH,
    @JsonProperty("missing")
    MISSING,
    @JsonProperty("not_allowed_on_standard_account")
    NOT_ALLOWED_ON_STANDARD_ACCOUNT,
    @JsonProperty("order_creation_failed")
    ORDER_CREATION_FAILED,
    @JsonProperty("order_required_settings")
    ORDER_REQUIRED_SETTINGS,
    @JsonProperty("order_status_invalid")
    ORDER_STATUS_INVALID,
    @JsonProperty("order_upstream_timeout")
    ORDER_UPSTREAM_TIMEOUT,
    @JsonProperty("out_of_inventory")
    OUT_OF_INVENTORY,
    @JsonProperty("parameter_invalid_integer")
    PARAMETER_INVALID_INTEGER,
    @JsonProperty("parameter_invalid_string_blank")
    PARAMETER_INVALID_STRING_BLANK,
    @JsonProperty("parameter_invalid_string_empty")
    PARAMETER_INVALID_STRING_EMPTY,
    @JsonProperty("parameter_missing")
    PARAMETER_MISSING,
    @JsonProperty("parameter_unknown")
    PARAMETER_UNKNOWN,
    @JsonProperty("payment_intent_authentication_failure")
    PAYMENT_INTENT_AUTHENTICATION_FAILURE,
    @JsonProperty("payment_intent_incompatible_payment_method")
    PAYMENT_INTENT_INCOMPATIBLE_PAYMENT_METHOD,
    @JsonProperty("payment_intent_invalid_parameter")
    PAYMENT_INTENT_INVALID_PARAMETER,
    @JsonProperty("payment_intent_payment_attempt_failed")
    PAYMENT_INTENT_PAYMENT_ATTEMPT_FAILED,
    @JsonProperty("payment_intent_unexpected_state")
    PAYMENT_INTENT_UNEXPECTED_STATE,
    @JsonProperty("payment_method_unactivated")
    PAYMENT_METHOD_UNACTIVATED,
    @JsonProperty("payment_method_unexpected_state")
    PAYMENT_METHOD_UNEXPECTED_STATE,
    @JsonProperty("payouts_not_allowed")
    PAYOUTS_NOT_ALLOWED,
    @JsonProperty("platform_api_key_expired")
    PLATFORM_API_KEY_EXPIRED,
    @JsonProperty("postal_code_invalid")
    POSTAL_CODE_INVALID,
    @JsonProperty("processing_error")
    PROCESSING_ERROR,
    @JsonProperty("product_inactive")
    PRODUCT_INACTIVE,
    @JsonProperty("rate_limit")
    RATE_LIMIT,
    @JsonProperty("resource_already_exists")
    RESOURCE_ALREADY_EXISTS,
    @JsonProperty("resource_missing")
    RESOURCE_MISSING,
    @JsonProperty("routing_number_invalid")
    ROUTING_NUMBER_INVALID,
    @JsonProperty("secret_key_required")
    SECRET_KEY_REQUIRED,
    @JsonProperty("sepa_unsupported_account")
    SEPA_UNSUPPORTED_ACCOUNT,
    @JsonProperty("setup_attempt_expired")
    SETUP_ATTEMPT_EXPIRED,
    @JsonProperty("setup_intent_authentication_failure")
    SETUP_INTENT_AUTHENTICATION_FAILURE,
    @JsonProperty("setup_intent_incompatible_payment_method")
    SETUP_INTENT_INCOMPATIBLE_PAYMENT_METHOD,
    @JsonProperty("setup_intent_invalid_parameter")
    SETUP_INTENT_INVALID_PARAMETER,
    @JsonProperty("setup_intent_unexpected_state")
    SETUP_INTENT_UNEXPECTED_STATE,
    @JsonProperty("subscription_already_canceled")
    SUBSCRIPTION_ALREADY_CANCELED,
    @JsonProperty("subscription_already_in_past_due")
    SUBSCRIPTION_ALREADY_IN_PAST_DUE,
    @JsonProperty("subscription_cancellation_failed")
    SUBSCRIPTION_CANCELLATION_FAILED,
    @JsonProperty("subscription_create_failed")
    SUBSCRIPTION_CREATE_FAILED,
    @JsonProperty("subscription_inactive")
    SUBSCRIPTION_INACTIVE,
    @JsonProperty("subscription_incomplete")
    SUBSCRIPTION_INCOMPLETE,
    @JsonProperty("subscription_invalid_parameter")
    SUBSCRIPTION_INVALID_PARAMETER,
    @JsonProperty("subscription_pause_beyond_max_pause_date")
    SUBSCRIPTION_PAUSE_BEYOND_MAX_PAUSE_DATE,
    @JsonProperty("subscription_schedule_canceled")
    SUBSCRIPTION_SCHEDULE_CANCELED,
    @JsonProperty("subscription_schedule_creation_failed")
    SUBSCRIPTION_SCHEDULE_CREATION_FAILED,
    @JsonProperty("subscription_schedule_edit_mismatch")
    SUBSCRIPTION_SCHEDULE_EDIT_MISMATCH,
    @JsonProperty("subscription_schedule_inactive")
    SUBSCRIPTION_SCHEDULE_INACTIVE,
    @JsonProperty("subscription_schedule_never_active")
    SUBSCRIPTION_SCHEDULE_NEVER_ACTIVE,
    @JsonProperty("subscription_schedule_not_cancellable")
    SUBSCRIPTION_SCHEDULE_NOT_CANCELLABLE,
    @JsonProperty("subscription_schedule_start_date_in_past")
    SUBSCRIPTION_SCHEDULE_START_DATE_IN_PAST,
    @JsonProperty("subscription_update_failed")
    SUBSCRIPTION_UPDATE_FAILED,
    @JsonProperty("tax_id_invalid")
    TAX_ID_INVALID,
    @JsonProperty("taxes_calculation_failed")
    TAXES_CALCULATION_FAILED,
    @JsonProperty("testmode_charges_only")
    TESTMODE_CHARGES_ONLY,
    @JsonProperty("three_d_secure_authentication_required")
    THREE_D_SECURE_AUTHENTICATION_REQUIRED,
    @JsonProperty("tls_version_unsupported")
    TLS_VERSION_UNSUPPORTED,
    @JsonProperty("token_already_used")
    TOKEN_ALREADY_USED,
    @JsonProperty("token_creation_failed")
    TOKEN_CREATION_FAILED,
    @JsonProperty("token_not_found")
    TOKEN_NOT_FOUND,
    @JsonProperty("transfer_already_reversed")
    TRANSFER_ALREADY_REVERSED,
    @JsonProperty("transfer_creation_failed")
    TRANSFER_CREATION_FAILED,
    @JsonProperty("transfer_reversal_not_allowed")
    TRANSFER_REVERSAL_NOT_ALLOWED,
    @JsonProperty("uncategorized")
    UNCATEGORIZED,
    @JsonProperty("unsupported_currency")
    UNSUPPORTED_CURRENCY,
    @JsonProperty("webhook_endpoint_error")
    WEBHOOK_ENDPOINT_ERROR,
    @JsonProperty("webhook_signing_secret_incorrect")
    WEBHOOK_SIGNING_SECRET_INCORRECT,
}
