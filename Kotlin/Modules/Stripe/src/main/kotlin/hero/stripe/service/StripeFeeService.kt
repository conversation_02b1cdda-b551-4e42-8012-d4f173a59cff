package hero.stripe.service

import hero.model.euCountries
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant

fun computeFee(
    feePercents: BigDecimal,
    creatorVatId: String?,
    creatorCountry: String,
    instant: Instant,
    countryToVatMapping: VatMapping,
): Triple<BigDecimal?, BigDecimal, BigDecimal> {
    // Invoicing & VAT specification: https://linear.app/herohero/issue/HH-638/invoicing-spec
    // eg.: (total) 100% - (fee) 10% - (vat) 10% * 21% = 87.9%
    // https://linear.app/herohero/issue/HH-1353/only-eu-countries-should-be-charged-with-vat
    val vatPercents: Int = when {
        // CZ must always have VAT with no exceptions
        creatorCountry == "CZ" -> countryToVatMapping["CZ", instant]
        // EU countries with VAT ID will follow reverse charge, therefore 0 VAT
        creatorCountry in euCountries && creatorVatId != null -> 0
        // EU countries without VAT ID will follow One-stop-shop and pay local VAT
        creatorCountry in euCountries && creatorVatId == null -> countryToVatMapping[creatorCountry, instant]
        // non-EU countries will pay no VAT
        else -> 0
    }

    val feeVatPercents = feePercents.times(vatPercents.toBigDecimal()).divide(BigDecimal(MAX_PERCENT_VALUE))

    // outside of EU, we use
    val transferPercents = if (creatorCountry in euCountries)
        (BigDecimal(MAX_PERCENT_VALUE).setScale(4) - feePercents - feeVatPercents)
            .setScale(2, RoundingMode.HALF_UP)
    else
        null

    return Triple(
        transferPercents,
        feePercents,
        feeVatPercents,
    )
}

internal const val MAX_PERCENT_VALUE = 100L
