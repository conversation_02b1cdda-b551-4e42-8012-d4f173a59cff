package hero.stripe.service

import com.stripe.exception.CardException
import com.stripe.exception.InvalidRequestException
import com.stripe.model.PaymentMethod
import com.stripe.model.SetupIntent
import com.stripe.param.ChargeSearchParams
import com.stripe.param.PaymentMethodAttachParams
import com.stripe.param.PaymentMethodListParams
import com.stripe.param.PaymentMethodUpdateParams
import com.stripe.param.SetupIntentCreateParams
import com.stripe.param.SetupIntentListParams
import com.stripe.param.SubscriptionListParams
import com.stripe.param.SubscriptionUpdateParams
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.PubSub
import hero.jackson.parseEnum
import hero.model.Currency
import hero.model.topics.CardCreateType
import hero.model.topics.CustomerPaymentMethodsUpdated
import hero.model.topics.PaymentMethodChanged
import hero.model.topics.StripeChargeReceived
import hero.stripe.model.SetupIntentStatus
import hero.stripe.model.StripeDeclineCode
import hero.stripe.model.StripeErrorCode
import java.time.Instant
import kotlin.concurrent.thread

class StripePaymentMethodsService(
    private val clients: StripeClients,
    private val service: StripeService,
    private val pubSub: PubSub?,
) {
    fun putPaymentMethodViaAttach(
        paymentMethodId: String,
        customerId: String,
        currency: Currency,
        makeDefault: Boolean,
        cardCreateType: CardCreateType? = null,
    ): PaymentMethod =
        try {
            retry {
                val paymentMethod = clients[currency].paymentMethods().retrieve(paymentMethodId)
                if (paymentMethod.customer == null) {
                    paymentMethod.attach(PaymentMethodAttachParams.builder().setCustomer(customerId).build())
                    pubSub?.publish(
                        PaymentMethodChanged(
                            customerId,
                            paymentMethodId,
                            currency,
                            makeDefault,
                            cardCreateType,
                        ),
                    )
                }
                // Note that this payment method will only be used for new subscriptions when
                // subscriptions will have their original paymentMethod detached.
                return@retry paymentMethod
            }
        } catch (e: CardException) {
            val message = e.localizedMessage.replace(";.*".toRegex(), "")
            val succeededCharges = succeededCharges(paymentMethodId, currency, false)
            pubSub?.publish(
                StripeChargeReceived.WithoutId(
                    chargeId = e.charge,
                    paymentMethodId = paymentMethodId,
                    customerId = customerId,
                    succeededCharges = succeededCharges,
                    declineCode = e.declineCode,
                    declineMessage = e.message,
                    currency = currency,
                ),
            )
            throw ForbiddenException(
                message,
                body = StripeService.CardDeclinedResponse(
                    message,
                    e.charge,
                    e.declineCode,
                    e.code,
                    e.param,
                    e.requestId,
                ),
                labels = mapOf("customerId" to customerId, "paymentMethodId" to paymentMethodId),
            )
        }

    fun putPaymentMethodViaSetupIntent(
        paymentMethodId: String,
        customerId: String,
        currency: Currency,
        makeDefault: Boolean,
        cardCreateType: CardCreateType? = null,
        /** should only be used when we want to attach the payment method without FE interaction */
        confirm: Boolean = false,
    ): SetupResponse =
        retry {
            try {
                // attach the payment method to the customer
                val params = SetupIntentCreateParams.builder()
                    .setCustomer(customerId)
                    .setPaymentMethod(paymentMethodId)
                    // A `return_url` must be specified because this Setup Intent is configured to automatically accept the payment
                    // methods enabled in the Dashboard, some of which may require a full page redirect to succeed. If you do not
                    // want to accept redirect-based payment methods, set `automatic_payment_methods[enabled]` to `true` and
                    // `automatic_payment_methods[allow_redirects]` to `never` when creating Setup Intents and Payment Intents.
                    .setAutomaticPaymentMethods(
                        SetupIntentCreateParams.AutomaticPaymentMethods.builder()
                            .setEnabled(true)
                            .setAllowRedirects(SetupIntentCreateParams.AutomaticPaymentMethods.AllowRedirects.NEVER)
                            .build(),
                    )
                    // When we are storing payment methods via SetupIntent, we cannot write `cardCreateType` directly
                    // to payment method, because it is not yet attached to the customer (will happen on frontend-side).
                    // To do so, we use `shiftMetadataFromSetupIntentToPaymentMethod` method in webhook call.
                    // Method `putPaymentMethodViaAttach` can do this directly.
                    .putMetadata("cardCreateType", cardCreateType?.name)
                    .putMetadata("currency", currency.name)
                    // Payment method can be set as default ONLY once it is confirmed, otherwise, it needs to be
                    // performed in a webhook, see:
                    // https://linear.app/herohero/issue/HH-1069/make-payment-method-default-on-new-attachment
                    // https://linear.app/herohero/issue/HH-2342/spec-for-making-new-cards-as-default
                    .putMetadata("makeDefault", makeDefault.toString())
                    .build()
                val setupIntent = clients[currency].setupIntents().create(params)
                val setupConfirm = if (confirm) {
                    val setupConfirm = setupIntent.confirm()
                    setupConfirm
                } else {
                    null
                }
                val status = setupConfirm?.status ?: setupIntent.status
                val nextAction = setupConfirm?.nextAction ?: setupIntent.nextAction

                thread {
                    checkFraudsForSetupIntents(customerId, currency)
                }

                SetupResponse(
                    paymentMethodId = paymentMethodId,
                    customerId = customerId,
                    status = parseEnum<SetupIntentStatus>(status)
                        ?: error("Cannot parse $status as SetupIntentStatus."),
                    clientSecret = setupIntent.clientSecret,
                    nextAction = nextAction,
                    declineCode = null,
                    declineError = null,
                    declineMessage = null,
                )
            } catch (e: CardException) {
                val succeededCharges = succeededCharges(paymentMethodId, currency, false)
                pubSub?.publish(
                    StripeChargeReceived.WithoutId(
                        chargeId = e.charge,
                        paymentMethodId = paymentMethodId,
                        customerId = customerId,
                        succeededCharges = succeededCharges,
                        declineCode = e.declineCode,
                        declineMessage = e.message,
                        currency = currency,
                    ),
                )
                SetupResponse(
                    paymentMethodId = paymentMethodId,
                    customerId = customerId,
                    status = SetupIntentStatus.CANCELLED,
                    clientSecret = null,
                    nextAction = null,
                    declineCode = parseEnum<StripeDeclineCode>(e.declineCode),
                    declineError = parseEnum<StripeErrorCode>(e.code),
                    declineMessage = e.stripeError.message,
                )
            }
        }

    data class SetupResponse(
        val paymentMethodId: String,
        val customerId: String,
        val status: SetupIntentStatus,
        val clientSecret: String?,
        val nextAction: SetupIntent.NextAction?,
        // https://stripe.com/docs/declines/codes
        val declineCode: StripeDeclineCode?,
        // https://stripe.com/docs/error-codes
        val declineError: StripeErrorCode?,
        val declineMessage: String?,
    )

    private fun checkFraudsForSetupIntents(
        customerId: String,
        currency: Currency,
    ) {
        val cardsTried = clients[currency].setupIntents()
            .list(
                SetupIntentListParams.builder()
                    .addExpand("data.payment_method")
                    .setCustomer(customerId)
                    .setCreated(
                        SetupIntentListParams.Created.builder()
                            .setGt(Instant.now().minusDays(1).epochSecond).build(),
                    )
                    .build(),
            )
            .autoPagingIterable()
            .mapNotNull { it.paymentMethodObject?.card?.last4 }
            .distinct()

        if (cardsTried.count() > 2) {
            service.fraudWarning(customerId, currency, "Too many payment method setup intents.")
        }
    }

    fun deletePaymentMethodsWithSameSuffix(
        customerId: String,
        paymentMethodIdToDelete: String,
        currency: Currency,
    ) {
        val allPaymentMethods = paymentMethods(customerId, currency)
        val cardToDelete = allPaymentMethods.firstOrNull { it.id == paymentMethodIdToDelete }?.card
            ?: throw NotFoundException("PaymentMethod $paymentMethodIdToDelete was not found.")

        allPaymentMethods
            .filter { it.card.last4 == cardToDelete.last4 && it.card.brand == cardToDelete.brand }
            .forEach { relatedPaymentMethod ->
                deletePaymentMethod(customerId, relatedPaymentMethod, currency)
            }
    }

    fun deletePaymentMethod(
        customerId: String,
        paymentMethodToDelete: PaymentMethod,
        currency: Currency,
    ) {
        try {
            clients[currency].paymentMethods().detach(paymentMethodToDelete.id)
            val customer = clients[currency].customers().retrieve(customerId)

            // get first available payment method (or null) to be default
            val availablePaymentMethod = paymentMethods(customerId, currency)
                .firstOrNull()

            // if customer is using this payment method as default, it must be detached from invoiceSettings
            // this is very important, otherwise this paymentMethod will still be retried for past_due subscriptions
            val newDefaultPaymentMethodId: String? =
                if (customer.invoiceSettings.defaultPaymentMethod == null ||
                    customer.invoiceSettings.defaultPaymentMethod == paymentMethodToDelete.id
                ) {
                    availablePaymentMethod?.let {
                        pubSub?.publish(
                            PaymentMethodChanged(
                                customerId = customerId,
                                paymentMethodId = it.id,
                                currency = currency,
                                makeDefault = true,
                                cardCreateType = null,
                            ),
                        )
                    }
                    availablePaymentMethod?.id
                } else {
                    customer.invoiceSettings.defaultPaymentMethod ?: availablePaymentMethod?.id
                }

            // now we need to iterate all subscriptions and replace the deleted payment method from them
            val paymentMethodUpdateParams = SubscriptionUpdateParams.builder()
                .setDefaultPaymentMethod(newDefaultPaymentMethodId)
                .build()

            clients[currency].subscriptions()
                .list(SubscriptionListParams.builder().setCustomer(customerId).build())
                .autoPagingIterable()
                .filter { it.defaultPaymentMethod == null || it.defaultPaymentMethod == paymentMethodToDelete.id }
                .forEach {
                    log.info(
                        "Replacing subscription ${it.id} payment method: " +
                            "${it.defaultPaymentMethod} -> $newDefaultPaymentMethodId",
                    )
                    retry { it.update(paymentMethodUpdateParams) }
                }

            pubSub?.publish(
                CustomerPaymentMethodsUpdated(customerId = customerId, currency = currency, paymentMethodId = null),
            )
        } catch (e: InvalidRequestException) {
            log.error(
                "Could not detach payment method (already detached?): ${e.message}",
                mapOf("customerId" to customerId, "paymentMethodId" to paymentMethodToDelete.id),
            )
        }
    }

    fun deleteAllPaymentMethods(
        customerId: String,
        currency: Currency,
    ) {
        retry { paymentMethods(customerId, currency) }
            .forEach {
                retry {
                    deletePaymentMethod(customerId, it, currency)
                }
            }
    }

    fun paymentMethods(
        customerId: String,
        currency: Currency,
    ): List<PaymentMethod> {
        val params = PaymentMethodListParams.builder()
            .setCustomer(customerId)
            .setType(PaymentMethodListParams.Type.CARD)
            .build()
        return retry {
            try {
                clients[currency].paymentMethods().list(params).autoPagingIterable().toList()
            } catch (e: NoSuchElementException) {
                // not sure why this may happen
                emptyList()
            } catch (e: InvalidRequestException) {
                if (e.code == "resource_missing") {
                    throw NotFoundException(e.message)
                }
                throw e
            }
        }
    }

    fun paymentMethod(
        paymentMethodId: String,
        currency: Currency,
    ): PaymentMethod =
        retry {
            clients[currency].paymentMethods().retrieve(paymentMethodId)
        }

    fun shiftMetadataFromSetupIntentToPaymentMethod(
        customerId: String,
        paymentMethodId: String,
        currency: Currency,
    ) {
        retry {
            val setupIntents = clients[currency].setupIntents()
                .list(SetupIntentListParams.builder().setPaymentMethod(paymentMethodId).build()).data
            val setupIntent = setupIntents.firstOrNull { it.metadata["cardCreateType"] != null }
                ?: return@retry

            pubSub?.publish(
                PaymentMethodChanged(
                    customerId = customerId,
                    paymentMethodId = paymentMethodId,
                    currency = currency,
                    makeDefault = setupIntent.metadata["makeDefault"] == "true",
                    cardCreateType = setupIntent.metadata["cardCreateType"]?.let { parseEnum<CardCreateType>(it) },
                ),
            )
            clients[currency].paymentMethods().update(
                paymentMethodId,
                PaymentMethodUpdateParams.builder()
                    .putAllMetadata(setupIntent.metadata)
                    .build(),
            )
        }
    }

    private fun succeededCharges(
        paymentMethodId: String,
        currency: Currency,
        estimate: Boolean = false,
    ): Int =
        retry {
            // charges cannot be searched directly by paymentMethod, we have to search by fingerprints
            val fingerprint = clients[currency]
                .paymentMethods()
                .retrieve(paymentMethodId)
                .card
                .fingerprint

            clients[currency]
                .charges()
                .search(
                    ChargeSearchParams.builder()
                        .setQuery("payment_method_details.card.fingerprint:'$fingerprint' AND status:'succeeded'")
                        .build(),
                )
                .let {
                    if (estimate) {
                        it.data
                    } else {
                        it.autoPagingIterable()
                    }
                }
                .filter { it.paymentMethod == paymentMethodId }
                .size
        }
}
