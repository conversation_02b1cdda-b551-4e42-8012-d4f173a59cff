package hero.baseutils

import kotlin.random.Random
import kotlin.reflect.KClass
import kotlin.reflect.full.isSubclassOf

/**
 * Function handles given exceptions in body and retries according to its parameters.
 * WARN cannot be made reified because of second T type parameter (DO NOT TRY)
 * @param onExceptions Exception class to be handled - method searches even for wrapped exceptions.
 * @param retries Number of retries to be tried.
 * @param retryWaitMillis Initial wait before first retry. Next retries are exponentially increased.
 *                        Eg. When using default parameters, it might be blocked up to 3 seconds.
 * @param body Function body to be executed.
 */
fun <T> retryOn(
    vararg onExceptions: KClass<out Throwable>,
    retries: Int = SystemEnv.ioRetries,
    retryWaitMillis: Long = 200L,
    body: () -> T,
): T {
    lateinit var lastException: Throwable
    var currentWaitMillis = retryWaitMillis

    for (tried in 0..retries) {
        try {
            return body.invoke()
        } catch (e: Throwable) {
            // note that we search for onExceptions, but in case of failure, we want to keep the whole exception
            onExceptions.mapNotNull { e.unwrapException(it) }.firstOrNull() ?: throw e
            // keeping the original exception
            lastException = e
            if (tried < retries) {
                val retryTimeout = (currentWaitMillis * Random.nextDouble(LOWER_BOUND, UPPER_BOUND)).toLong()
                @Suppress("ktlint:standard:max-line-length")
                log.debug(
                    "Retrying operation in $retryTimeout ms: $tried <= $retries:" +
                        " ${lastException::class.simpleName}/${lastException.message}.",
                )
                // might be better to use coroutines delay instead of blocking entire thread
                Thread.sleep(retryTimeout)
                currentWaitMillis *= 2
            }
        }
    }
    // if we were not successful, throw last exception
    log.error(
        "Thrown ${lastException::class.simpleName}/${lastException.message}" +
            " couldn't be resolved by $retries retries.",
    )
    throw lastException
}

@Suppress("unchecked_cast")
fun <T : Throwable> Throwable.unwrapException(throwable: KClass<T>): T? =
    when {
        this::class.isSubclassOf(throwable) -> this as T
        cause != null -> cause!!.unwrapException(throwable)
        else -> null
    }

/**
 * Apply given method on every element until first successful application(no exception was thrown).
 */
fun <T, R> Collection<T>.firstSuccessfulResult(fn: (T) -> R): Result<R> {
    if (this.isEmpty()) {
        return Result.failure(IllegalStateException("Empty array"))
    }

    val failedResults = mutableListOf<Result<R>>()

    for (value in this) {
        val result = runCatching { fn(value) }
        if (result.isSuccess) {
            return result
        } else {
            failedResults += result
        }
    }

    return failedResults.last()
}

private const val LOWER_BOUND = 0.7
private const val UPPER_BOUND = 1.3
