package hero.baseutils

import com.vdurmont.emoji.EmojiParser
import java.net.MalformedURLException
import java.net.URISyntaxException
import java.net.URL
import java.net.URLEncoder
import java.security.MessageDigest
import java.text.BreakIterator
import java.text.Normalizer
import java.util.Base64

fun String.cyrillicToAscii(): String =
    this
        .replace("а", "a")
        .replace("б", "b")
        .replace("в", "v")
        .replace("г", "g")
        .replace("д", "d")
        .replace("е", "e")
        .replace("ё", "jo")
        .replace("ж", "zh")
        .replace("з", "z")
        .replace("и", "i")
        .replace("й", "jj")
        .replace("к", "k")
        .replace("л", "l")
        .replace("м", "m")
        .replace("н", "n")
        .replace("о", "o")
        .replace("п", "p")
        .replace("р", "r")
        .replace("с", "s")
        .replace("т", "t")
        .replace("у", "u")
        .replace("ф", "f")
        .replace("х", "kh")
        .replace("ц", "c")
        .replace("ч", "ch")
        .replace("ш", "sh")
        .replace("щ", "shh")
        .replace("ъ", "")
        .replace("ы", "y")
        .replace("ь", "")
        .replace("э", "eh")
        .replace("ю", "ju")
        .replace("я", "ja")
        .replace("А", "A")
        .replace("Б", "B")
        .replace("В", "V")
        .replace("Г", "G")
        .replace("Д", "D")
        .replace("Е", "E")
        .replace("Ё", "JO")
        .replace("Ж", "ZH")
        .replace("З", "Z")
        .replace("И", "I")
        .replace("Й", "JJ")
        .replace("К", "K")
        .replace("Л", "L")
        .replace("М", "M")
        .replace("Н", "N")
        .replace("О", "O")
        .replace("П", "P")
        .replace("Р", "R")
        .replace("С", "S")
        .replace("Т", "T")
        .replace("У", "U")
        .replace("Ф", "F")
        .replace("Х", "KH")
        .replace("Ц", "C")
        .replace("Ч", "CH")
        .replace("Ш", "SH")
        .replace("Щ", "SHH")
        .replace("Ъ", "")
        .replace("Ы", "Y")
        .replace("Ь", "")
        .replace("Э", "EH")
        .replace("Ю", "JU")
        .replace("Я", "JA")

val asciiRegex = "\\p{InCombiningDiacriticalMarks}+".toRegex()

fun CharSequence.normalize(): String = Normalizer.normalize(this, Normalizer.Form.NFD)

fun CharSequence.toAscii(): String =
    this
        .normalize()
        .replace(asciiRegex, "")
        .replace("ø", "o")
        .replace("Ø", "O")
        .replace("ł", "l")
        .replace("Ł", "L")
        .replace("ß", "s")
        .replace("$", "s")
        .cyrillicToAscii()

fun CharSequence.removeNonAscii(): String =
    this
        .toAscii()
        .replace("[^a-zA-Z0-9]+".toRegex(), "")
        .trim()

fun String.webalize(
    character: Char = '-',
    lowercase: Boolean = true,
): String {
    if (this.isEmpty()) {
        return "null"
    }

    var result = this

    if (lowercase) {
        result = result.lowercase()
    }

    return result
        .replace("\\s*[&]\\s*".toRegex(), " and ")
        .let { EmojiParser.parseToAliases(it, EmojiParser.FitzpatrickAction.REMOVE) }
        .toAscii()
        // don't -> dont instead of don-t
        .replace("'", "")
        .replace("[^a-zA-Z0-9]+".toRegex(), character.toString())
        .trim(character)
}

fun String.escapeUrl(): String = URLEncoder.encode(this, "utf-8")

fun String.truncate(
    maxlen: Int?,
    replacement: String = "…",
): String {
    if (maxlen == null || length <= maxlen) return this

    val endIndex = BreakIterator.getWordInstance().let {
        it.setText(this)
        it.preceding(maxlen)
    }

    return substring(0, if (endIndex == 0) maxlen else endIndex)
        .replace("[\\s :\"!\\.-]]*$".toRegex(), "") + replacement
}

fun String.md5nice(): String = this.md5().lowercase().replace("[^a-z]".toRegex(), "")

fun String.md5(): String = this.mdInternal().toBase64().replace("[^a-zA-Z0-9]".toRegex(), "")

private fun String.mdInternal(): ByteArray {
    val md = MessageDigest.getInstance("MD5")
    md.update(this.toByteArray(charset("UTF-8")))
    return md.digest()
}

fun String.tokenize(): List<String> =
    this.toAscii().lowercase().split("[^a-z0-9]".toRegex()).filter { it.isNotBlank() }.distinct()

fun String.prefixes(): Set<String> = (1..count()).map { take(it).trim() }.toSet()

fun List<String>.prefixes(): List<String> =
    this
        .permutations()
        // we don't need to keep any spaces
        .map { it.joinToString("") }
        .map { it.prefixes() }
        .flatten()

fun tokenize(vararg strings: String?): List<String> = tokenize(strings.toList())

fun tokenize(strings: Map<*, String>): List<String> = tokenize(strings.values.toList())

fun tokenize(strings: List<String?>): List<String> = strings.filterNotNull().map { it.tokenize() }.flatten().distinct()

fun String?.nullIfEmpty(): String? = if (this.isNullOrEmpty()) null else this

fun <R, S> Map<R, S>?.nullIfEmpty(): Map<R, S>? = if (this?.isNotEmpty() == true) this else null

fun <T> List<T>?.nullIfEmpty(): List<T>? = if (this?.isNotEmpty() == true) this else null

fun <T> Collection<T>?.nullIfEmpty(): Collection<T>? = if (this?.isNotEmpty() == true) this else null

fun String.decodeBase64(): ByteArray = java.util.Base64.getDecoder().decode(this)

fun String.parsePem(): ByteArray =
    this
        // remove -----BEGIN|END PUBLIC KEY-----
        .replace("-----[A-Z ]+-----".toRegex(), "")
        .replace("\\s+".toRegex(), "")
        // getting the content in DER format
        .decodeBase64()

// Note that base64 is not url safe! One must use toBase64().escapeUrl().
fun ByteArray.toBase64() = String(Base64.getEncoder().encode(this))

fun String.toBase64() = toByteArray().toBase64()

fun String.fromBase64() = String(Base64.getDecoder().decode(toByteArray()))

@Suppress("ReturnCount")
fun String.isUrlValid(requiredDomain: String? = null): Boolean {
    return try {
        if (this.contains("[\"']".toRegex())) {
            return false
        }
        // Even though URL constructor is deprecated, the URI.toURL() throws generic IllegalStateExceptions
        // so it may be better to keep the original URL() to receive specific Malformed/URISyntax.
        val url = URL(this)
        if (requiredDomain != null && !url.host.matches(requiredDomain.toRegex())) {
            return false
        }
        true
    } catch (e: MalformedURLException) {
        false
    } catch (e: URISyntaxException) {
        false
    }
}

// https://linear.app/herohero/issue/HH-3443/polish-new-e-mail-template#comment-f2480dd6
fun String.preventGmailLinks(): String = this.replace(".", ".\u200C")

fun allowedRedirects(
    production: Boolean,
    currentHostName: String,
): String =
    if (production)
        URL(currentHostName).host
    else
        "(?:.*\\.herohero\\.co)|(?:localhost)"

fun randomString(
    length: Int,
    allowedCharacters: CharRange = ('a'..'z'),
): String =
    (1..length)
        .map { allowedCharacters.random() }
        .joinToString("")
