package hero.spotify

import com.github.kittinunf.fuel.core.Request
import com.github.kittinunf.fuel.core.extensions.authentication
import com.github.kittinunf.fuel.httpPost
import hero.baseutils.FuelException
import hero.baseutils.fetch
import hero.baseutils.fuelUserAgent
import hero.exceptions.http.HttpStatusException
import hero.exceptions.http.NotFoundException
import hero.spotify.dto.AccessTokenResponse
import hero.spotify.dto.GetEntitlements
import hero.spotify.dto.RegisterUserResponse
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.jackson.io.JacksonSerializer
import java.net.URLEncoder
import javax.crypto.spec.SecretKeySpec

/** https://open-access.spotify.com/api */
@OptIn(ExperimentalStdlibApi::class)
class SpotifyOpenClient(
    private val clientId: String,
    private val clientSecret: String,
    private val partnerId: String,
) {
    private val openAccessPath = "https://open-access.spotify.com/api/v1"
    private val accountsPath = "https://accounts.spotify.com"
    private val hmacKey = SecretKeySpec(clientSecret.toByteArray(), "HmacSHA256")

    /**
     * The `client` access token differs from `user` one and is related
     * to the whole Herohero app, whereas `user` one relates to specific user.
     * This token is used eg. for unlinking our users from Spotify.
     */
    private fun requestClientAccessToken(scope: String) =
        "$accountsPath/api/token"
            .httpPost()
            .header("Content-Type", "application/x-www-form-urlencoded")
            .body(
                listOf(
                    "grant_type" to "client_credentials",
                    "scope" to scope,
                )
                    .map { it.first + "=" + URLEncoder.encode(it.second, "UTF-8") }
                    .joinToString("&"),
            )
            .authentication()
            .basic(clientId, clientSecret)
            .fetch<AccessTokenResponse>()

    fun refreshToken(refreshToken: String): AccessTokenResponse =
        // TODO implement via OAuthController
        "$accountsPath/api/token"
            .httpPost()
            .header("Content-Type", "application/x-www-form-urlencoded")
            .body(
                listOf(
                    "grant_type" to "refresh_token",
                    "refresh_token" to refreshToken,
                    "client_id" to clientId,
                    "client_secret" to clientSecret,
                )
                    .map { it.first + "=" + URLEncoder.encode(it.second, "UTF-8") }
                    .joinToString("&"),
            )
            .fetch<AccessTokenResponse>()

    // https://developer.spotify.com/documentation/open-access/reference/register-user
    fun registerUser(
        userId: String,
        userAccessToken: String,
        entitlements: List<String>,
    ): RegisterUserResponse =
        try {
            "$openAccessPath/register-user"
                .httpPost()
                .authorize(userAccessToken)
                .body(
                    sign(
                        "partner_id" to partnerId,
                        // https://developer.spotify.com/documentation/open-access/concepts#partner-user-id
                        "partner_user_id" to userId.toByteArray().toHexString(),
                        "entitlements" to entitlements.plus(userId).map { it.toByteArray().toHexString() },
                    ),
                )
                .fetch<RegisterUserResponse>()
        } catch (e: FuelException) {
            throw HttpStatusException(
                e.status,
                e.message,
                mapOf("userId" to userId, "entitlements" to entitlements.toString()),
                null,
            )
        }

    // https://developer.spotify.com/documentation/open-access/reference/add-entitlements
    fun addEntitlements(
        userId: String,
        entitlements: List<String>,
    ) {
        val clientTokens = requestClientAccessToken("soa-manage-entitlements")
        val response = "$openAccessPath/add-entitlements"
            .httpPost()
            .authorize(clientTokens.accessToken)
            .body(
                sign(
                    "partner_id" to partnerId,
                    "partner_user_id" to userId.toByteArray().toHexString(),
                    "entitlements" to entitlements.plus(userId).map { it.toByteArray().toHexString() },
                ),
            )
            .response()
            .second

        if (response.statusCode >= 300) {
            throw HttpStatusException(response.statusCode, response.responseMessage, emptyMap(), null)
        }
    }

    // https://developer.spotify.com/documentation/open-access/reference/delete-entitlements
    fun deleteEntitlements(
        userId: String,
        entitlements: List<String>,
    ) {
        val clientTokens = requestClientAccessToken("soa-manage-entitlements")
        val response = "$openAccessPath/delete-entitlements"
            .httpPost()
            .authorize(clientTokens.accessToken)
            .body(
                sign(
                    "partner_id" to partnerId,
                    "partner_user_id" to userId.toByteArray().toHexString(),
                    "entitlements" to entitlements.map { it.toByteArray().toHexString() },
                ),
            )
            .response()
            .second

        if (response.statusCode >= 300) {
            throw HttpStatusException(response.statusCode, response.responseMessage, emptyMap(), null)
        }
    }

    fun getEntlitlements(userId: String): List<String> {
        val clientTokens = requestClientAccessToken("soa-manage-entitlements")
        try {
            return "$openAccessPath/get-entitlements"
                .httpPost()
                .authorize(clientTokens.accessToken)
                .body(
                    sign(
                        "partner_id" to partnerId,
                        "partner_user_id" to userId.toByteArray().toHexString(),
                    ),
                )
                .fetch<GetEntitlements>()
                .entitlements
                .map { String(it.hexToByteArray()) }
        } catch (e: FuelException) {
            if (e.status == 404) {
                throw NotFoundException("User $userId was not found and was probably unlinked.")
            } else {
                throw HttpStatusException(e.status, e.message, emptyMap(), null)
            }
        }
    }

    // https://developer.spotify.com/documentation/open-access/reference/unlink-user
    fun unlinkUser(userId: String) {
        val clientTokens = requestClientAccessToken("user-soa-unlink")
        val response = "$openAccessPath/unlink-user"
            .httpPost()
            .authorize(clientTokens.accessToken)
            .body(
                sign(
                    "partner_id" to partnerId,
                    "partner_user_id" to userId.toByteArray().toHexString(),
                ),
            )
            .response()
            .second

        // 404 means the user has already been unlinked
        if (response.statusCode != 404 && response.statusCode >= 300) {
            throw HttpStatusException(response.statusCode, response.responseMessage, emptyMap(), null)
        }
    }

    private fun Request.authorize(accessToken: String): Request =
        this
            .header("Authorization", "Bearer $accessToken")
            .header("User-Agent", fuelUserAgent)

    private fun sign(vararg body: Pair<String, Any>): String =
        Jwts.builder()
            .json(JacksonSerializer())
            .claims(
                Jwts.claims()
                    .also { claims ->
                        body.forEach {
                            claims.add(it.first, it.second)
                        }
                    }
                    .build(),
            )
            .also {
                it.header().let { headers ->
                    headers.add("typ", "JWT")
                    headers.add("alg", "HS256")
                }
            }
            .signWith(hmacKey)
            .compact()
}
