package hero.model

enum class ModeratorPermission(val flag: Int) {
    DELETE_USERS(1 shl 0), // 1 << 0 == 1
    CANCEL_SUBSCRIBERS(1 shl 1), // 1 << 1 == 2
    ;

    companion object {
        fun of(vararg permissions: ModeratorPermission): Int {
            return permissions.fold(0) { acc, permission ->
                acc or permission.flag
            }
        }

        fun canDeleteUsers(permission: Int?): <PERSON><PERSON><PERSON> = (permission ?: 0) and DELETE_USERS.flag > 0

        fun canCancelSubscribers(permission: Int?): <PERSON><PERSON><PERSON> = (permission ?: 0) and CANCEL_SUBSCRIBERS.flag > 0
    }
}
