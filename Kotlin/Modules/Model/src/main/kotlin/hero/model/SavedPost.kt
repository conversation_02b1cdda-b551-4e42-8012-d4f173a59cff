package hero.model

import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant

@NoArg
data class SavedPost(
    val postId: String,
    val creatorId: String,
    val userId: String,
    val subscriptionActive: Boolean,
    val savedAt: Instant,
    val postPublishedAt: Instant,
    val id: String = id(userId = userId, postId = postId),
) {
    companion object : EntityCollection<SavedPost> {
        override val collectionName: String = "saved-posts"

        fun id(
            userId: String,
            postId: String,
        ) = "$userId-$postId"
    }
}
