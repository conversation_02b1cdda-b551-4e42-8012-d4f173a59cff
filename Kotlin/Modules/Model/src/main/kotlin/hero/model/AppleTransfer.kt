package hero.model

import hero.core.annotation.NoArg
import hero.core.data.EntityCollection
import java.time.Instant

@NoArg
data class AppleTransfer(
    val appleTransactionId: String,
    val appleReferenceId: String,
    val createdAt: Instant,
    val userId: String,
    val creatorId: String,
    val tierId: String,
    val targetAccountId: String,
    val stripeTransferId: String?,
    val transferredAt: Instant?,
) {
    companion object : EntityCollection<AppleTransfer> {
        override val collectionName: String = "apple-transfers"
    }
}
