package hero.model.topics

import java.time.Instant

/**
 * Published when a new Post is published and chunk of subscribers is fetched. Note that
 * for a single Post there might be more publications of this topic depending on the count
 * of subscribers. Eg. currently Lada Sinai ~150 subscribers, chunks of 100 -> two publications.
 */
data class PostNotificationGroupPrepared(
    val creatorId: String,
    val postIds: List<String>,
    val subscriberIds: List<String>,
    val timestamp: Instant,
)
