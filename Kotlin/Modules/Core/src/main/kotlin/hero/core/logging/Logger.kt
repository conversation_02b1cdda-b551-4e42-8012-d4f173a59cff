package hero.core.logging

/**
 * Interface for logging so we that can interchange cloud logging
 * in tests for any other way
 */
interface Logger {
    fun warn(
        message: String?,
        properties: Map<String, Any?> = mapOf(),
        cause: Throwable? = null,
    )

    fun notice(
        message: String?,
        properties: Map<String, Any?> = mapOf(),
        cause: Throwable? = null,
    )

    fun debug(
        message: String?,
        properties: Map<String, Any?> = mapOf(),
        cause: Throwable? = null,
    )

    fun info(
        message: String?,
        properties: Map<String, Any?> = mapOf(),
        cause: Throwable? = null,
    )

    fun error(
        message: String?,
        properties: Map<String, Any?> = mapOf(),
        cause: Throwable? = null,
    )

    fun fatal(
        message: String?,
        properties: Map<String, Any?> = mapOf(),
        cause: Throwable? = null,
    )
}
