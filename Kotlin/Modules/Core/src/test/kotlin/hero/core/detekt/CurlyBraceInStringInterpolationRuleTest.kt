package hero.core.detekt

import io.gitlab.arturbosch.detekt.test.TestConfig
import io.gitlab.arturbosch.detekt.test.assertThat
import io.gitlab.arturbosch.detekt.test.compileAndLint
import org.junit.jupiter.api.Test

class CurlyBraceInStringInterpolationRuleTest {
    @Test
    fun `should report missing curly braces when there is dot notation following an interpolation`() {
        val underTest = CurlyBraceInStringInterpolationRule(TestConfig())

        val findings = underTest.compileAndLint(
            """
            val hyperlink = "The length is ${'$'}user.length" 
            """.trimIndent(),
        )

        assertThat(findings).hasSize(1)
        assertThat(findings[0]).hasMessage("Wrong string interpolation \"The length is ${'$'}user.length\"")
    }

    @Test
    fun `should not report anything if correctly interpolated`() {
        val underTest = CurlyBraceInStringInterpolationRule(TestConfig())

        val findings = underTest.compileAndLint(
            """
            val hyperlink = "The length is ${'$'}{user.length}" 
            """.trimIndent(),
        )

        assertThat(findings).isEmpty()
    }
}
