concurrent = 16
check_interval = 1

[session_server]
  session_timeout = 1800

[[runners]]
  name = "gitlab-runner-containerized-"
  url = "https://gitlab.com/"
  # This token is not the one given by gitlab UI, you need to generate it with `gitlab-runner register`
  # and found in `/etc/gitlab-runner/config.toml`
  # see https://gitlab.com/gitlab-org/gitlab-runner/issues/1932
  token = "********************"
  executor = "shell"
  # TODO: I don't understand how concurrent, limit and request_concurrency work together:
  # https://docs.gitlab.com/runner/configuration/advanced-configuration.html
  # https://gitlab.com/gitlab-org/gitlab-runner/issues/3751
  # https://stackoverflow.com/questions/54534387/how-gitlab-runner-concurrency-works
  limit = 16
  request_concurrency = 8
