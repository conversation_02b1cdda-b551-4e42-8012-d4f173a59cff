#!/usr/bin/env python3

"""
Helper script to check-out Kotlin module dependencies from GCS-based module cache.

Usage: ./restore-dependencies.py <Name/Of/ci-job>
"""

from subprocess import Popen
import sys
import os

from common import compute_module_hash, get_job_dependencies, REPO_ROOT_DIR


def main(job_name):
    deps = get_job_dependencies(job_name)
    hashes = [compute_module_hash(dep) for dep in deps]
    print(f'Fetching {len(deps)} dependencies:', ''.join(f'\n  {dep} ({hash})' for dep, hash in zip(deps, hashes)),
          file=sys.stderr)
    # Start all gsutil commands concurrently:
    workdir = f'{REPO_ROOT_DIR}/m2'
    if not os.path.exists(workdir):
        os.makedirs(workdir)
    programs = [
        shell_popen(f'gsutil cp gs://heroheroco-gitlab-build-cache/v1/{hash}.tar - | tar -x', cwd=workdir)
        for hash in hashes]
    # Wait for them to finish:
    for program in programs:
        retcode = program.wait()
        if retcode != 0:
            raise Exception(f'Program "{program.args}" failed with return code {retcode}.')


def shell_popen(args, **popen_kwargs):
    # https://stackoverflow.com/a/21742965/4345715
    return Popen(['/bin/bash', '-e', '-o', 'pipefail', '-c', args], **popen_kwargs)


if __name__ == '__main__':
    if len(sys.argv) < 2 or sys.argv[1] in ('-h', '--help'):
        print(__doc__.strip())
        exit(1)
    main(sys.argv[1])
