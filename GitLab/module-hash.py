#!/usr/bin/env python3

"""
Little helper script to compute hash of a GoOut module source code.

Usage: ./module-hash.py Name/Of/ci-job [additional folders to/hash]

It works by:
- reading .gitlab-ci.yml corresponding to specified (usually current) GitLab CI job,
- looking up the specified job, extracting its dependencies, extracting folders of the dependencies,
- computing git tree hashes for: dependency folders, specified job folder, extra folders passed on command line,
- hashing folder hashes (hehe) together with with specified job name.
- cutting it at 8 characters (enough to practically avoid collisions)

That way we specify dependencies only once in .gitlab-ci.yml.

Use of this script introduces additional requirements on:
a) specified job: it must be defined in .gitlab-ci.yml file whose path corresponds to specified job name,
b) parent jobs of the one specified: they must be affected only by files in directory that corresponds to their names.

Additional folders should be specified without trailing slash.
"""

import sys

from common import compute_module_hash


def main(job_name, additional_folders):
    # trailing slashes kill deduplication, fail if somebody passes them.
    assert all(not folder.endswith('/') for folder in additional_folders), additional_folders
    module_hash = compute_module_hash(job_name, additional_folders)
    print(module_hash)


if __name__ == '__main__':
    if len(sys.argv) < 2 or sys.argv[1] in ('-h', '--help'):
        print(__doc__.strip())
        exit(1)
    main(sys.argv[1], sys.argv[2:])
