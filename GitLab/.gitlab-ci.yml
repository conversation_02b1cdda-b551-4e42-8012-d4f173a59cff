include:
- "/GitLab/Runner/.gitlab-ci.yml"

# If running first time, the scaledown_task for $GITLAB_SCALEDOWN_TASK_ID must be created with following script:
#
#    - curl
#      --request POST
#      --header "PRIVATE-TOKEN:$GITLAB_ACCESS_TOKEN"
#      --form "cron=1 1 1 1 1"
#      --form "active=false"
#      --form "description=GitLab runner scale down task."
#      --form "ref=main"
#      "https://gitlab.com/api/v4/projects/heroheroco%2Fbackend/pipeline_schedules"
#
# And then obtain following task id:

variables:
  GITLAB_PROJECT_ID: "heroheroco%2Fbackend"

GitLab/runner-scale-up:
  image: google/cloud-sdk:slim
  stage: gitlab
  interruptible: true
  retry:
    max: 2
  except:
    - schedule
  # We don't need to force running on shared runners (tags: saas-linux-small-amd64) as this can run
  # both on our runner and the shared one. Let the gitlab pick one.
  script:
    # if the runner is scaled down, we scale it up
    - gcloud auth activate-service-account --key-file $GITLAB_RUNNER_GCP_SERVICE_ACCOUNT_FILE;
    - gcloud config set project $CLOUD_PROJECT
    - export INSTANCES=`gcloud compute instance-groups describe gitlab-runner --format='value(size)' --zone=europe-west1-b`
    - if [ $INSTANCES == "0" ]; then
        echo "Scaling gitlab-runner to 2 instances.";
        ./scale-runner.sh 2;
      else
        echo "Runner is already active in $INSTANCES instances.";
      fi
    - export CRON_SCHEDULE=`date -d '25 minutes' +"%M %H %d %m *"`
    - echo "Editing cron to scale down on $CRON_SCHEDULE"
    # we first need to take ownership of the pipeline_schedule with current access token
    # note that the token must be of "MAINTAINER" level here:
    # https://gitlab.com/groups/heroheroco/-/settings/access_tokens
    # for details see: https://support.gitlab.com/hc/requests/521698
    - curl
      --fail
      --request POST
      --header "PRIVATE-TOKEN:$GITLAB_ACCESS_TOKEN"
      "https://gitlab.com/api/v4/projects/$GITLAB_PROJECT_ID/pipeline_schedules/$GITLAB_SCALEDOWN_TASK_ID/take_ownership"
    # and only then we can modify the existing schedule
    - curl
      --fail
      --request PUT
      --header "PRIVATE-TOKEN:$GITLAB_ACCESS_TOKEN"
      --form "cron=$CRON_SCHEDULE"
      --form "active=true"
      "https://gitlab.com/api/v4/projects/$GITLAB_PROJECT_ID/pipeline_schedules/$GITLAB_SCALEDOWN_TASK_ID"
    - echo "See schedules at https://gitlab.com/heroheroco/backend/-/pipeline_schedules"

GitLab/runner-scale-down:
  image: google/cloud-sdk:slim
  stage: gitlab
  interruptible: true
  retry:
    max: 2
  # We don't need to force running on shared runners (tags: saas-linux-small-amd64) as this can run
  # both on our runner and the shared one. Let the gitlab pick one.
  rules:
    # Run unconditionally from schedules.
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: always
    # Prevent creation of "detached" pipelines, needs to be here due to strange GitLab behaviour if missing.
    - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "detached"'
      when: never
    # Otherwise default to manual to enable testing.
    - when: manual
      # We need to explicitly tell GitLab not to wait for this manual job, the auto-allow_failure is not applied to rules.
      allow_failure: true
  script:
    - echo "Scaling down gitlab-runner."
    - gcloud auth activate-service-account --key-file $GITLAB_RUNNER_GCP_SERVICE_ACCOUNT_FILE
    - gcloud config set project $CLOUD_PROJECT
    - ./scale-runner.sh 0
    - echo "See schedules at https://gitlab.com/heroheroco/backend/-/pipeline_schedules"

GitLab/runner-boost:
  image: google/cloud-sdk:slim
  stage: gitlab
  interruptible: true
  retry:
    max: 2
  extends:
    - .run-only-manually
  script:
    - echo "Boosting gitlab-runner."
    - gcloud auth activate-service-account --key-file $GITLAB_RUNNER_GCP_SERVICE_ACCOUNT_FILE
    - gcloud config set project $CLOUD_PROJECT
    - ./scale-runner.sh 4
    - echo "See schedules at https://gitlab.com/heroheroco/backend/-/pipeline_schedules"

# Inspired by https://medium.com/ovrsea/how-to-automatically-rebase-all-your-merge-requests-on-gitlab-when-pushing-on-master-9b7c5119ac5f
GitLab/rebase-all-mrs:
  stage: gitlab
  extends:
    - .run-always-on-main-or-manually
  script:
    - GITLAB_BASE_URL=https://gitlab.com/api/v4/projects
    - AUTH="PRIVATE-TOKEN:$GITLAB_ACCESS_TOKEN"
    - OPEN_MRS=$(curl -fsS -H "$AUTH" "$GITLAB_BASE_URL/$GITLAB_PROJECT_ID/merge_requests?state=opened&wip=no")
    - echo "$OPEN_MRS" | jq -r '.[] | "\(.iid) \(.title)"' | while read -r iid title; do
        printf "Rebasing '$title':\n\t";
        curl -fsS -X PUT -H "$AUTH" "$GITLAB_BASE_URL/$GITLAB_PROJECT_ID/merge_requests/$iid/rebase";
        printf "\n\n\n";
      done
