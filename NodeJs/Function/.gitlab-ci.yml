include:
  - "/NodeJs/Function/FirestoreBackup/.gitlab-ci.yml"

.NodeJs/Function/deploy:
  variables:
    ADDITIONAL_PARAMETERS: ""
    BUILD_ENV_VARS: "BUILD_VARS=NONE"
  needs: []
  script:
    # prevent regular wildcard expansion and build files to compute the module hash from
    - set -f
    - HASHED_FILES=('*.js' '*.ts' '*.mjs' '*.ejs' '*.cjs' '.gitlab-ci.yml' 'package.json')
    - FIND_OPTIONS=()
    # loop through the file patterns and add -o -name for each pattern
    - for pattern in "${HASHED_FILES[@]}"; do
        FIND_OPTIONS+=(-o -name "$pattern");
      done
    - FIND_OPTIONS[0]='.'
    # .gitlab-ci.yml from parent directory cannot be specified via `find` above
    - MATCHED_FILES=`find "${FIND_OPTIONS[@]}"`" ../.gitlab-ci.yml"
    - echo $MATCHED_FILES
    # and compute the module hash from $MATCHED_FILES
    - MODULE_HASH=`echo $MATCHED_FILES | xargs sha1sum | sha1sum | awk '{ print $1 }' | cut -c-8` || true
    - CURRENT_HASH=$(gcloud functions describe $ENVIRONMENT_FUNCTION_NAME --region=europe-west1 --format='value(environmentVariables.MODULE_HASH)' || echo 1)
    - if [ "$CURRENT_HASH" == "$MODULE_HASH" ]; then
        echo "Function $ENVIRONMENT_FUNCTION_NAME/$MODULE_HASH is already deployed, skipping deployment.";
        gcloud functions list --regions=$CLOUD_REGION;
        exit;
      else
        echo "Replacing function of $CURRENT_HASH with $MODULE_HASH.";
      fi
    # gen2 functions will require deletion of all previously running services
    # --gen2
    - gcloud beta functions deploy $ENVIRONMENT_FUNCTION_NAME $TRIGGER
        --set-env-vars MODULE_HASH=$MODULE_HASH,$ENVIRONMENT_ENV_VARS,$ENV_VARS
        --set-env-vars=INTERNAL_API_KEY=$INTERNAL_API_KEY
        --set-build-env-vars $BUILD_ENV_VARS
        --region europe-west1
        --memory $MEMORY
        --runtime nodejs18
        --timeout $TIMEOUT
        --max-instances $MAX_INSTANCES
        --entry-point entryPoint
        --service-account=$SERVICE_ACCOUNT
        --allow-unauthenticated
        $ADDITIONAL_PARAMETERS
