.NodeJs/Run/GraphQL/variables:
  variables:
    SERVICE_NAME: graphql
    SENTRY_DSN: https://<EMAIL>/4506059349098496
    ENV_VARS: "HIVE_API_KEY=$HIVE_API_KEY,FEATURE_POSTGRES_USER_SEARCH=true,FEATURE_POSTGRES_POST_SEARCH=true"

NodeJs/Run/GraphQL/build:
  extends:
    - .NodeJs/Run/variables-devel
    - .NodeJs/Run/GraphQL/variables
    - .NodeJs/Run/build-service
  variables:
    NODE_ENV: test
  script:
    - !reference [ .NodeJs/Run/build-service-script, script ]

.NodeJs/Run/GraphQL/deploy:
  script:
    - !reference [ .NodeJs/Run/deploy-script, script ]
    - npx -yes @graphql-hive/cli schema:publish --registry.accessToken $HIVE_API_KEY schema.graphql

NodeJs/Run/GraphQL/deploy-devel:
  stage: deploy-devel
  extends:
    - .NodeJs/Run/deploy-devel
    - .NodeJs/Run/GraphQL/variables
    - .NodeJs/Run/GraphQL/deploy
  needs:
    - NodeJs/Run/GraphQL/build
  variables:
    HIVE_API_KEY: $HIVE_API_DEVEL_KEY

NodeJs/Run/GraphQL/deploy-staging:
  stage: deploy-staging
  extends:
    - .NodeJs/Run/deploy-staging
    - .NodeJs/Run/GraphQL/variables
    - .NodeJs/Run/GraphQL/deploy
  needs:
    - NodeJs/Run/GraphQL/build
  variables:
    HIVE_API_KEY: $HIVE_API_STAGING_KEY

NodeJs/Run/GraphQL/deploy-prod:
  stage: deploy-prod
  extends:
    - .NodeJs/Run/deploy-prod
    - .NodeJs/Run/GraphQL/variables
    - .NodeJs/Run/GraphQL/deploy
  needs:
    - NodeJs/Run/GraphQL/build
  variables:
    THROTTLING: "no"
    HIVE_API_KEY: $HIVE_API_PROD_KEY
    ENV_VARS: "HIVE_API_KEY=$HIVE_API_KEY,FEATURE_POSTGRES_USER_SEARCH=true,FEATURE_POSTGRES_POST_SEARCH=true"
