import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { ExpectedIncomeResponse } from '../generated/api'
import { ExpectedIncomeModel } from '../models/statistics'

export class StatisticsAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getExpectedIncome(userId: string): Promise<ExpectedIncomeModel> {
        return this.get<ExpectedIncomeResponse>(`/v1/users/${userId}/statistics/income/expected`)
    }
}
