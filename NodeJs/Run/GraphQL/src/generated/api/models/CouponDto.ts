/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CouponDtoAttributes } from './CouponDtoAttributes';
import type { CouponDtoRelationships } from './CouponDtoRelationships';
export type CouponDto = {
    id: string;
    attributes: CouponDtoAttributes;
    relationships: CouponDtoRelationships;
    type: string;
};

