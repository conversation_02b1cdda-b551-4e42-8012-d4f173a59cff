/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export enum StripeErrorCode {
    ACCOUNT_ALREADY_EXISTS = 'account_already_exists',
    ACCOUNT_COUNTRY_INVALID_ADDRESS = 'account_country_invalid_address',
    ACCOUNT_INVALID = 'account_invalid',
    ACCOUNT_NUMBER_INVALID = 'account_number_invalid',
    ALIPAY_UPGRADE_REQUIRED = 'alipay_upgrade_required',
    AMOUNT_TOO_SMALL = 'amount_too_small',
    API_KEY_EXPIRED = 'api_key_expired',
    AUTHENTICATION_REQUIRED = 'authentication_required',
    BALANCE_INSUFFICIENT = 'balance_insufficient',
    BANK_ACCOUNT_DECLINED = 'bank_account_declined',
    BANK_ACCOUNT_EXISTS = 'bank_account_exists',
    BANK_ACCOUNT_UNUSABLE = 'bank_account_unusable',
    BANK_ACCOUNT_UNVERIFIED = 'bank_account_unverified',
    BITCOIN_UPGRADE_REQUIRED = 'bitcoin_upgrade_required',
    CARD_DECLINED = 'card_declined',
    CHARGE_ALREADY_CAPTURED = 'charge_already_captured',
    CHARGE_ALREADY_REFUNDED = 'charge_already_refunded',
    CHARGE_DISPUTED = 'charge_disputed',
    CHARGE_EXPIRED_FOR_CAPTURE = 'charge_expired_for_capture',
    COUNTRY_UNSUPPORTED = 'country_unsupported',
    COUPON_EXPIRED = 'coupon_expired',
    CUSTOMER_MAX_SUBSCRIPTIONS = 'customer_max_subscriptions',
    EMAIL_INVALID = 'email_invalid',
    EXPIRED_CARD = 'expired_card',
    IDEMPOTENCY_KEY_IN_USE = 'idempotency_key_in_use',
    INCORRECT_ADDRESS = 'incorrect_address',
    INCORRECT_CVC = 'incorrect_cvc',
    INCORRECT_NUMBER = 'incorrect_number',
    INCORRECT_ZIP = 'incorrect_zip',
    INSTANT_PAYOUTS_UNSUPPORTED = 'instant_payouts_unsupported',
    INVALID_CARD_TYPE = 'invalid_card_type',
    INVALID_CHARGE_AMOUNT = 'invalid_charge_amount',
    INVALID_CVC = 'invalid_cvc',
    INVALID_EXPIRY_MONTH = 'invalid_expiry_month',
    INVALID_EXPIRY_YEAR = 'invalid_expiry_year',
    INVALID_NUMBER = 'invalid_number',
    INVALID_SOURCE_USAGE = 'invalid_source_usage',
    INVOICE_NO_CUSTOMER_LINE_ITEMS = 'invoice_no_customer_line_items',
    INVOICE_NO_SUBSCRIPTION_LINE_ITEMS = 'invoice_no_subscription_line_items',
    INVOICE_NOT_EDITABLE = 'invoice_not_editable',
    INVOICE_UPCOMING_NONE = 'invoice_upcoming_none',
    LIVEMODE_MISMATCH = 'livemode_mismatch',
    MISSING = 'missing',
    NOT_ALLOWED_ON_STANDARD_ACCOUNT = 'not_allowed_on_standard_account',
    ORDER_CREATION_FAILED = 'order_creation_failed',
    ORDER_REQUIRED_SETTINGS = 'order_required_settings',
    ORDER_STATUS_INVALID = 'order_status_invalid',
    ORDER_UPSTREAM_TIMEOUT = 'order_upstream_timeout',
    OUT_OF_INVENTORY = 'out_of_inventory',
    PARAMETER_INVALID_INTEGER = 'parameter_invalid_integer',
    PARAMETER_INVALID_STRING_BLANK = 'parameter_invalid_string_blank',
    PARAMETER_INVALID_STRING_EMPTY = 'parameter_invalid_string_empty',
    PARAMETER_MISSING = 'parameter_missing',
    PARAMETER_UNKNOWN = 'parameter_unknown',
    PAYMENT_INTENT_AUTHENTICATION_FAILURE = 'payment_intent_authentication_failure',
    PAYMENT_INTENT_INCOMPATIBLE_PAYMENT_METHOD = 'payment_intent_incompatible_payment_method',
    PAYMENT_INTENT_INVALID_PARAMETER = 'payment_intent_invalid_parameter',
    PAYMENT_INTENT_PAYMENT_ATTEMPT_FAILED = 'payment_intent_payment_attempt_failed',
    PAYMENT_INTENT_UNEXPECTED_STATE = 'payment_intent_unexpected_state',
    PAYMENT_METHOD_UNACTIVATED = 'payment_method_unactivated',
    PAYMENT_METHOD_UNEXPECTED_STATE = 'payment_method_unexpected_state',
    PAYOUTS_NOT_ALLOWED = 'payouts_not_allowed',
    PLATFORM_API_KEY_EXPIRED = 'platform_api_key_expired',
    POSTAL_CODE_INVALID = 'postal_code_invalid',
    PROCESSING_ERROR = 'processing_error',
    PRODUCT_INACTIVE = 'product_inactive',
    RATE_LIMIT = 'rate_limit',
    RESOURCE_ALREADY_EXISTS = 'resource_already_exists',
    RESOURCE_MISSING = 'resource_missing',
    ROUTING_NUMBER_INVALID = 'routing_number_invalid',
    SECRET_KEY_REQUIRED = 'secret_key_required',
    SEPA_UNSUPPORTED_ACCOUNT = 'sepa_unsupported_account',
    SETUP_ATTEMPT_EXPIRED = 'setup_attempt_expired',
    SETUP_INTENT_AUTHENTICATION_FAILURE = 'setup_intent_authentication_failure',
    SETUP_INTENT_INCOMPATIBLE_PAYMENT_METHOD = 'setup_intent_incompatible_payment_method',
    SETUP_INTENT_INVALID_PARAMETER = 'setup_intent_invalid_parameter',
    SETUP_INTENT_UNEXPECTED_STATE = 'setup_intent_unexpected_state',
    SUBSCRIPTION_ALREADY_CANCELED = 'subscription_already_canceled',
    SUBSCRIPTION_ALREADY_IN_PAST_DUE = 'subscription_already_in_past_due',
    SUBSCRIPTION_CANCELLATION_FAILED = 'subscription_cancellation_failed',
    SUBSCRIPTION_CREATE_FAILED = 'subscription_create_failed',
    SUBSCRIPTION_INACTIVE = 'subscription_inactive',
    SUBSCRIPTION_INCOMPLETE = 'subscription_incomplete',
    SUBSCRIPTION_INVALID_PARAMETER = 'subscription_invalid_parameter',
    SUBSCRIPTION_PAUSE_BEYOND_MAX_PAUSE_DATE = 'subscription_pause_beyond_max_pause_date',
    SUBSCRIPTION_SCHEDULE_CANCELED = 'subscription_schedule_canceled',
    SUBSCRIPTION_SCHEDULE_CREATION_FAILED = 'subscription_schedule_creation_failed',
    SUBSCRIPTION_SCHEDULE_EDIT_MISMATCH = 'subscription_schedule_edit_mismatch',
    SUBSCRIPTION_SCHEDULE_INACTIVE = 'subscription_schedule_inactive',
    SUBSCRIPTION_SCHEDULE_NEVER_ACTIVE = 'subscription_schedule_never_active',
    SUBSCRIPTION_SCHEDULE_NOT_CANCELLABLE = 'subscription_schedule_not_cancellable',
    SUBSCRIPTION_SCHEDULE_START_DATE_IN_PAST = 'subscription_schedule_start_date_in_past',
    SUBSCRIPTION_UPDATE_FAILED = 'subscription_update_failed',
    TAX_ID_INVALID = 'tax_id_invalid',
    TAXES_CALCULATION_FAILED = 'taxes_calculation_failed',
    TESTMODE_CHARGES_ONLY = 'testmode_charges_only',
    THREE_D_SECURE_AUTHENTICATION_REQUIRED = 'three_d_secure_authentication_required',
    TLS_VERSION_UNSUPPORTED = 'tls_version_unsupported',
    TOKEN_ALREADY_USED = 'token_already_used',
    TOKEN_CREATION_FAILED = 'token_creation_failed',
    TOKEN_NOT_FOUND = 'token_not_found',
    TRANSFER_ALREADY_REVERSED = 'transfer_already_reversed',
    TRANSFER_CREATION_FAILED = 'transfer_creation_failed',
    TRANSFER_REVERSAL_NOT_ALLOWED = 'transfer_reversal_not_allowed',
    UNCATEGORIZED = 'uncategorized',
    UNSUPPORTED_CURRENCY = 'unsupported_currency',
    WEBHOOK_ENDPOINT_ERROR = 'webhook_endpoint_error',
    WEBHOOK_SIGNING_SECRET_INCORRECT = 'webhook_signing_secret_incorrect',
}
