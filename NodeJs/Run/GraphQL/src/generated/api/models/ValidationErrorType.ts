/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export enum ValidationErrorType {
    MIN_LENGTH_TWO = 'min_length_two',
    LOWERCASE_ALPHANUMERIC = 'lowercase_alphanumeric',
    ILLEGAL_STRING = 'illegal_string',
    NAME_TAKEN = 'name_taken',
    PATH_CHANGE_TOO_OFTEN = 'path_change_too_often',
    MAX_LENGTH_EXCEEDED = 'max_length_exceeded',
}
