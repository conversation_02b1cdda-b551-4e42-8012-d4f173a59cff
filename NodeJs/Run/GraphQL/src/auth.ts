import { z } from 'zod'

export type UserJwtRole = 'moderator' | 'user'

export function parseAuthJwt(cookieHeader: string | undefined) {
    if (!cookieHeader) {
        return null
    }

    const authTokens = findAuthenticationTokens(cookieHeader)
    const authToken = authTokens.impersonateToken ?? authTokens.accessToken ?? null
    return parseJwt(authToken)
}

function parseJwt(token: string | null) {
    if (token) {
        return Jwt.parse(JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString()))
    } else {
        return null
    }
}

function findAuthenticationTokens(cookieHeader: string): AuthTokens {
    const tokens: AuthTokens = {}
    const cookies = cookieHeader.split('; ')
    for (const cookie of cookies) {
        const [name, value] = cookie.split('=')
        if (name === 'impersonateToken2') {
            tokens.impersonateToken = value
        } else if (name === 'accessToken2') {
            tokens.accessToken = value
        }
    }

    return tokens
}

type AuthTokens = {
    impersonateToken?: string
    accessToken?: string
}

const Jwt = z.object({
    sub: z.string(),
    exp: z.number(),
    ro: z.number(),
})
