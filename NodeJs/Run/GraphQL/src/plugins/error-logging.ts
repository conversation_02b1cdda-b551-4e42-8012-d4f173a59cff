import { ApolloServerPlugin } from '@apollo/server'
import { DataSourceContext } from '../context'
import { logger } from '../common/logger'
import { GraphQLError } from 'graphql/error'

export const apolloLoggerPlugin: ApolloServerPlugin<DataSourceContext> = {
    async unexpectedErrorProcessingRequest({ error }) {
        logger.error(`Unexpected error when processing request: ${JSON.stringify(error)}`)
    },

    async invalidRequestWasReceived({ error }) {
        logger.error(`Received invalid request: ${JSON.stringify(error)}`)
    },

    async requestDidStart() {
        return {
            async didEncounterErrors({ errors }) {
                errors.forEach((error) => {
                    if (shouldLog(error)) {
                        logger.error(`Encountered errors ${JSON.stringify(error)}`, {
                            labels: { path: error.path?.join() },
                        })
                    }
                })
            },
        }
    },
}

function shouldLog(error: GraphQLError) {
    return error.extensions.code !== 'UNAUTHENTICATED'
}
