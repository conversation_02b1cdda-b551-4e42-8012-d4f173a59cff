import { ApolloServerPlugin } from '@apollo/server'
import { DataSourceContext } from '../context'
import * as Sentry from '@sentry/node'
import { getCodeFromExtensions } from '../resolvers/utils'

export const apolloSentryPlugin: ApolloServerPlugin<DataSourceContext> = {
    async requestDidStart() {
        return {
            async didEncounterErrors({ errors, operation, request }) {
                if (!operation) {
                    for (const err of errors) {
                        Sentry.withScope((scope) => {
                            scope.setContext('Query Details', { query: request.query })
                            scope.setContext('Request Headers', Object.fromEntries(request.http?.headers ?? []))
                            scope.update({
                                contexts: {
                                    response: {
                                        headers: {},
                                    },
                                },
                            })
                            Sentry.captureException(err)
                        })
                    }
                    return
                }

                for (const err of errors) {
                    const status = getCodeFromExtensions(err.extensions)
                    if (status && status >= 400 && status < 500) {
                        continue
                    }
                    if (err.message == 'Unauthorized') {
                        continue
                    }
                    Sentry.withScope((scope) => {
                        scope.setTag('kind', operation.operation)
                        scope.setContext('Request Headers', Object.fromEntries(request.http?.headers ?? []))
                        scope.setContext('Query Details', {
                            query: request.query,
                            variables: JSON.stringify(request.variables),
                        })
                        if (err.path) {
                            scope.addBreadcrumb({
                                category: 'query-path',
                                message: err.path.join(' > '),
                                level: 'debug',
                            })
                        }

                        Sentry.captureException(err)
                    })
                }
            },
        }
    },
}
