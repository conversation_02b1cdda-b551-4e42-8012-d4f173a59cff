import {
    LivestreamDetailsResolvers,
    SubscribeRequestState,
    UserDetailsResolvers,
    UserResolvers,
} from '../generated/resolvers-types'
import { GraphQLError } from 'graphql/error'
import { getCodeFromExtensions } from './utils'
import { plainTextToMarkdown } from '../common/util'
import { logger } from '../common/logger'

export const userResolvers: UserResolvers = {
    subscription: async ({ id }, _, { dataSources, user }) => {
        if (!user?.id) {
            return null
        }

        if (user.id === id) {
            return null
        }

        try {
            return await dataSources.subscriptionAPI.getSubscription(user.id, id)
        } catch (err) {
            if (err instanceof GraphQLError) {
                const code = getCodeFromExtensions(err.extensions)
                if (code === 404 || code === 403 || code === 401) {
                    return null
                }
            }

            throw err
        }
    },

    subscriber: async ({ id }, _, { dataSources, user }) => {
        if (!user?.id) {
            return null
        }

        if (user.id === id) {
            return null
        }

        try {
            return await dataSources.subscriptionAPI.getSubscriber(user.id, id)
        } catch (err) {
            if (err instanceof GraphQLError) {
                const code = getCodeFromExtensions(err.extensions)
                if (code === 404 || code === 403 || code === 401) {
                    return null
                }
            }

            throw err
        }
    },

    analytics: ({ analytics }) => {
        return analytics ? analytics : {}
    },

    bioMarkdown: ({ bio }) => {
        return plainTextToMarkdown(bio)
    },

    spotifyShowId: ({ spotifyShowUri }) => {
        if (!spotifyShowUri) return null

        const showId = /^spotify:show:([a-zA-Z0-9]+)$/.exec(spotifyShowUri)

        if (!showId) {
            logger.error(`Failed to parse ${spotifyShowUri}`)
            return null
        }

        return showId[1]
    },

    subscribeRequestState: async ({ id }, _, { dataSources, user }) => {
        if (!user) {
            return null
        }

        if (user.id === id) {
            return null
        }

        const subscribeRequest = await dataSources.subscribeRequestAPI.getSubscribeRequest(id)
        if (!subscribeRequest) {
            return null
        }

        if (subscribeRequest.acceptedAt) {
            return SubscribeRequestState.ACCEPTED
        } else if (subscribeRequest.declinedAt) {
            return SubscribeRequestState.DECLINED
        } else {
            return SubscribeRequestState.PENDING
        }
    },
}

export const userDetailsResolvers: UserDetailsResolvers = {
    bioMarkdown: ({ bio }) => {
        return plainTextToMarkdown(bio)
    },
}

export const livestreamDetailsResolvers: LivestreamDetailsResolvers = {
    playbackUrl: async (parent, _, { dataSources }) => {
        return await dataSources.gjirafaAPI.getPlaybackUrl(parent.publicId)
    },
}
