import { parseAuthJwt } from '../src/auth'

const accessToken =
    '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
const impersonateToken =
    '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
describe('access and impersonate tokens should be parsed from cookie header', () => {
    test('should correctly retrieve access token', () => {
        const parsedUserId = parseAuthJwt(`accessToken2=${accessToken}`)
        expect(parsedUserId).toEqual({ sub: 'eliskaslaharovanojvddav', ro: 0, exp: 1741002873 })
    })
    test('should prioritize impersonate token', () => {
        const parsedUserId = parseAuthJwt(`accessToken2=${accessToken}; impersonateToken2=${impersonateToken}`)
        expect(parsedUserId).toEqual({ sub: 'estermalenovappwftsvc', ro: 1, exp: 1741003452 })
    })
})
