import { fullSubscription, limitedSubscription, subscribeRequest, testContext, user } from './test-utils'
import { resolvers } from '../../src/resolvers'
import { GraphQLResolveInfo } from 'graphql/type'
import { SubscriptionType } from '../../src/generated/resolvers-types'

describe('userSubscriptionDetailsResolvers', () => {
    describe('field: type', () => {
        test('should return SUBSCRIBE_REQUEST when tier id is EUR00', async () => {
            const context = testContext()
            const subscription = fullSubscription({
                tierId: 'EUR00',
            })

            const result = await resolvers.UserSubscriptionDetails!.type!(
                subscription,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscriptionType.SUBSCRIBE_REQUEST)
        })

        test('should return null when tier id is not EUR00', async () => {
            const context = testContext()
            const subscription = fullSubscription({
                tierId: 'EUR05',
            })

            const result = await resolvers.UserSubscriptionDetails!.type!(
                subscription,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscriptionType.STRIPE)
        })

        test('should return null when tier is null', async () => {
            const context = testContext()
            const subscription = {
                ...fullSubscription(),
                tier: null,
            }

            const result = await resolvers.UserSubscriptionDetails!.type!(
                subscription,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscriptionType.STRIPE)
        })

        test('should return null when tier is undefined', async () => {
            const context = testContext()
            const subscription = {
                ...fullSubscription(),
                tier: undefined,
            }

            const result = await resolvers.UserSubscriptionDetails!.type!(
                subscription,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscriptionType.STRIPE)
        })

        test('should return null when tier id is different currency EUR00', async () => {
            const context = testContext()
            const subscription = fullSubscription({
                tierId: 'USD00',
            })

            const result = await resolvers.UserSubscriptionDetails!.type!(
                subscription,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscriptionType.STRIPE)
        })

        test('should return null when tier id is empty string', async () => {
            const context = testContext()
            const subscription = fullSubscription({
                tierId: '',
            })

            const result = await resolvers.UserSubscriptionDetails!.type!(
                subscription,
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(SubscriptionType.STRIPE)
        })
    })
})

describe('userSubscriptionResolvers', () => {
    describe('field: __resolveType', () => {
        test('should return UserSubscriptionDetails when subscriptionModelType is full', () => {
            const subscription = fullSubscription()

            const result = resolvers.UserSubscription!.__resolveType(
                subscription,
                testContext(),
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual('UserSubscriptionDetails')
        })

        test('should return UserSubscriptionInfo when subscriptionModelType is limited', () => {
            const subscription = limitedSubscription()

            const result = resolvers.UserSubscription!.__resolveType(
                subscription,
                testContext(),
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual('UserSubscriptionInfo')
        })
    })
})

describe('subscribeRequestResolvers', () => {
    describe('field: user', () => {
        test('should return user from API', async () => {
            const context = testContext()
            const expectedUser = user('user-id')
            const request = subscribeRequest({ userId: 'user-id' })
            context.dataSources.userAPI.getUser = jest.fn(async () => expectedUser)

            const result = await resolvers.SubscribeRequest!.user!(request, {}, context, {} as GraphQLResolveInfo)

            expect(result).toEqual(expectedUser)
            expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('user-id')
        })
    })
})
