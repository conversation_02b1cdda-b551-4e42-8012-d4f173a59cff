variable "alerts_devel" {
  default = [
    # WARN New alerts must be appended to the end, otherwise causing terrible resources replacement,
    # possibly even getting stuck on blocked resource removal.
    {
      host        = "devel-auth-y7fnkx4lwq-ew.a.run.app"
      path        = "/liveness"
      regions     = ["EUROPE", "USA"]
      contentType = "application/json"
    },
    {
      host        = "devel-image-y7fnkx4lwq-ew.a.run.app"
      path        = "/liveness"
      regions     = ["EUROPE", "USA"]
      contentType = "application/json"
    },
    {
      host        = "devel-api-y7fnkx4lwq-ew.a.run.app"
      path        = "/liveness"
      regions     = ["EUROPE", "USA"]
      contentType = "application/json"
    },
    {
      host        = "main.frontend-apg.pages.dev"
      path        = "/"
      regions     = ["EUROPE", "USA"]
      contentType = "text/html"
    },
  ]
}

variable "alerts_prod" {
  default = [
    # WARN New alerts must be appended to the end, otherwise causing terrible resources replacement,
    # possibly even getting stuck on blocked resource removal.
    {
      host        = "prod-auth-y7fnkx4lwq-ew.a.run.app"
      path        = "/liveness"
      regions     = ["EUROPE", "USA"]
      contentType = "application/json"
    },
    {
      host        = "prod-image-y7fnkx4lwq-ew.a.run.app"
      path        = "/liveness"
      regions     = ["EUROPE", "USA"]
      contentType = "application/json"
    },
    {
      host        = "prod-api-y7fnkx4lwq-ew.a.run.app"
      path        = "/liveness"
      regions     = ["EUROPE", "USA"]
      contentType = "application/json"
    },
    {
      host        = "herohero.co"
      path        = "/"
      regions     = ["EUROPE", "USA"]
      contentType = "text/html"
    },
  ]
}

resource "google_monitoring_notification_channel" "dev_backend_notification_channel" {
  display_name = "Dev/Backend email channel"
  type         = "email"
  labels = {
    email_address = "<EMAIL>"
  }
}

resource "google_monitoring_notification_channel" "dev_frontend_notification_channel" {
  display_name = "Dev/Frontend email channel"
  type         = "email"
  labels = {
    email_address = "<EMAIL>"
  }
}

resource "google_monitoring_uptime_check_config" "uptime_checks_devel" {
  count            = length(var.alerts_devel)
  display_name     = join("", ["Uptime policy for ", var.alerts_devel[count.index].host, var.alerts_devel[count.index].path])
  timeout          = "10s"
  selected_regions = var.alerts_devel[count.index].regions
  period           = "600s"
  http_check {
    path         = var.alerts_devel[count.index].path
    port         = "443"
    mask_headers = false
    use_ssl      = true
    validate_ssl = true
    # TODO: Google Cloud does not yet support matching response content-type,
    #       should use `var.alerts_devel[count.index].contentType`
  }
  monitored_resource {
    type = "uptime_url"
    labels = {
      "host"       = var.alerts_devel[count.index].host
      "project_id" = var.project
    }
  }
}

resource "google_monitoring_uptime_check_config" "uptime_checks_prod" {
  count            = length(var.alerts_prod)
  display_name     = join("", ["Uptime policy for ", var.alerts_prod[count.index].host, var.alerts_prod[count.index].path])
  timeout          = "10s"
  selected_regions = var.alerts_prod[count.index].regions
  period           = "600s"
  http_check {
    path         = var.alerts_prod[count.index].path
    port         = "443"
    mask_headers = false
    use_ssl      = true
    validate_ssl = true
    # TODO: Google Cloud does not yet support matching response content-type,
    #       should use `var.alerts_prod[count.index].contentType`
  }
  monitored_resource {
    type = "uptime_url"
    labels = {
      "host"       = var.alerts_prod[count.index].host
      "project_id" = var.project
    }
  }
}

resource "google_monitoring_alert_policy" "alert_policies_devel" {
  combiner     = "OR"
  display_name = "Uptime alerts devel"
  enabled      = true
  notification_channels = [
    google_monitoring_notification_channel.dev_backend_notification_channel.name,
  ]

  dynamic "conditions" {
    for_each = var.alerts_devel
    content {
      display_name = join("", ["Uptime Alert for ", conditions.value.host, conditions.value.path])
      condition_threshold {
        comparison = "COMPARISON_GT"
        duration   = "60s"
        filter = join("",
          [
            "metric.type=\"monitoring.googleapis.com/uptime_check/check_passed\" resource.type=\"uptime_url\" metric.label.\"check_id\"=\"",
            replace(google_monitoring_uptime_check_config.uptime_checks_devel[conditions.key].name, "projects/heroheroco/uptimeCheckConfigs/", ""),
            "\""
          ]
        )
        threshold_value = 1
        aggregations {
          alignment_period     = "1200s"
          cross_series_reducer = "REDUCE_COUNT_FALSE"
          group_by_fields = [
            "resource.*",
          ]
          per_series_aligner = "ALIGN_NEXT_OLDER"
        }
        trigger {
          count   = 1
          percent = 0
        }
      }
    }
  }
}

resource "google_monitoring_alert_policy" "alert_policies_prod" {
  combiner     = "OR"
  display_name = "Uptime alerts prod"
  enabled      = true
  notification_channels = [
    // normally we notify only backend team, but for production errors it is a good idea
    // to let know also the frontend team so that someone will get to handle it sooner than later
    google_monitoring_notification_channel.dev_backend_notification_channel.name,
    google_monitoring_notification_channel.dev_frontend_notification_channel.name,
  ]

  dynamic "conditions" {
    for_each = var.alerts_prod
    content {
      display_name = join("", ["Uptime Alert for ", conditions.value.host, conditions.value.path])
      condition_threshold {
        comparison = "COMPARISON_GT"
        duration   = "60s"
        filter = join("",
          [
            "metric.type=\"monitoring.googleapis.com/uptime_check/check_passed\" resource.type=\"uptime_url\" metric.label.\"check_id\"=\"",
            replace(google_monitoring_uptime_check_config.uptime_checks_prod[conditions.key].name, "projects/heroheroco/uptimeCheckConfigs/", ""),
            "\""
          ]
        )
        threshold_value = 1
        aggregations {
          alignment_period     = "1200s"
          cross_series_reducer = "REDUCE_COUNT_FALSE"
          group_by_fields = [
            "resource.*",
          ]
          per_series_aligner = "ALIGN_NEXT_OLDER"
        }
        trigger {
          count   = 1
          percent = 0
        }
      }
    }
  }
}

# https://console.cloud.google.com/monitoring/alerting/policies/3615685640645056640?project=heroheroco
resource "google_monitoring_alert_policy" "cloud_logging_threshold" {
  display_name = "Cloud Logging Thresholds"
  combiner     = "OR"
  conditions {
    display_name = "Cloud Run alerts threshold"
    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/alerts_severity\" AND resource.type=\"cloud_run_revision\""
      comparison      = "COMPARISON_GT"
      threshold_value = 20
      duration        = "60s"
      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_SUM"
        cross_series_reducer = "REDUCE_SUM"
      }
    }
  }
  conditions {
    display_name = "Cloud Run logging threshold"

    condition_threshold {
      comparison      = "COMPARISON_GT"
      duration        = "0s"
      filter          = "resource.type = \"cloud_run_revision\" AND metric.type = \"logging.googleapis.com/log_entry_count\""
      threshold_value = 13000

      aggregations {
        alignment_period     = "60s"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields = [
          "resource.label.service_name",
        ]
        per_series_aligner = "ALIGN_SUM"
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }
  conditions {
    display_name = "Cloud Functions alerts threshold"
    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/alerts_severity\" AND resource.type=\"cloud_function\""
      comparison      = "COMPARISON_GT"
      threshold_value = 10
      duration        = "60s"
      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_COUNT"
      }
    }
  }
  conditions {
    display_name = "Cloud Function logging threshold"

    condition_threshold {
      comparison = "COMPARISON_GT"
      duration   = "0s"
      filter = join(
        " AND ",
        [
          "resource.type = \"cloud_function\"",
          "metric.type = \"logging.googleapis.com/log_entry_count\"",
          "resource.labels.function_name != \"prod-post-subscriber-group-notifier\"",
          "resource.labels.function_name != \"prod-mailgun-publisher\"",
          "resource.labels.function_name != \"prod-subscriber-statistics-writer\"",
        ]
      )
      threshold_value = 500
      aggregations {
        alignment_period     = "60s"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields = [
          "resource.label.function_name",
        ]
        per_series_aligner = "ALIGN_SUM"
      }

      trigger {
        count   = 1
        percent = 0
      }
    }
  }
  notification_channels = [
    google_monitoring_notification_channel.dev_backend_notification_channel.name,
  ]
}

resource "google_monitoring_uptime_check_config" "uptime_check_ssr" {
  display_name     = "SSR uptime check"
  timeout          = "60s"
  selected_regions = ["EUROPE", "USA"]
  period           = "900s"
  http_check {
    path         = "/cestmir"
    port         = "443"
    mask_headers = false
    use_ssl      = true
    validate_ssl = true
  }
  content_matchers {
    content = join(
      ".*",
      [
        "<title>Čestmír Strakatý | Herohero</title>",
        "<meta property=\"og:image\" content=\"https://storage.googleapis.com/heroheroco-ogimages",
        "\"@context\":\"https://schema.org\",\"@type\":\"Person\",\"name\":\"Čestmír Strakatý\"",
      ]
    )
    matcher = "MATCHES_REGEX"
  }
  monitored_resource {
    type = "uptime_url"
    labels = {
      "host"       = "herohero.co"
      "project_id" = var.project
    }
  }
}

resource "google_monitoring_alert_policy" "alert_policy_ssr" {
  combiner     = "OR"
  display_name = "SSR uptime check"
  enabled      = true
  notification_channels = [
    google_monitoring_notification_channel.dev_frontend_notification_channel.id,
    google_monitoring_notification_channel.dev_backend_notification_channel.id,
  ]
  conditions {
    display_name = "SSR uptime check"
    condition_threshold {
      comparison = "COMPARISON_GT"
      duration   = "600s"
      filter = join("",
        [
          "metric.type=\"monitoring.googleapis.com/uptime_check/check_passed\" resource.type=\"uptime_url\" metric.label.\"check_id\"=\"",
          replace(google_monitoring_uptime_check_config.uptime_check_ssr.name, "projects/heroheroco/uptimeCheckConfigs/", ""),
          "\""
        ]
      )
      threshold_value = 3
      aggregations {
        alignment_period     = "1200s"
        cross_series_reducer = "REDUCE_COUNT_FALSE"
        group_by_fields = [
          "resource.*",
        ]
        per_series_aligner = "ALIGN_NEXT_OLDER"
      }
      trigger {
        count   = 3
        percent = 0
      }
    }
  }
}

resource "google_logging_metric" "stuck_processing_metric" {
  name        = "stuck-processing-metric"
  description = "Post stuck processing metric"
  filter      = "labels.stuckPosts != ''"

  // Note that we should ideally use value_extractor, however than we cannot perform
  // `max` or `sum` aggregations as it generates histogram in buckets for which
  // alerting is not working as we would expect.
  label_extractors = {
    "stuckPosts" = "REGEXP_EXTRACT(labels.stuckPosts, \"(\\\\d+)\")"
  }

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
    labels {
      key        = "stuckPosts"
      value_type = "INT64"
    }
  }

  timeouts {}
}

resource "google_monitoring_alert_policy" "stuck_processing_alert" {
  display_name = "Stuck posts in processing alert"
  combiner     = "OR"
  severity     = "CRITICAL"

  notification_channels = [
    google_monitoring_notification_channel.dev_backend_notification_channel.name,
  ]

  conditions {
    display_name = "stuck-processing-condition"
    condition_threshold {
      filter = join("",
        [
          "resource.type = \"cloud_function\" AND ",
          "metric.labels.stuckPosts > \"0\" AND ",
          "metric.type = \"logging.googleapis.com/user/",
          google_logging_metric.stuck_processing_metric.id,
          "\"",
        ]
      )
      threshold_value = 0
      duration        = "0s"
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MAX"
      }
      aggregations {
        // the stuck script is run every ten minutes
        alignment_period     = "600s"
        cross_series_reducer = "REDUCE_MAX"
        group_by_fields      = []
        per_series_aligner   = "ALIGN_MAX"
      }
      trigger {
        count   = 1
        percent = 0
      }
    }
  }

  timeouts {}
}

resource "google_logging_metric" "metric_error_webhooks" {
  name        = "metric_error_webhooks"
  description = "Metrics counting errrors on webhooks"
  filter      = "httpRequest.status > 300 AND httpRequest.requestUrl:\"webhook\""
  timeouts {}
}

# WARN: This only notifies about Cloud Run errors (not Cloud Functions)
resource "google_monitoring_alert_policy" "alert_error_webhooks" {
  display_name = "Webhook errors"
  combiner     = "OR"
  severity     = "CRITICAL"

  notification_channels = [
    google_monitoring_notification_channel.dev_backend_notification_channel.name,
  ]

  conditions {
    display_name = "webhook_errors"
    condition_threshold {
      filter = join("",
        [
          "resource.type = \"cloud_run_revision\" AND metric.type = \"logging.googleapis.com/user/",
          google_logging_metric.metric_error_webhooks.id,
          "\"",
        ]
      )
      threshold_value = 20
      duration        = "0s"
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period   = "600s"
        per_series_aligner = "ALIGN_SUM"
      }
      trigger {
        count   = 1
        percent = 0
      }
    }
  }
}
