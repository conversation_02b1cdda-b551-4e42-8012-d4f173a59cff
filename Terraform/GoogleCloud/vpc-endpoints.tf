// See: https://cloud.google.com/vpc/docs/configure-private-service-connect-apis
// To use the endpoint, use DNS `p.googleapis.com`.
// For example, if your endpoint name is xyz, DNS records are created for
// storage-xyz.p.googleapis.com, compute-xyz.p.googleapis.com, and other
// commonly used APIs in the API bundle.
resource "google_compute_global_address" "vpc_endpoint" {
  project      = var.project
  name         = "global-psconnect-ip-prod"
  address_type = "INTERNAL"
  purpose      = "PRIVATE_SERVICE_CONNECT"
  network      = google_compute_network.vpc.id
  address      = "********"
}

resource "google_compute_global_forwarding_rule" "vpc_endpoint" {
  project               = var.project
  name                  = "vpcendpoint" // max 23 characters !!!
  target                = "all-apis"
  network               = google_compute_network.vpc.id
  ip_address            = google_compute_global_address.vpc_endpoint.id
  load_balancing_scheme = ""
}
