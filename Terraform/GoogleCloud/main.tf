terraform {
  backend "gcs" {
    bucket = "heroheroco-terraform-state"
    prefix = "gcloud/state"
  }
}

provider "google" {
  project = var.project
  region  = var.region
  zone    = var.zone
}

provider "google-beta" {
  project = var.project
  region  = var.region
  zone    = var.zone
}

// To start using Terraform, thes APIs must be enabled.
// gcloud services enable cloudresourcemanager.googleapis.com
// gcloud services enable cloudbilling.googleapis.com
// gcloud services enable iam.googleapis.com
// gcloud services enable compute.googleapis.com
// gcloud services enable serviceusage.googleapis.com