variable "gcp-loadbalancer" {
  type    = string
  default = "34.149.29.218"
}

resource "cloudflare_record" "cname_devel_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "devel.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "true"
  content = "main.frontend-apg.pages.dev"
}

resource "cloudflare_record" "cname_devel_firebase_auth" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "auth-devel.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "false"
  content = "devel-heroheroco.firebaseapp.com"
}

resource "cloudflare_record" "cname_prod_firebase_auth" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "auth.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "false"
  content = "prod-heroheroco.firebaseapp.com"
}

resource "cloudflare_record" "a_devel_gtm_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "gtm-devel.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "a_signoz_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "signoz.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = "35.205.69.213"
}

resource "cloudflare_record" "a_l_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "l.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = "35.205.69.213"
}

resource "cloudflare_record" "a_prod_gtm_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "gtm.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "svc_devel_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "svc-devel.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "svc_devel_na_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "svc-devel-na.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "svc_staging_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "svc-staging.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "svc_staging_na_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "svc-staging-na.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "svc_prod_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "svc-prod.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "svc_prod_na_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "svc-prod-na.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "a_mg_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "mg.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "false"
  content = "**************"
}

resource "cloudflare_record" "google_search_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "herohero.co"
  type    = "TXT"
  ttl     = "1"
  proxied = "false"
  content = "google-site-verification=N58C8wWy5UdHyhZhFCJfrNvEiH7GlKk5HlQPTN3SLYA"
}

resource "cloudflare_record" "stripe_saml_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "herohero.co"
  type    = "TXT"
  ttl     = "1"
  proxied = "false"
  content = "stripe-verification=0398da049d72ef7451b3bd22429219ab27d1aac931cd200a61cf20bbc30e206b"
}

resource "cloudflare_record" "cname_staging_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "staging.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "true"
  content = "herohero-ui-staging.pages.dev"
}

resource "cloudflare_record" "cname_prod_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "prod.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "true"
  content = "frontend-apg.pages.dev"
}

resource "cloudflare_record" "cname_about_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "about.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "true"
  content = "frontend-about.pages.dev"
}

resource "cloudflare_record" "cname_help_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "help.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "true"
  content = "us.intercomhelpcenter.com"
}

resource "cloudflare_record" "a_static_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "static.herohero.co"
  type    = "A"
  ttl     = "1"
  proxied = "true"
  content = var.gcp-loadbalancer
}

resource "cloudflare_record" "cname_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "true"
  content = "frontend-apg.pages.dev"
}

resource "cloudflare_record" "CNAME_www_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "www"
  type    = "CNAME"
  ttl     = "1"
  // this must be proxied to allow for www removal on the Cloudflare level
  proxied = "true"
  content = "frontend-apg.pages.dev"
}

resource "cloudflare_record" "CNAME_assets_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "assets"
  type    = "CNAME"
  ttl     = "1"
  proxied = "false"
  content = "heroheroco-assets.b-cdn.net"
}

variable "mx" {
  default = [
    {
      priority = 1
      domain   = "aspmx.l.google.com"
    },
    {
      priority = 5
      domain   = "alt1.aspmx.l.google.com"
    },
    {
      priority = 5
      domain   = "alt2.aspmx.l.google.com"
    },
    {
      priority = 10
      domain   = "alt3.aspmx.l.google.com"
    },
    {
      priority = 10
      domain   = "alt4.aspmx.l.google.com"
    },
  ]
}

resource "cloudflare_record" "mx_herohero_co" {
  count    = length(var.mx)
  zone_id  = cloudflare_zone.herohero_co.id
  name     = cloudflare_zone.herohero_co.zone
  type     = "MX"
  ttl      = "1"
  proxied  = "false"
  priority = var.mx[count.index].priority
  content  = var.mx[count.index].domain
}

// email gmail-client alias
// https://admin.google.com/u/5/ac/accountsettings/customurl
resource "cloudflare_record" "CNAME_mail_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "mail"
  type    = "CNAME"
  ttl     = "1"
  proxied = "false"
  content = "ghs.googlehosted.com"
}

resource "cloudflare_record" "spf_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "herohero.co"
  type    = "TXT"
  content = "v=spf1 include:_spf.google.com ~all"
  proxied = false
  ttl     = 1
}

resource "cloudflare_record" "dkim_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "google._domainkey"
  type    = "TXT"
  content = "v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlWa/aKxXkLGk8mjE3fCYfIv5Bs7gaKSiF7GFTX3E6nMrPDffarTcwf35D3PSNqJpDvlrGeF5RyOjKF5o41IzTdq0ZHcOLU3RO5FZxWHcuyxOFl3UalXTgLODU5gAwIH6toF9ss82EuJ8Ras1Wj2ChFd794xQlrkueTx2esm+wfE3glT9LD6gYchHsJ+DYEDM2rPru0zSJB9WgKiu971Xfj8hLf0rNQ57OHbErrLQ1uispQGk9epgbNWNK54N3WBlleyBa4Tv/hhRpycJ+qo9PAhgw4bQSwv8buJF5kK07iQz2ALRv5QtNH75V1KrhdttNc7SlwW4u4JUPuGnBSzM4wIDAQAB"
  proxied = false
  ttl     = 1
}

resource "cloudflare_record" "intercom_dkim_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "intercom._domainkey"
  type    = "CNAME"
  content = "bfdbf59e-4740-418d-8c2c-5fa6921e0854.dkim.intercom.io"
  proxied = false
  ttl     = 1
}

resource "cloudflare_record" "intercom_outbound_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "outbound.intercom"
  type    = "CNAME"
  content = "rp.herohero.intercom-mail.com"
  proxied = false
  ttl     = 1
}

resource "cloudflare_record" "dmarc_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "_dmarc"
  type    = "TXT"
  ttl     = "1"
  proxied = "false"
  content = "v=DMARC1; p=quarantine; sp=quarantine; rf=afrf; pct=100;"
}

// mailgun
resource "cloudflare_record" "spf_mg_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "mg.herohero.co"
  type    = "TXT"
  ttl     = "1"
  proxied = "false"
  content = "v=spf1 include:eu.mailgun.org ~all"
}
resource "cloudflare_record" "mta_mg_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "mta._domainkey.mg.herohero.co"
  type    = "TXT"
  ttl     = "1"
  proxied = "false"
  content = "k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDKYYxITyPuAD5fSXusVf7jJGAlVpW+lp5i4+DVaNSRT45SgfXm8zmhx34pdNu8uuTxSD0Z9xWmcT3QkYubX+r+tIKSf2jzZC/ouZgKckTQuVSy+IxilvlmOiQJFP6z0W81tWIFVH3zV3z6d2N5OAluqyWUnKnnGhylyOzNoqpgzQIDAQAB"
}
resource "cloudflare_record" "dmarc_mg_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "_dmarc.mg"
  type    = "TXT"
  ttl     = "1"
  proxied = "false"
  content = "v=DMARC1; p=quarantine; sp=quarantine; rf=afrf; pct=100;"
}

variable "mx_mg" {
  default = [
    {
      priority = 10
      domain   = "mxa.eu.mailgun.org"
    },
    {
      priority = 10
      domain   = "mxb.eu.mailgun.org"
    },
  ]
}

resource "cloudflare_record" "mx_mg_herohero_co" {
  count    = length(var.mx)
  zone_id  = cloudflare_zone.herohero_co.id
  name     = "mg.herohero.co"
  type     = "MX"
  ttl      = "1"
  proxied  = "false"
  priority = var.mx[count.index].priority
  content  = var.mx[count.index].domain
}

resource "cloudflare_record" "email_mg_herohero_co" {
  zone_id = cloudflare_zone.herohero_co.id
  name    = "email.mg.herohero.co"
  type    = "CNAME"
  ttl     = "1"
  proxied = "false"
  content = "eu.mailgun.org"
}
