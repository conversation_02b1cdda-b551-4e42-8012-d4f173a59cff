resource "bunnynet_pullzone" "heroheroco_assets" {
  name                          = "heroheroco-assets"
  optimizer_enabled             = true
  optimizer_minify_css          = true
  optimizer_minify_js           = true
  optimizer_smartimage          = true
  optimizer_webp                = true
  permacache_storagezone        = 147985
  block_post_requests           = true
  block_root_path               = true
  allow_referers                = []
  cache_expiration_time         = ********
  cache_expiration_time_browser = ********
  block_ips                     = []
  safehop_enabled               = false
  origin {
    url  = "https://heroheroco-assets.storage.googleapis.com"
    type = "OriginUrl"
  }
  routing {
    tier = "Volume"
  }
  cache_vary = [
    "querystring",
    "webp",
  ]
  cors_extensions = [
    "css",
    "eot",
    "ts",
    "ttf",
    "woff",
    "woff2",
    "gif",
    "jpeg",
    "jpg",
    "js",
    "mp3",
    "mp4",
    "mpeg",
    "png",
    "svg",
    "webm",
    "webp",
  ]
}

resource "bunnynet_storage_zone" "heroheroco_assets" {
  name      = "heroheroco-assets"
  region    = "DE"
  zone_tier = "Standard"
  replication_regions = [
    "NY",
  ]
}
